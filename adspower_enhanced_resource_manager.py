#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AdsPower增强资源管理器
解决配置文件无法从AdsPower应用列表中完全移除的问题

修复要点：
1. 正确实现两步骤清理：停止浏览器 + 删除配置文件
2. 处理"User_id is not open"错误 - 这是正常状态
3. 即使停止失败也要尝试删除配置文件
4. 增加重试机制和验证机制
5. 保护智能答题功能不受影响
"""

import asyncio
import logging
import requests
from typing import Dict, Optional
import time

logger = logging.getLogger(__name__)

class EnhancedAdsPowerResourceManager:
    """增强版AdsPower资源管理器"""
    
    def __init__(self, adspower_host: str = "http://local.adspower.net:50325"):
        self.adspower_host = adspower_host
        self.adspower_base_url = f"{adspower_host}/api/v1"
        self.logger = logger
    
    async def complete_cleanup_adspower_profile(self, profile_id: str, persona_name: str = "未知") -> Dict:
        """
        完全清理AdsPower配置文件 - 两步骤标准流程
        
        确保配置文件从AdsPower应用环境列表中完全移除，释放浏览器额度
        """
        try:
            self.logger.info(f"🚀 开始AdsPower资源两步骤清理流程")
            self.logger.info(f"   目标: {persona_name} (配置文件ID: {profile_id})")
            self.logger.info(f"   目标: 从AdsPower应用列表中完全移除配置文件")
            
            # ✨ 标准两步骤清理流程
            self.logger.info("📍 执行AdsPower官方推荐的两步骤资源释放：")
            self.logger.info("   第一步：停止浏览器实例")
            self.logger.info("   第二步：删除配置文件（从AdsPower列表移除）")
            
            # 第一步：停止浏览器实例
            stop_success = await self._stop_browser_enhanced(profile_id)
            
            # 第二步：删除配置文件（关键步骤）
            # 注意：即使停止失败也要执行此步骤
            if stop_success:
                self.logger.info("✅ 浏览器停止成功，等待2秒后执行删除...")
                await asyncio.sleep(2)
                delete_success = await self._delete_profile_enhanced(profile_id)
            else:
                self.logger.warning("⚠️ 浏览器停止失败，但仍会尝试删除配置文件")
                self.logger.info("ℹ️ 浏览器可能已被手动关闭，直接执行配置文件删除")
                await asyncio.sleep(1)
                delete_success = await self._delete_profile_enhanced(profile_id)
            
            # 评估清理结果
            result = {
                "success": delete_success,  # 以配置文件删除结果为准
                "profile_id": profile_id,
                "persona_name": persona_name,
                "browser_stopped": stop_success,
                "profile_deleted": delete_success,
                "fully_released": delete_success,
                "method": "two_step_cleanup"
            }
            
            if result["success"]:
                self.logger.info("✅ AdsPower资源两步骤清理完全成功")
                self.logger.info("🎯 配置文件已从AdsPower应用列表中完全移除")
                self.logger.info("💾 浏览器额度已释放，可创建新的配置文件")
            else:
                self.logger.warning("⚠️ AdsPower资源清理失败")
                if not delete_success:
                    self.logger.error("❌ 配置文件删除失败，资源可能仍占用AdsPower额度")
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ AdsPower资源清理严重异常: {e}")
            return {
                "success": False,
                "profile_id": profile_id,
                "persona_name": persona_name,
                "browser_stopped": False,
                "profile_deleted": False,
                "fully_released": False,
                "error": str(e),
                "method": "two_step_cleanup"
            }
    
    async def _stop_browser_enhanced(self, profile_id: str) -> bool:
        """第一步：增强版停止AdsPower浏览器实例"""
        try:
            self.logger.info(f"⏹️ 第一步：停止AdsPower浏览器实例 {profile_id}")
            
            url = f"{self.adspower_base_url}/browser/stop"
            params = {"user_id": profile_id}
            
            self.logger.info(f"🔗 API请求URL: {url}")
            self.logger.info(f"📊 请求参数: {params}")
            
            response = requests.get(url, params=params, timeout=15)
            
            self.logger.info(f"📊 API响应状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                self.logger.info(f"📋 API响应内容: {result}")
                
                if result.get("code") == 0:
                    self.logger.info("✅ 浏览器实例停止成功")
                    return True
                elif result.get("code") == -1 and "User_id is not open" in result.get("msg", ""):
                    # 🔑 关键修复：这不是错误，是正常状态
                    self.logger.info("ℹ️ 浏览器实例已处于关闭状态（正常状态）")
                    self.logger.info("ℹ️ 'User_id is not open' 表示浏览器已关闭，可以继续删除配置文件")
                    return True
                else:
                    self.logger.warning(f"⚠️ 浏览器停止API响应异常: {result.get('msg', '未知错误')}")
                    return False
            else:
                self.logger.warning(f"⚠️ 浏览器停止API请求失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.warning(f"⚠️ 停止浏览器异常: {e}")
            self.logger.info("ℹ️ 浏览器可能已被手动关闭，这不影响配置文件删除")
            return False
    
    async def _delete_profile_enhanced(self, profile_id: str) -> bool:
        """第二步：增强版删除AdsPower配置文件"""
        try:
            self.logger.info(f"🗑️ 第二步：删除AdsPower配置文件 {profile_id}")
            self.logger.info("🎯 目标：从AdsPower应用环境列表中完全移除配置文件")
            
            # 构建API URL - 根据AdsPower官方文档
            url = f"{self.adspower_base_url}/user/delete"
            data = {"user_ids": [profile_id]}
            
            self.logger.info(f"🔗 删除API请求URL: {url}")
            self.logger.info(f"📊 请求数据: {data}")
            
            # 增强重试机制
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    self.logger.info(f"🔄 执行删除尝试 {attempt + 1}/{max_retries}")
                    
                    response = requests.post(url, json=data, timeout=15)
                    self.logger.info(f"📊 删除API响应状态: {response.status_code}")
                    
                    if response.status_code == 200:
                        result = response.json()
                        self.logger.info(f"📋 删除API响应内容: {result}")
                        
                        if result.get("code") == 0:
                            self.logger.info("✅ AdsPower配置文件删除成功")
                            self.logger.info("🎯 配置文件已从AdsPower应用环境列表中完全移除")
                            self.logger.info("💾 浏览器额度已释放")
                            
                            # 验证删除结果
                            await self._verify_profile_deletion(profile_id)
                            return True
                        else:
                            error_msg = result.get('msg', '未知错误')
                            self.logger.warning(f"⚠️ 配置文件删除失败: {error_msg}")
                            
                            # 某些错误可以重试
                            if any(keyword in error_msg.lower() for keyword in ["busy", "in use", "locked"]):
                                if attempt < max_retries - 1:
                                    wait_time = 2 * (attempt + 1)  # 递增等待时间
                                    self.logger.info(f"🔄 资源忙碌，{wait_time}秒后重试")
                                    await asyncio.sleep(wait_time)
                                    continue
                            return False
                    else:
                        self.logger.warning(f"⚠️ API请求失败，状态码: {response.status_code}")
                        response_text = response.text[:200]  # 限制日志长度
                        self.logger.warning(f"⚠️ 响应内容: {response_text}")
                        
                        if attempt < max_retries - 1:
                            wait_time = 2 * (attempt + 1)
                            self.logger.info(f"🔄 网络异常，{wait_time}秒后重试")
                            await asyncio.sleep(wait_time)
                            continue
                        return False
                        
                except requests.exceptions.Timeout:
                    if attempt < max_retries - 1:
                        wait_time = 3 * (attempt + 1)
                        self.logger.warning(f"⚠️ 请求超时，{wait_time}秒后重试 (第{attempt + 1}/{max_retries}次)")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        self.logger.error("❌ 删除API请求多次超时失败")
                        return False
                        
                except requests.exceptions.RequestException as e:
                    if attempt < max_retries - 1:
                        wait_time = 2 * (attempt + 1)
                        self.logger.warning(f"⚠️ 网络异常: {e}，{wait_time}秒后重试")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        self.logger.error(f"❌ 删除API网络异常: {e}")
                        return False
            
            self.logger.error("❌ 配置文件删除最终失败")
            return False
                
        except Exception as e:
            self.logger.error(f"❌ 删除配置文件严重异常: {e}")
            return False
    
    async def _verify_profile_deletion(self, profile_id: str):
        """验证配置文件是否真正从AdsPower列表中删除"""
        try:
            self.logger.info(f"🔍 验证配置文件删除结果: {profile_id}")
            
            # 查询所有配置文件
            query_url = f"{self.adspower_base_url}/user/list"
            
            response = requests.get(query_url, timeout=10)
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    profiles = result.get("data", {}).get("list", [])
                    
                    # 检查目标配置文件是否还在列表中
                    found = any(profile.get("user_id") == profile_id for profile in profiles)
                    
                    if not found:
                        self.logger.info("✅ 验证成功：配置文件已从AdsPower列表中完全消失")
                        return True
                    else:
                        self.logger.warning("⚠️ 验证失败：配置文件仍在AdsPower列表中")
                        return False
                else:
                    self.logger.warning("⚠️ 无法验证删除结果：查询API异常")
                    return None
            else:
                self.logger.warning("⚠️ 无法验证删除结果：查询请求失败")
                return None
                
        except Exception as e:
            self.logger.warning(f"⚠️ 验证删除结果时出现异常: {e}")
            return None

# 创建全局实例
enhanced_adspower_manager = EnhancedAdsPowerResourceManager()

async def test_enhanced_cleanup():
    """测试增强清理功能"""
    manager = EnhancedAdsPowerResourceManager()
    
    # 示例测试
    test_profile_id = "k10lqo46"  # 替换为实际的profile ID
    
    result = await manager.complete_cleanup_adspower_profile(
        profile_id=test_profile_id,
        persona_name="测试清理"
    )
    
    print("清理结果:", result)

if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_enhanced_cleanup())
