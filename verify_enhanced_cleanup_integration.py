#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证增强版AdsPower资源清理集成是否正常工作
"""

import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def verify_integration():
    """验证集成状态"""
    
    logger.info("🔍 开始验证增强版AdsPower资源清理集成...")
    
    try:
        # 1. 测试主集成模块导入
        logger.info("📋 1. 测试主集成模块导入...")
        import adspower_browser_use_integration
        logger.info("✅ 主集成模块导入成功")
        
        # 2. 测试增强版资源管理器导入
        logger.info("📋 2. 测试增强版资源管理器导入...")
        from adspower_enhanced_resource_manager import EnhancedAdsPowerResourceManager
        logger.info("✅ 增强版资源管理器导入成功")
        
        # 3. 测试集成补丁导入
        logger.info("📋 3. 测试集成补丁导入...")
        import enhanced_resource_cleanup_integration
        logger.info("✅ 集成补丁导入成功")
        
        # 4. 验证增强版管理器实例化
        logger.info("📋 4. 验证增强版管理器实例化...")
        manager = EnhancedAdsPowerResourceManager()
        logger.info("✅ 增强版管理器实例化成功")
        
        # 5. 检查核心方法是否存在
        logger.info("�� 5. 检查核心方法是否存在...")
        methods_to_check = [
            'complete_cleanup_adspower_profile',
            '_stop_browser_enhanced',
            '_delete_profile_enhanced',
            '_verify_profile_deletion'
        ]
        
        for method_name in methods_to_check:
            if hasattr(manager, method_name):
                logger.info(f"   ✅ {method_name} 方法存在")
            else:
                logger.error(f"   ❌ {method_name} 方法缺失")
                return False
        
        # 6. 验证AdsPowerResourceManager是否被增强
        logger.info("📋 6. 验证AdsPowerResourceManager是否被增强...")
        if hasattr(adspower_browser_use_integration, 'AdsPowerResourceManager'):
            original_manager_class = adspower_browser_use_integration.AdsPowerResourceManager
            
            # 检查是否有cleanup_adspower_resources方法
            if hasattr(original_manager_class, 'cleanup_adspower_resources'):
                logger.info("   ✅ AdsPowerResourceManager.cleanup_adspower_resources 方法存在")
            else:
                logger.warning("   ⚠️ AdsPowerResourceManager.cleanup_adspower_resources 方法不存在")
            
            logger.info("✅ AdsPowerResourceManager集成验证完成")
        else:
            logger.warning("⚠️ AdsPowerResourceManager类未找到")
        
        # 7. 验证导入状态变量
        logger.info("📋 7. 验证导入状态变量...")
        if hasattr(adspower_browser_use_integration, 'enhanced_cleanup_available'):
            status = adspower_browser_use_integration.enhanced_cleanup_available
            logger.info(f"   enhanced_cleanup_available: {status}")
            if status:
                logger.info("   ✅ 增强版清理功能可用")
            else:
                logger.warning("   ⚠️ 增强版清理功能不可用")
        
        logger.info("\n🎉 所有验证项目完成！")
        logger.info("✅ 增强版AdsPower资源清理集成验证成功")
        logger.info("\n📋 功能总结：")
        logger.info("   🔧 两步骤资源清理流程")
        logger.info("   🔧 正确处理'User_id is not open'状态")
        logger.info("   🔧 配置文件从AdsPower列表完全移除")
        logger.info("   🔧 智能答题功能保护")
        logger.info("   🔧 重试机制和验证机制")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ 验证过程中出现异常: {e}")
        return False

if __name__ == "__main__":
    success = verify_integration()
    if success:
        print("\n🎯 集成验证成功！系统已准备好使用增强版AdsPower资源清理功能。")
    else:
        print("\n❌ 集成验证失败！请检查相关文件是否正确安装。")
