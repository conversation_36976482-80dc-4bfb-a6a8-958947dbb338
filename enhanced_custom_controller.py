# ⚠️ 注意：此文件的功能已被src/controller/custom_controller.py中的终极智能点击功能替代
# 为避免冲突，此文件的click_element_by_index相关功能已被禁用

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
🔥 增强版CustomController - 集成所有智能题型处理引擎
确保所有题型都能被正确识别和处理
"""

import asyncio
import logging
import random
import time
from typing import Dict, Any, Optional, List, Union, Callable, Awaitable, Type
from pydantic import BaseModel

# WebUI核心导入
from src.controller.custom_controller import CustomController
from browser_use.browser.context import BrowserContext
from browser_use.agent.views import ActionResult
from browser_use.controller.service import ActionModel

logger = logging.getLogger(__name__)

class EnhancedCustomController(CustomController):
    """🔥 增强版CustomController - 集成所有智能引擎的超级控制器"""
    
    def __init__(self, exclude_actions: list[str] = [],
                 output_model: Optional[Type[BaseModel]] = None,
                 ask_assistant_callback: Optional[Union[Callable[[str, BrowserContext], Dict[str, Any]], Callable[
                     [str, BrowserContext], Awaitable[Dict[str, Any]]]]] = None,
                 ):
        # 🔥 继承父类的所有功能
        super().__init__(exclude_actions, output_model, ask_assistant_callback)
        
        # 🔥 重新初始化智能引擎（确保加载）
        self._force_initialize_all_intelligent_engines()
        
        # 🔥 注册增强版智能选择系统
        self._register_enhanced_intelligent_selection_system()
        
        logger.info("✅ EnhancedCustomController已激活 - 所有智能引擎已强制集成")

    def _force_initialize_all_intelligent_engines(self):
        """🧠 强制初始化所有智能处理引擎 - 确保100%加载成功"""
        logger.info("🔥 开始强制初始化所有智能引擎...")
        
        # 智能引擎状态
        self.engine_status = {
            'nationality_engine': False,
            'radio_handler': False,
            'drag_drop_engine': False,
            'enhanced_radio_system': False,
            'conservative_radio_handler': False  # 新增保守版本
        }
        
        # 1. 智能国籍区域选择引擎
        for module_name in ['intelligent_nationality_region_engine', 'IntelligentNationalityRegionEngine']:
            try:
                if module_name == 'intelligent_nationality_region_engine':
                    from intelligent_nationality_region_engine import IntelligentNationalityRegionEngine
                    self.nationality_engine = IntelligentNationalityRegionEngine()
                    self.engine_status['nationality_engine'] = True
                    logger.info("✅ 智能国籍区域选择引擎已加载")
                    break
            except (ImportError, AttributeError) as e:
                logger.debug(f"⚠️ 尝试加载 {module_name} 失败: {e}")
                continue
        
        if not self.engine_status['nationality_engine']:
            self.nationality_engine = None
            logger.warning("⚠️ 智能国籍区域选择引擎不可用")
        
        # 2. 自定义单选按钮处理器（多版本兼容）
        for module_name in ['custom_radio_button_handler', 'enhanced_custom_radio_system', 'conservative_custom_radio_handler']:
            try:
                if module_name == 'custom_radio_button_handler':
                    from custom_radio_button_handler import CustomRadioButtonHandler
                    self.radio_handler = CustomRadioButtonHandler()
                    self.engine_status['radio_handler'] = True
                    logger.info("✅ 自定义单选按钮处理器已加载")
                elif module_name == 'enhanced_custom_radio_system':
                    from enhanced_custom_radio_system import EnhancedCustomRadioSystem
                    self.enhanced_radio_system = EnhancedCustomRadioSystem()
                    self.engine_status['enhanced_radio_system'] = True
                    logger.info("✅ 增强自定义Radio系统已加载")
                elif module_name == 'conservative_custom_radio_handler':
                    from conservative_custom_radio_handler import ConservativeCustomRadioHandler
                    self.conservative_radio_handler = ConservativeCustomRadioHandler()
                    self.engine_status['conservative_radio_handler'] = True
                    logger.info("✅ 保守版自定义Radio处理器已加载")
            except (ImportError, AttributeError) as e:
                logger.debug(f"⚠️ 尝试加载 {module_name} 失败: {e}")
                continue
        
        # 设置默认值
        if not self.engine_status['radio_handler']:
            self.radio_handler = None
        if not self.engine_status['enhanced_radio_system']:
            self.enhanced_radio_system = None
        if not self.engine_status['conservative_radio_handler']:
            self.conservative_radio_handler = None
        
        # 3. 智能拖拽排序引擎
        try:
            from intelligent_drag_drop_ranking_engine import IntelligentDragDropRankingEngine
            self.drag_drop_engine = IntelligentDragDropRankingEngine()
            self.engine_status['drag_drop_engine'] = True
            logger.info("✅ 智能拖拽排序引擎已加载")
        except (ImportError, AttributeError) as e:
            self.drag_drop_engine = None
            logger.warning(f"⚠️ 智能拖拽排序引擎不可用: {e}")
        
        # 统计结果
        active_engines = sum(self.engine_status.values())
        total_engines = len(self.engine_status)
        logger.info(f"🧠 智能引擎强制初始化完成: {active_engines}/{total_engines} 个引擎可用")
        logger.info(f"📊 详细引擎状态: {self.engine_status}")

    def _register_enhanced_intelligent_selection_system(self):
        """🎯 注册增强版智能选择系统"""
        
        @self.registry.action(
            'Enhanced intelligent question type handler with all engines - ultimate solution',
        )
        async def ultimate_intelligent_question_handler(
            index: int, 
            browser: BrowserContext,
            force_intelligent: bool = True
        ) -> ActionResult:
            """🎯 终极智能题型处理器 - 覆盖所有题型"""
            try:
                # 获取元素信息
                selector_map = await browser.get_selector_map()
                if index not in selector_map:
                    return ActionResult(error=f"Element index {index} not found")
                
                dom_element = selector_map[index]
                element_text = getattr(dom_element, 'text', '') or ''
                element_tag = getattr(dom_element, 'tag_name', '')
                page = await browser.get_current_page()
                
                logger.info(f"🎯 终极智能处理器启动 - 元素: '{element_text}' 标签: '{element_tag}'")
                
                # ============= 题型优先级处理 =============
                
                # 🗺️ 优先级1: 国家/地区选择 - 当前最高优先级问题
                if self._is_country_selection_element(element_text):
                    logger.info(f"🗺️ 检测到国家选择题型，使用最高优先级处理")
                    result = await self._handle_country_selection_with_all_engines(
                        browser, page, dom_element, element_text, index
                    )
                    if result.extracted_content and "成功" in result.extracted_content:
                        return result
                
                # 🎯 优先级2: 自定义Radio按钮
                if await self._is_any_custom_radio_element(page, dom_element, element_text):
                    logger.info(f"🎯 检测到自定义Radio题型")
                    result = await self._handle_custom_radio_with_all_engines(
                        browser, page, dom_element, element_text, index
                    )
                    if result.extracted_content and "成功" in result.extracted_content:
                        return result
                
                # 🔀 优先级3: 拖拽排序
                if await self._is_drag_drop_element_enhanced(page, dom_element):
                    logger.info(f"🔀 检测到拖拽排序题型")
                    return await self._handle_drag_drop_with_all_engines(
                        browser, page, dom_element, element_text
                    )
                
                # 📋 优先级4: 下拉选择
                if self._is_dropdown_element(element_tag, element_text):
                    logger.info(f"📋 检测到下拉选择题型")
                    result = await self._handle_dropdown_with_intelligence(
                        browser, page, dom_element, element_text, index
                    )
                    if result.extracted_content and "成功" in result.extracted_content:
                        return result
                
                # 🎯 优先级5: 通用智能选择
                logger.info(f"🎯 使用通用智能选择处理")
                return await self._handle_general_selection_enhanced(
                    browser, page, dom_element, element_text, index
                )
                
            except Exception as e:
                logger.error(f"❌ 终极智能处理失败: {e}")
                return await self._safe_fallback_click(browser, index, element_text)
        
        # 🔥 重写click_element_by_index，确保所有点击都经过增强处理
        @self.registry.action(
            'Overridden click_element_by_index with ultimate intelligence',
        )
        async def overridden_click_element_by_index(
            index: int, 
            browser: BrowserContext
        ) -> ActionResult:
            """🔥 重写的click_element_by_index - 确保100%智能处理"""
            return await ultimate_intelligent_question_handler(index, browser, force_intelligent=True)

    # ============= 增强版题型检测方法 =============

    def _is_country_selection_element(self, element_text: str) -> bool:
        """🗺️ 增强版国家选择检测"""
        country_keywords = [
            # 中文关键词
            "中国", "中华", "大陆", "内地", "华人", "汉族",
            "菲律宾", "美国", "日本", "韩国", "澳大利亚", "加拿大", "英国", "法国", "德国",
            "国家", "国籍", "地区", "区域", "居住地", "出生地",
            # 英文关键词
            "china", "chinese", "mainland", "philippines", "usa", "america", "japan", "korea",
            "australia", "canada", "uk", "france", "germany", "country", "nationality", 
            "region", "residence", "birthplace", "homeland"
        ]
        
        # 增强检测：检查是否包含多个国家关键词
        text_lower = element_text.lower()
        matches = sum(1 for keyword in country_keywords if keyword in text_lower)
        
        # 如果包含2个以上关键词，几乎确定是国家选择
        if matches >= 2:
            logger.info(f"🗺️ 强匹配国家选择元素: {element_text} (匹配{matches}个关键词)")
            return True
        
        # 单个关键词也可能是国家选择
        is_country = any(keyword in text_lower for keyword in country_keywords)
        if is_country:
            logger.info(f"🗺️ 弱匹配国家选择元素: {element_text}")
        
        return is_country

    async def _is_any_custom_radio_element(self, page, dom_element, element_text: str) -> bool:
        """🎯 使用所有可用引擎检测自定义Radio"""
        # 尝试使用增强版系统
        if hasattr(self, 'enhanced_radio_system') and self.enhanced_radio_system:
            try:
                analysis = await self.enhanced_radio_system.analyze_element_comprehensively_v2(
                    page, dom_element, 0, element_text, getattr(dom_element, 'tag_name', '')
                )
                if analysis.get("is_custom_radio", False):
                    return True
            except Exception as e:
                logger.debug(f"⚠️ 增强Radio检测失败: {e}")
        
        # 尝试使用保守版系统
        if hasattr(self, 'conservative_radio_handler') and self.conservative_radio_handler:
            try:
                is_custom = await self.conservative_radio_handler.is_custom_radio_element(
                    page, dom_element
                )
                if is_custom:
                    return True
            except Exception as e:
                logger.debug(f"⚠️ 保守Radio检测失败: {e}")
        
        # 基础检测
        return self._basic_radio_detection(dom_element, element_text)

    def _basic_radio_detection(self, dom_element, element_text: str) -> bool:
        """基础Radio检测"""
        # 检查标签类型
        tag_name = getattr(dom_element, 'tag_name', '').lower()
        if tag_name in ['input'] and 'radio' in str(getattr(dom_element, 'attributes', {})).lower():
            return True
        
        # 检查class和其他属性
        class_name = getattr(dom_element, 'class_name', '') or ''
        if any(keyword in class_name.lower() for keyword in ['radio', 'option', 'choice']):
            return True
        
        return False

    async def _is_drag_drop_element_enhanced(self, page, dom_element) -> bool:
        """🔀 增强版拖拽检测"""
        if hasattr(self, 'drag_drop_engine') and self.drag_drop_engine:
            try:
                analysis = await self.drag_drop_engine.analyze_drag_drop_question_type(
                    page, self.digital_human_info or {}
                )
                return analysis.get("question_type") != "none"
            except Exception as e:
                logger.debug(f"⚠️ 拖拽引擎检测失败: {e}")
        
        # 基础拖拽检测
        element_class = getattr(dom_element, 'class_name', '') or ''
        element_attributes = getattr(dom_element, 'attributes', {})
        
        drag_keywords = ['draggable', 'sortable', 'drag', 'drop', 'reorder']
        return any(keyword in element_class.lower() for keyword in drag_keywords) or \
               any(keyword in str(element_attributes).lower() for keyword in drag_keywords)

    def _is_dropdown_element(self, element_tag: str, element_text: str) -> bool:
        """📋 下拉元素检测"""
        return element_tag.lower() == 'select' or \
               'select' in element_text.lower() or \
               '下拉' in element_text or \
               'dropdown' in element_text.lower()

    # ============= 增强版题型处理方法 =============

    async def _handle_country_selection_with_all_engines(
        self, browser: BrowserContext, page, dom_element, element_text: str, index: int
    ) -> ActionResult:
        """🗺️ 使用所有可用引擎处理国家选择"""
        
        # 方案1: 使用智能国籍引擎
        if hasattr(self, 'nationality_engine') and self.nationality_engine and self.digital_human_info:
            try:
                logger.info(f"🗺️ 方案1: 使用智能国籍引擎")
                
                # 确定目标国籍
                target_nationality = self._determine_target_nationality()
                
                result = await self.nationality_engine.handle_nationality_selection_page(
                    browser, target_nationality=target_nationality
                )
                
                if result.get('success'):
                    return ActionResult(
                        extracted_content=f"✅ 智能国籍引擎成功: {result.get('selected_option', '未知')}"
                    )
                else:
                    logger.warning(f"⚠️ 智能国籍引擎失败: {result.get('error', '未知')}")
            except Exception as e:
                logger.warning(f"⚠️ 智能国籍引擎异常: {e}")
        
        # 方案2: 使用智能选项发现引擎
        if hasattr(self, 'digital_human_info') and self.digital_human_info:
            try:
                logger.info(f"🗺️ 方案2: 使用智能选项发现引擎")
                
                discovery_result = await self.intelligent_option_discovery_engine(
                    page, self.digital_human_info,
                    target_question_context="nationality_selection",
                    search_scope="country_language"
                )
                
                if discovery_result.get('success') and discovery_result.get('recommended_option'):
                    recommended = discovery_result['recommended_option']
                    
                    # 尝试点击推荐选项
                    try:
                        await page.click(f'text="{recommended.get("text", "")}"')
                        return ActionResult(
                            extracted_content=f"✅ 选项发现引擎成功: {recommended.get('text', '未知')}"
                        )
                    except Exception as click_error:
                        logger.warning(f"⚠️ 推荐选项点击失败: {click_error}")
            except Exception as e:
                logger.warning(f"⚠️ 选项发现引擎异常: {e}")
        
        # 方案3: 智能决策机制
        if hasattr(self, 'digital_human_info') and self.digital_human_info:
            try:
                logger.info(f"🗺️ 方案3: 使用智能决策机制")
                
                decision_result = await self._make_intelligent_selection_decision(
                    element_text, index, browser, self.digital_human_info
                )
                
                if decision_result.get("should_override"):
                    # 寻找更好的选项
                    correct_choice_result = await self._find_and_click_correct_option(
                        decision_result['recommended_choice'], browser
                    )
                    
                    if correct_choice_result.get("success"):
                        return ActionResult(
                            extracted_content=f"✅ 智能决策成功: {decision_result['recommended_choice']}"
                        )
            except Exception as e:
                logger.warning(f"⚠️ 智能决策异常: {e}")
        
        # 方案4: 标准回退
        logger.info(f"🗺️ 方案4: 使用标准回退")
        return await self._safe_fallback_click(browser, index, element_text)

    def _determine_target_nationality(self) -> str:
        """确定目标国籍"""
        if not self.digital_human_info:
            return "中国"
        
        # 从residence和location字段推断
        residence = str(self.digital_human_info.get('residence', ''))
        location = str(self.digital_human_info.get('location', ''))
        combined = (residence + location).lower()
        
        if any(keyword in combined for keyword in ['中国', 'china', '北京', '上海', '深圳', '广州']):
            return "中国"
        elif any(keyword in combined for keyword in ['美国', 'usa', 'america']):
            return "美国"
        elif any(keyword in combined for keyword in ['日本', 'japan']):
            return "日本"
        
        return "中国"  # 默认

    async def _handle_custom_radio_with_all_engines(
        self, browser: BrowserContext, page, dom_element, element_text: str, index: int
    ) -> ActionResult:
        """🎯 使用所有可用Radio引擎处理"""
        
        # 方案1: 增强Radio系统
        if hasattr(self, 'enhanced_radio_system') and self.enhanced_radio_system:
            try:
                logger.info(f"🎯 方案1: 使用增强Radio系统")
                result = await self.enhanced_radio_system.handle_enhanced_custom_radio_selection(
                    page, dom_element, index, element_text, self.digital_human_info or {}
                )
                if result.get('success'):
                    return ActionResult(
                        extracted_content=f"✅ 增强Radio系统成功: {element_text}"
                    )
            except Exception as e:
                logger.warning(f"⚠️ 增强Radio系统异常: {e}")
        
        # 方案2: 基础Radio处理器
        if hasattr(self, 'radio_handler') and self.radio_handler:
            try:
                logger.info(f"🎯 方案2: 使用基础Radio处理器")
                result = await self.radio_handler.handle_radio_click(
                    page, dom_element, index, element_text
                )
                if result.get('success'):
                    return ActionResult(
                        extracted_content=f"✅ 基础Radio处理器成功: {element_text}"
                    )
            except Exception as e:
                logger.warning(f"⚠️ 基础Radio处理器异常: {e}")
        
        # 方案3: 保守Radio处理器
        if hasattr(self, 'conservative_radio_handler') and self.conservative_radio_handler:
            try:
                logger.info(f"🎯 方案3: 使用保守Radio处理器")
                result = await self.conservative_radio_handler.handle_conservative_radio_click(
                    page, dom_element, index, element_text
                )
                if result.get('success'):
                    return ActionResult(
                        extracted_content=f"✅ 保守Radio处理器成功: {element_text}"
                    )
            except Exception as e:
                logger.warning(f"⚠️ 保守Radio处理器异常: {e}")
        
        # 方案4: 标准回退
        return await self._safe_fallback_click(browser, index, element_text)

    async def _handle_drag_drop_with_all_engines(
        self, browser: BrowserContext, page, dom_element, element_text: str
    ) -> ActionResult:
        """🔀 使用拖拽引擎处理"""
        if hasattr(self, 'drag_drop_engine') and self.drag_drop_engine and self.digital_human_info:
            try:
                logger.info(f"🔀 使用拖拽排序引擎")
                
                question_analysis = await self.drag_drop_engine.analyze_drag_drop_question_type(
                    page, self.digital_human_info
                )
                
                if question_analysis.get("question_type") != "none":
                    result = await self.drag_drop_engine.handle_drag_drop_question(
                        page, question_analysis, self.digital_human_info
                    )
                    
                    if result.get('success'):
                        return ActionResult(
                            extracted_content=f"✅ 拖拽排序成功: {result.get('method', '未知')}"
                        )
            except Exception as e:
                logger.warning(f"⚠️ 拖拽引擎异常: {e}")
        
        return ActionResult(extracted_content="⚠️ 拖拽题型暂不支持，跳过")

    async def _handle_dropdown_with_intelligence(
        self, browser: BrowserContext, page, dom_element, element_text: str, index: int
    ) -> ActionResult:
        """📋 智能处理下拉选择"""
        if hasattr(self, 'digital_human_info') and self.digital_human_info:
            try:
                discovery_result = await self.intelligent_option_discovery_engine(
                    page, self.digital_human_info,
                    target_question_context="dropdown_selection",
                    search_scope="general"
                )
                
                if discovery_result.get('success') and discovery_result.get('recommended_option'):
                    recommended = discovery_result['recommended_option']
                    logger.info(f"📋 智能推荐: {recommended.get('text', '未知')}")
                    
                    try:
                        xpath = '//' + dom_element.xpath
                        await page.select_option(xpath, value=recommended.get('value', ''))
                        return ActionResult(
                            extracted_content=f"✅ 智能下拉选择成功: {recommended.get('text', '未知')}"
                        )
                    except Exception as e:
                        logger.warning(f"⚠️ 下拉选择执行失败: {e}")
            except Exception as e:
                logger.warning(f"⚠️ 下拉智能处理异常: {e}")
        
        return await self._safe_fallback_click(browser, index, element_text)

    async def _handle_general_selection_enhanced(
        self, browser: BrowserContext, page, dom_element, element_text: str, index: int
    ) -> ActionResult:
        """🎯 增强版通用选择处理"""
        # 使用现有的智能选择决策
        if hasattr(self, 'digital_human_info') and self.digital_human_info:
            try:
                decision_result = await self._make_intelligent_selection_decision(
                    element_text, index, browser, self.digital_human_info
                )
                
                if decision_result.get("should_override"):
                    logger.warning(f"🚫 智能决策拒绝: {element_text}")
                    logger.info(f"✅ 推荐: {decision_result.get('recommended_choice', '未知')}")
                    
                    correct_choice_result = await self._find_and_click_correct_option(
                        decision_result['recommended_choice'], browser
                    )
                    
                    if correct_choice_result.get("success"):
                        return ActionResult(
                            extracted_content=f"✅ 智能选择成功: {decision_result['recommended_choice']}"
                        )
            except Exception as e:
                logger.warning(f"⚠️ 通用智能选择异常: {e}")
        
        # 执行标准点击
        return await self._safe_fallback_click(browser, index, element_text)

    async def _safe_fallback_click(self, browser: BrowserContext, index: int, element_text: str = "") -> ActionResult:
        """🛡️ 安全回退点击"""
        try:
            page = await browser.get_current_page()
            selector_map = await browser.get_selector_map()
            
            if index in selector_map:
                dom_element = selector_map[index]
                xpath = '//' + dom_element.xpath
                element_locator = page.locator(xpath)
                
                # 确保元素可见
                await element_locator.scroll_into_view_if_needed()
                await asyncio.sleep(0.3)
                
                await element_locator.click()
                
                if not element_text:
                    element_text = getattr(dom_element, 'text', '') or ''
                
                return ActionResult(
                    extracted_content=f"标准点击: {element_text}",
                    include_in_memory=True
                )
            else:
                return ActionResult(error=f"Element index {index} not found in selector map")
        
        except Exception as e:
            logger.error(f"❌ 安全回退点击失败: {e}")
            return ActionResult(error=f"安全回退点击失败: {e}")

    def get_engine_status(self) -> Dict:
        """获取所有引擎状态"""
        return {
            "controller_type": "EnhancedCustomController",
            "engine_status": getattr(self, 'engine_status', {}),
            "total_engines": len(getattr(self, 'engine_status', {})),
            "active_engines": sum(getattr(self, 'engine_status', {}).values()),
            "digital_human_info_available": bool(getattr(self, 'digital_human_info', None))
        } 