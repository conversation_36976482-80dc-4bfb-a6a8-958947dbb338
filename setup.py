from setuptools import setup, find_packages

setup(
    name="browser_use",
    version="0.1.0",
    packages=find_packages(),
    install_requires=[
        "aiohttp",
        "playwright",
        "langchain",
        "langchain-google-genai",
        "langchain-openai",
        "pydantic",
        "python-dotenv",
        "requests",
        "beautifulsoup4",
        "pillow",
        "numpy",
        "pandas"
    ],
    python_requires=">=3.8",
    author="Your Name",
    author_email="<EMAIL>",
    description="Browser automation with human-like behavior",
    long_description=open("README.md").read(),
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/browser-use",
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
) 