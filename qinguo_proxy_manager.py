#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
青果普通代理管理器
管理青果普通代理的获取、分配和释放
"""

import requests
import time
import logging
from typing import Dict, List, Optional, Any
from src.config.proxy_config import (
    get_proxy_config,
    format_auth_string,
    get_proxy_url,
    get_api_params,
    validate_proxy_config
)

logger = logging.getLogger(__name__)

class QinguoProxyManager:
    """青果普通代理管理器"""
    
    def __init__(self):
        # 从配置系统获取青果代理配置
        self.config = get_proxy_config()
        
        # 验证配置
        error = validate_proxy_config()
        if error:
            raise ValueError(f"代理配置错误: {error}")
        
        # 代理池管理
        self.proxy_pool = []  # 代理池
        self.last_refresh = 0  # 上次刷新时间
    
    def refresh_proxy_pool(self) -> bool:
        """刷新代理池"""
        try:
            # 检查是否需要刷新
            current_time = time.time()
            if (current_time - self.last_refresh) < self.config["pool_refresh_interval"]:
                return True
            
            # 获取API参数
            params = get_api_params("allocate")
            params.update({
                "num": self.config["pool_size"],
                "type": "short"  # 短效代理
            })
            
            # 调用API获取代理
            url = f"{self.config['base_url']}/allocate"
            response = requests.get(url, params=params, timeout=self.config["timeout"])
            response.raise_for_status()
            
            # 解析响应
            if response.text.startswith("ERROR"):
                raise Exception(f"API错误: {response.text}")
            
            # 更新代理池
            proxies = response.json()
            if not isinstance(proxies, list):
                raise Exception("API返回格式错误")
            
            self.proxy_pool = [
                {
                    "proxy_type": "http",
                    "proxy_host": proxy["host"],
                    "proxy_port": proxy["port"],
                    "proxy_user": format_auth_string("default"),
                    "proxy_password": self.config["auth_pwd"],
                    "allocated": False,
                    "allocated_to": None,
                    "allocated_at": None,
                    "expires_at": current_time + proxy["ttl"],
                    "type": "short"
                }
                for proxy in proxies
            ]
            
            self.last_refresh = current_time
            logger.info(f"代理池刷新成功，获取 {len(self.proxy_pool)} 个代理")
            return True
            
        except Exception as e:
            logger.error(f"刷新代理池失败: {e}")
            return False
    
    def allocate_proxy_for_browser(self, browser_name: str) -> Dict:
        """为浏览器分配代理"""
        try:
            # 刷新代理池
            if not self.proxy_pool or time.time() > self.last_refresh + self.config["pool_refresh_interval"]:
                if not self.refresh_proxy_pool():
                    raise Exception("无法刷新代理池")
            
            # 查找可用代理
            current_time = time.time()
            for proxy in self.proxy_pool:
                if not proxy["allocated"] and proxy["expires_at"] > current_time:
                    # 测试代理连接
                    if not self.test_proxy(proxy):
                        continue
                    
                    # 分配代理
                    proxy.update({
                        "allocated": True,
                        "allocated_to": browser_name,
                        "allocated_at": current_time
                    })
                    
                    logger.info(f"为浏览器 {browser_name} 分配代理: {proxy['proxy_host']}:{proxy['proxy_port']}")
                    return proxy
            
            raise Exception("没有可用的代理")
            
        except Exception as e:
            logger.error(f"分配代理失败: {e}")
            return {}
    
    def generate_adspower_proxy_config(self, proxy: Dict) -> Dict:
        """生成AdsPower代理配置"""
        if not proxy or proxy.get("type") != "short":
            return {
                "proxy_soft": "no_proxy",
                "proxy_type": "noproxy"
            }
        
        return {
            "proxy_soft": "other",
            "proxy_type": "http",
            "proxy_host": proxy["proxy_host"],
            "proxy_port": proxy["proxy_port"],
            "proxy_user": proxy["proxy_user"],
            "proxy_password": proxy["proxy_password"],
            
            # 附加配置
            "proxy_check": True,  # 启用代理检查
            "proxy_connection_timeout": 10,  # 连接超时时间
            "proxy_read_timeout": 30,  # 读取超时时间
            "proxy_retry_count": 3,  # 重试次数
            
            # DNS配置
            "proxy_dns_type": "default",  # 使用系统DNS
            
            # 高级选项
            "proxy_force_local_dns": False,  # 不强制本地DNS
            "proxy_force_http_tunnel": True,  # 强制HTTP隧道
            "proxy_disable_cache": True  # 禁用缓存
        }
    
    def test_proxy(self, proxy: Dict) -> bool:
        """测试代理是否可用"""
        try:
            if not proxy or proxy.get("type") != "short":
                return False
            
            # 获取代理URL
            proxy_url = get_proxy_url("http", "default")
            
            proxies = {
                "http": proxy_url,
                "https": proxy_url
            }
            
            # 添加重试机制
            for attempt in range(self.config["retry_attempts"]):
                try:
                    # 测试请求
                    response = requests.get(
                        "http://httpbin.org/ip",
                        proxies=proxies,
                        timeout=self.config["timeout"]
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        current_ip = result.get('origin', 'unknown')
                        logger.info(f"代理测试成功: {proxy['proxy_host']}:{proxy['proxy_port']} -> IP: {current_ip}")
                        proxy["current_ip"] = current_ip
                        return True
                    
                except Exception as e:
                    if attempt == self.config["retry_attempts"] - 1:
                        logger.error(f"代理测试失败 {proxy['proxy_host']}:{proxy['proxy_port']}: {e}")
                        return False
                    time.sleep(self.config["retry_delay"])
            
            return False
            
        except Exception as e:
            logger.error(f"代理测试失败: {e}")
            return False
    
    def release_proxy(self, browser_name: str) -> bool:
        """释放浏览器的代理"""
        try:
            for proxy in self.proxy_pool:
                if proxy.get("allocated_to") == browser_name:
                    proxy.update({
                        "allocated": False,
                        "allocated_to": None,
                        "allocated_at": None
                    })
                    logger.info(f"释放浏览器 {browser_name} 的代理: {proxy['proxy_host']}:{proxy['proxy_port']}")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"释放代理失败: {e}")
            return False
    
    def get_proxy_status(self) -> Dict:
        """获取代理状态"""
        current_time = time.time()
        return {
            "pool_size": len(self.proxy_pool),
            "available_count": sum(1 for p in self.proxy_pool if not p["allocated"] and p["expires_at"] > current_time),
            "allocated_count": sum(1 for p in self.proxy_pool if p["allocated"]),
            "expired_count": sum(1 for p in self.proxy_pool if p["expires_at"] <= current_time),
            "last_refresh": time.strftime("%H:%M:%S", time.localtime(self.last_refresh)),
            "allocated_proxies": [
                {
                    "browser": p["allocated_to"],
                    "proxy": f"{p['proxy_host']}:{p['proxy_port']}",
                    "current_ip": p.get("current_ip", "未检测"),
                    "allocated_time": time.strftime("%H:%M:%S", time.localtime(p["allocated_at"])),
                    "expires_in": int(p["expires_at"] - current_time)
                }
                for p in self.proxy_pool if p["allocated"]
            ]
        }
    
    async def cleanup_all_proxies(self):
        """清理所有代理"""
        try:
            logger.info("清理所有代理资源...")
            
            # 释放所有代理
            for proxy in self.proxy_pool:
                if proxy.get("allocated_to"):
                    self.release_proxy(proxy["allocated_to"])
            
            # 清理代理池
            self.proxy_pool.clear()
            self.last_refresh = 0
            
            logger.info("✅ 代理资源清理完成")
            
        except Exception as e:
            logger.error(f"❌ 代理资源清理失败: {e}")

# 测试函数
async def test_qinguo_proxy():
    """测试青果代理功能"""
    print("🧪 测试青果代理功能...")
    
    manager = QinguoProxyManager()
    
    try:
        # 1. 刷新代理池
        if manager.refresh_proxy_pool():
            print("✅ 代理池刷新成功")
        else:
            print("❌ 代理池刷新失败")
            return
        
        # 2. 为测试浏览器分配代理
        proxy1 = manager.allocate_proxy_for_browser("测试浏览器1")
        proxy2 = manager.allocate_proxy_for_browser("测试浏览器2")
        
        print(f"\n✅ 分配代理:")
        print(f"   浏览器1: {proxy1['proxy_host']}:{proxy1['proxy_port']}")
        print(f"   浏览器2: {proxy2['proxy_host']}:{proxy2['proxy_port']}")
        
        # 3. 测试代理连接
        print(f"\n🔍 测试代理连接...")
        
        if manager.test_proxy(proxy1):
            print(f"✅ 浏览器1代理测试成功: IP = {proxy1.get('current_ip')}")
        else:
            print(f"❌ 浏览器1代理测试失败")
        
        if manager.test_proxy(proxy2):
            print(f"✅ 浏览器2代理测试成功: IP = {proxy2.get('current_ip')}")
        else:
            print(f"❌ 浏览器2代理测试失败")
        
        # 4. 生成AdsPower配置
        adspower_config1 = manager.generate_adspower_proxy_config(proxy1)
        adspower_config2 = manager.generate_adspower_proxy_config(proxy2)
        
        print(f"\n📋 AdsPower配置:")
        print(f"   浏览器1: {adspower_config1}")
        print(f"   浏览器2: {adspower_config2}")
        
        # 5. 显示状态
        status = manager.get_proxy_status()
        print(f"\n📊 代理池状态:")
        print(f"   总数量: {status['pool_size']}")
        print(f"   可用数量: {status['available_count']}")
        print(f"   已分配数量: {status['allocated_count']}")
        print(f"   已过期数量: {status['expired_count']}")
        print(f"   上次刷新时间: {status['last_refresh']}")
        
        print("\n📋 已分配代理:")
        for proxy_info in status['allocated_proxies']:
            print(f"   - {proxy_info['browser']}: {proxy_info['proxy']} (IP: {proxy_info['current_ip']}, 过期时间: {proxy_info['expires_in']}秒)")
        
        # 6. 清理资源
        await manager.cleanup_all_proxies()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    import asyncio
    asyncio.run(test_qinguo_proxy()) 