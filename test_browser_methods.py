#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试Browser对象的可用方法
"""

import asyncio
from browser_use import <PERSON><PERSON><PERSON>, BrowserConfig

async def test_browser_methods():
    """测试Browser对象的方法"""
    try:
        config = BrowserConfig(headless=True)
        browser = Browser(config=config)
        
        print("Browser对象的方法:")
        methods = [method for method in dir(browser) if not method.startswith('_')]
        for method in methods:
            print(f"  - {method}")
        
        # 尝试启动浏览器
        await browser.run()
        
        # 检查是否有page对象
        if hasattr(browser, 'page') and browser.page:
            print(f"\nPage对象的方法:")
            page_methods = [method for method in dir(browser.page) if not method.startswith('_')]
            for method in page_methods[:10]:  # 只显示前10个
                print(f"  - {method}")
        
        # 检查是否有browser对象
        if hasattr(browser, 'browser') and browser.browser:
            print(f"\nPlaywright Browser对象的方法:")
            playwright_methods = [method for method in dir(browser.browser) if not method.startswith('_')]
            for method in playwright_methods[:10]:  # 只显示前10个
                print(f"  - {method}")
        
        await browser.close()
        
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_browser_methods()) 