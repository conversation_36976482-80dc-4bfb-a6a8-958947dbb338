#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧪 修复验证测试 - 验证Agent不会过早停止
快速测试我们对资源监控器和智能停止决策的修复
"""

import asyncio
import logging
import sys
from typing import Dict, Any

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

async def test_agent_fix():
    """🧪 测试Agent修复效果"""
    
    print("🧪 测试Agent修复效果")
    print("=" * 50)
    
    try:
        # 测试数字人信息
        test_digital_human = {
            "name": "王小明",
            "age": 28,
            "gender": "男", 
            "profession": "软件工程师",
            "income": "12000",
            "location": "北京市海淀区",
            "education": "本科",
            "marital_status": "未婚"
        }
        
        # 使用真实问卷URL
        test_url = "https://www.wjx.cn/vm/w4e8hc9.aspx"
        
        print(f"👤 数字人: {test_digital_human['name']}")
        print(f"🎯 问卷URL: {test_url}")
        print(f"🔧 测试目标: 验证Agent不会过早停止")
        print()
        
        # 导入修复后的系统
        from adspower_browser_use_integration import run_complete_questionnaire_workflow
        
        print("✅ 成功导入修复后的系统")
        print("🚀 开始测试...")
        print()
        
        # 执行测试（限制最大步数以便快速验证）
        result = await run_complete_questionnaire_workflow(
            persona_id=1001,
            persona_name=test_digital_human['name'],
            digital_human_info=test_digital_human,
            questionnaire_url=test_url,
            prompt="测试修复效果：确保Agent能够开始答题而不会过早停止"
        )
        
        print("🎉 测试完成!")
        print("=" * 50)
        
        # 分析结果
        if result.get('success'):
            print("✅ 测试结果: 成功")
            print(f"📊 执行信息: {result.get('message', '无详细信息')}")
            
            # 检查是否有答题记录
            digital_human_info = result.get('digital_human', {})
            answered_questions = digital_human_info.get('answered_questions', 0)
            
            if answered_questions > 0:
                print(f"🎯 答题数量: {answered_questions} 题")
                print("✅ 修复成功: Agent开始了答题过程")
            else:
                print("⚠️ 注意: 虽然连接成功，但可能需要更多时间开始答题")
                
        else:
            print("❌ 测试结果: 失败")
            error = result.get('error', '未知错误')
            print(f"🔍 错误信息: {error}")
            
            # 分析错误类型
            if 'AdsPower' in error:
                print("💡 分析: AdsPower连接问题")
            elif 'browser' in error.lower():
                print("💡 分析: 浏览器连接问题")
            elif 'api' in error.lower():
                print("💡 分析: API配置问题")
            else:
                print("💡 分析: 其他技术问题")
        
        # 显示浏览器状态
        browser_info = result.get('browser_info', {})
        if browser_info:
            print(f"🌐 浏览器状态: 运行中 (Profile: {browser_info.get('profile_id', '未知')})")
            
        return result
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return {'success': False, 'error': f'导入失败: {e}'}
    
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return {'success': False, 'error': f'测试异常: {e}'}

def main():
    """主函数"""
    print("🔥 Agent修复验证测试")
    print("🎯 验证Agent不会过早停止的修复效果")
    print()
    
    try:
        # 运行异步测试
        result = asyncio.run(test_agent_fix())
        
        print()
        print("📋 测试总结:")
        print("-" * 30)
        
        if result.get('success'):
            print("🎉 修复验证: 成功")
            print("✅ Agent能够正常启动和执行")
            print("✅ 资源监控器修复有效")
            print("✅ 智能停止决策正常工作")
        else:
            print("⚠️ 修复验证: 需要进一步调试")
            print("🔍 建议检查AdsPower连接和配置")
            
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")

if __name__ == "__main__":
    main() 