# 🚨 智能链接过滤器 - 防止错误点击非问卷链接
import logging
import re
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)

class SmartLinkFilter:
    """智能链接过滤器 - 防止Agent点击非问卷相关链接"""
    
    def __init__(self):
        # 🚫 禁止点击的链接类型
        self.forbidden_link_patterns = [
            # 隐私政策和法律文档
            r'privacy.*policy', r'隐私.*政策', r'privacy.*statement',
            r'terms.*service', r'服务.*条款', r'terms.*conditions',
            r'cookie.*policy', r'cookie.*notice', r'cookie.*通知',
            r'legal.*notice', r'法律.*声明', r'disclaimer',
            
            # 外部链接和广告
            r'evidon\.com', r'trustpilot\.com', r'facebook\.com',
            r'twitter\.com', r'linkedin\.com', r'instagram\.com',
            r'advertisement', r'广告', r'sponsor', r'赞助',
            
            # 帮助和支持页面
            r'help.*center', r'帮助.*中心', r'support', r'客服',
            r'contact.*us', r'联系.*我们', r'about.*us', r'关于.*我们',
            
            # 登录和注册
            r'sign.*in', r'log.*in', r'登录', r'register', r'注册',
            r'create.*account', r'创建.*账户',
            
            # 其他非问卷链接
            r'download', r'下载', r'app.*store', r'应用.*商店',
            r'newsletter', r'订阅', r'subscribe'
        ]
        
        # ✅ 允许的问卷相关关键词
        self.allowed_questionnaire_patterns = [
            r'survey', r'questionnaire', r'问卷', r'调查', r'form',
            r'continue', r'继续', r'next', r'下一步', r'submit', r'提交',
            r'start', r'开始', r'begin', r'proceed', r'前进',
            r'answer', r'回答', r'complete', r'完成'
        ]
        
        # 🚫 禁止的元素选择器
        self.forbidden_selectors = [
            'a[href*="privacy"]', 'a[href*="terms"]', 'a[href*="cookie"]',
            'a[href*="evidon"]', 'a[href*="trustpilot"]',
            'a[target="_blank"]',  # 新窗口链接通常不是问卷
            '.privacy-link', '.terms-link', '.cookie-link',
            '[data-purpose="privacy"]', '[data-purpose="terms"]'
        ]
    
    def should_allow_click(self, element_text: str, element_href: str = "", element_class: str = "") -> Dict:
        """判断是否应该允许点击某个元素"""
        try:
            # 合并所有文本信息
            combined_text = f"{element_text} {element_href} {element_class}".lower()
            
            # 检查是否是禁止的链接
            for pattern in self.forbidden_link_patterns:
                if re.search(pattern, combined_text, re.IGNORECASE):
                    return {
                        "allowed": False,
                        "reason": f"匹配禁止模式: {pattern}",
                        "risk_level": "high"
                    }
            
            # 检查是否是问卷相关链接
            for pattern in self.allowed_questionnaire_patterns:
                if re.search(pattern, combined_text, re.IGNORECASE):
                    return {
                        "allowed": True,
                        "reason": f"匹配问卷模式: {pattern}",
                        "confidence": "high"
                    }
            
            # 默认：如果不确定，采用保守策略
            if len(element_text.strip()) < 3:  # 文本太短，可能是装饰性元素
                return {
                    "allowed": False,
                    "reason": "文本过短，可能非问卷元素",
                    "risk_level": "medium"
                }
            
            # 如果包含href且指向外部，谨慎处理
            if element_href and ('http' in element_href and 'survey' not in element_href.lower()):
                return {
                    "allowed": False,
                    "reason": "外部链接，非问卷相关",
                    "risk_level": "high"
                }
            
            return {
                "allowed": True,
                "reason": "未发现明显风险",
                "confidence": "medium"
            }
            
        except Exception as e:
            logger.error(f"链接过滤检查失败: {e}")
            return {
                "allowed": False,
                "reason": f"检查异常: {e}",
                "risk_level": "high"
            }
    
    def filter_clickable_elements(self, elements: List[Dict]) -> List[Dict]:
        """过滤可点击元素列表，移除危险链接"""
        try:
            safe_elements = []
            filtered_count = 0
            
            for element in elements:
                element_text = element.get('text', '')
                element_href = element.get('href', '')
                element_class = element.get('class', '')
                
                check_result = self.should_allow_click(element_text, element_href, element_class)
                
                if check_result['allowed']:
                    safe_elements.append(element)
                    logger.debug(f"✅ 允许元素: '{element_text[:30]}...' - {check_result['reason']}")
                else:
                    filtered_count += 1
                    logger.info(f"🚫 过滤元素: '{element_text[:30]}...' - {check_result['reason']}")
            
            logger.info(f"🔍 链接过滤完成: 保留{len(safe_elements)}个, 过滤{filtered_count}个")
            return safe_elements
            
        except Exception as e:
            logger.error(f"元素过滤失败: {e}")
            return elements  # 失败时返回原列表

# 全局过滤器实例
SMART_LINK_FILTER = SmartLinkFilter() 