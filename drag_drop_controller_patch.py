"""
🎯 智能拖拽排序控制器补丁
为CustomController添加拖拽排序功能
"""

import asyncio
import logging
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)

class DragDropControllerPatch:
    """拖拽排序控制器补丁"""
    
    def __init__(self, controller):
        self.controller = controller
        self._initialize_drag_drop_engine()
    
    def _initialize_drag_drop_engine(self):
        """初始化拖拽排序引擎"""
        try:
            from intelligent_drag_drop_ranking_engine import IntelligentDragDropRankingEngine
            self.controller.drag_drop_engine = IntelligentDragDropRankingEngine()
            logger.info("✅ 智能拖拽排序引擎初始化成功")
        except ImportError as e:
            logger.warning(f"⚠️ 拖拽排序引擎导入失败: {e}")
            self.controller.drag_drop_engine = None
        except Exception as e:
            logger.error(f"❌ 拖拽排序引擎初始化失败: {e}")
            self.controller.drag_drop_engine = None
    
    async def detect_drag_drop_question_type(self, page, digital_human_info: Dict) -> Dict:
        """🔍 检测拖拽排序题型"""
        try:
            logger.info("🔍 开始检测拖拽排序题型...")
            
            # 获取页面内容分析
            page_analysis = await page.evaluate("""
                () => {
                    const result = {
                        draggable_elements: 0,
                        drop_zones: 0,
                        sortable_lists: 0,
                        shuttle_boxes: 0,
                        question_text: '',
                        has_ranking_keywords: false,
                        has_drag_keywords: false
                    };
                    
                    // 检测可拖拽元素
                    const draggableElements = document.querySelectorAll('[draggable="true"], .draggable, .sortable-item, .drag-item');
                    result.draggable_elements = draggableElements.length;
                    
                    // 检测投放区域
                    const dropZones = document.querySelectorAll('.drop-zone, .droppable, .sortable-container, [ondrop]');
                    result.drop_zones = dropZones.length;
                    
                    // 检测排序列表
                    const sortableLists = document.querySelectorAll('.sortable, .ranking-list, ul.sortable, ol.sortable');
                    result.sortable_lists = sortableLists.length;
                    
                    // 检测穿梭框
                    const shuttleBoxes = document.querySelectorAll('.shuttle-box, .transfer-box, .dual-list');
                    result.shuttle_boxes = shuttleBoxes.length;
                    
                    // 获取问题文本
                    const questionElements = document.querySelectorAll('.question, .title, h1, h2, h3, .question-text');
                    let questionText = '';
                    for (const elem of questionElements) {
                        if (elem.textContent && elem.textContent.trim().length > 10) {
                            questionText += elem.textContent.trim() + ' ';
                        }
                    }
                    result.question_text = questionText.trim();
                    
                    // 检测关键词
                    const text = (document.body.textContent || '').toLowerCase();
                    result.has_ranking_keywords = /排序|重要性|优先级|rank|order|priority|importance|按照.*顺序/.test(text);
                    result.has_drag_keywords = /拖拽|拖动|drag|drop|拖曳/.test(text);
                    
                    return result;
                }
            """)
            
            # 判断是否为拖拽题型
            is_drag_drop = self._analyze_drag_drop_indicators(page_analysis)
            question_type = self._determine_specific_drag_drop_type(page_analysis)
            
            return {
                'is_drag_drop_question': is_drag_drop,
                'question_type': question_type,
                'page_analysis': page_analysis,
                'confidence': self._calculate_drag_drop_confidence(page_analysis)
            }
            
        except Exception as e:
            logger.error(f"❌ 拖拽题型检测失败: {e}")
            return {
                'is_drag_drop_question': False,
                'question_type': 'none',
                'error': str(e)
            }
    
    def _analyze_drag_drop_indicators(self, page_analysis: Dict) -> bool:
        """分析拖拽指标"""
        try:
            # 多重指标检测
            indicators = [
                page_analysis.get('draggable_elements', 0) > 0,
                page_analysis.get('drop_zones', 0) > 0,
                page_analysis.get('sortable_lists', 0) > 0,
                page_analysis.get('shuttle_boxes', 0) > 0,
                page_analysis.get('has_ranking_keywords', False),
                page_analysis.get('has_drag_keywords', False)
            ]
            
            # 至少满足2个指标
            return sum(indicators) >= 2
            
        except Exception as e:
            logger.warning(f"⚠️ 拖拽指标分析失败: {e}")
            return False
    
    def _determine_specific_drag_drop_type(self, page_analysis: Dict) -> str:
        """确定具体的拖拽题型"""
        try:
            # 排序题型
            if (page_analysis.get('has_ranking_keywords', False) and 
                (page_analysis.get('draggable_elements', 0) > 2 or page_analysis.get('sortable_lists', 0) > 0)):
                return 'ranking_sort'
            
            # 穿梭框题型
            if page_analysis.get('shuttle_boxes', 0) > 0:
                return 'shuttle_transfer'
            
            # 优先级排序
            if 'priority' in page_analysis.get('question_text', '').lower():
                return 'priority_ordering'
            
            # 通用拖拽
            if (page_analysis.get('draggable_elements', 0) > 0 or 
                page_analysis.get('drop_zones', 0) > 0):
                return 'generic_drag_drop'
            
            return 'none'
            
        except Exception as e:
            logger.warning(f"⚠️ 拖拽题型确定失败: {e}")
            return 'none'
    
    def _calculate_drag_drop_confidence(self, page_analysis: Dict) -> float:
        """计算拖拽检测置信度"""
        try:
            confidence = 0.0
            
            # 各项指标加分
            if page_analysis.get('draggable_elements', 0) > 0:
                confidence += 0.3
            if page_analysis.get('drop_zones', 0) > 0:
                confidence += 0.25
            if page_analysis.get('sortable_lists', 0) > 0:
                confidence += 0.2
            if page_analysis.get('has_ranking_keywords', False):
                confidence += 0.15
            if page_analysis.get('has_drag_keywords', False):
                confidence += 0.1
            
            return min(1.0, confidence)
            
        except Exception as e:
            logger.warning(f"⚠️ 置信度计算失败: {e}")
            return 0.0
    
    async def handle_intelligent_drag_drop_sorting(self, page, digital_human_info: Dict, drag_detection: Dict) -> Dict:
        """🎯 处理智能拖拽排序"""
        try:
            logger.info("🎯 开始智能拖拽排序处理...")
            
            # 检查引擎是否可用
            if not hasattr(self.controller, 'drag_drop_engine') or self.controller.drag_drop_engine is None:
                return {
                    'success': False,
                    'error': '拖拽排序引擎未初始化'
                }
            
            # 调用拖拽排序引擎
            result = await self.controller.drag_drop_engine.handle_drag_drop_question(
                page=page,
                digital_human_info=digital_human_info,
                ranking_context="importance"
            )
            
            if result.get('success'):
                logger.info(f"✅ 拖拽排序成功: {result.get('method')}")
                
                # 等待页面响应
                await asyncio.sleep(2)
                
                return {
                    'success': True,
                    'method': result.get('method', 'intelligent_drag_drop'),
                    'details': result.get('details', {})
                }
            else:
                logger.warning(f"⚠️ 拖拽排序失败: {result.get('error')}")
                return {
                    'success': False,
                    'error': result.get('error', '未知错误')
                }
                
        except Exception as e:
            logger.error(f"❌ 智能拖拽排序处理异常: {e}")
            return {
                'success': False,
                'error': f'处理异常: {str(e)}'
            }


def apply_drag_drop_patch(controller):
    """应用拖拽排序补丁到控制器"""
    patch = DragDropControllerPatch(controller)
    
    # 添加方法到控制器
    controller._detect_drag_drop_question_type = patch.detect_drag_drop_question_type
    controller._handle_intelligent_drag_drop_sorting = patch.handle_intelligent_drag_drop_sorting
    
    logger.info("✅ 拖拽排序补丁已应用到控制器")
    return patch 