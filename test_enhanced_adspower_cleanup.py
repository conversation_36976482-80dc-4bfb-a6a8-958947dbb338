#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试增强版AdsPower资源清理功能
验证两步骤清理流程是否正确工作
"""

import asyncio
import logging
import sys
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_enhanced_cleanup():
    """测试增强版AdsPower资源清理"""
    try:
        logger.info("🚀 开始测试增强版AdsPower资源清理功能")
        
        # 导入增强版资源管理器
        from adspower_enhanced_resource_manager import EnhancedAdsPowerResourceManager
        
        # 创建管理器实例
        manager = EnhancedAdsPowerResourceManager()
        
        # 测试用的配置文件ID（请替换为实际的ID）
        test_profiles = [
            {"id": "k10lqo46", "name": "测试配置1"},
            # 可以添加更多测试配置
        ]
        
        logger.info(f"📋 将测试 {len(test_profiles)} 个配置文件的清理功能")
        
        for i, profile in enumerate(test_profiles, 1):
            profile_id = profile["id"]
            persona_name = profile["name"]
            
            logger.info(f"\n{'='*60}")
            logger.info(f"🔧 测试 {i}/{len(test_profiles)}: {persona_name} ({profile_id})")
            logger.info(f"{'='*60}")
            
            # 执行增强版清理
            result = await manager.complete_cleanup_adspower_profile(
                profile_id=profile_id,
                persona_name=persona_name
            )
            
            # 显示结果
            logger.info(f"\n📊 清理结果:")
            for key, value in result.items():
                logger.info(f"   {key}: {value}")
            
            # 评估结果
            if result.get("success"):
                logger.info(f"✅ {persona_name} 资源清理成功")
                if result.get("profile_deleted"):
                    logger.info("🎯 配置文件已从AdsPower应用列表中完全移除")
                    logger.info("💾 浏览器额度已释放")
            else:
                logger.warning(f"⚠️ {persona_name} 资源清理失败")
                error = result.get("error", "未知错误")
                logger.warning(f"❌ 错误原因: {error}")
            
            # 测试间隔
            if i < len(test_profiles):
                logger.info("⏳ 等待5秒后进行下一个测试...")
                await asyncio.sleep(5)
        
        logger.info(f"\n🎉 增强版AdsPower资源清理测试完成!")
        
    except ImportError:
        logger.error("❌ 无法导入增强版资源管理器")
        logger.error("请确保 adspower_enhanced_resource_manager.py 文件存在")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ 测试过程中出现异常: {e}")
        sys.exit(1)

async def test_verification():
    """测试配置文件删除验证功能"""
    try:
        logger.info("🔍 测试配置文件删除验证功能")
        
        from adspower_enhanced_resource_manager import EnhancedAdsPowerResourceManager
        manager = EnhancedAdsPowerResourceManager()
        
        # 测试验证功能
        test_profile_id = "k10lqo46"  # 已删除的配置文件ID
        
        result = await manager._verify_profile_deletion(test_profile_id)
        
        if result is True:
            logger.info("✅ 验证成功：配置文件已完全删除")
        elif result is False:
            logger.warning("⚠️ 验证失败：配置文件仍然存在")
        else:
            logger.info("ℹ️ 无法验证删除结果")
            
    except Exception as e:
        logger.error(f"❌ 验证测试失败: {e}")

def print_usage():
    """打印使用说明"""
    print("\n🔧 AdsPower增强资源清理测试工具")
    print("=" * 50)
    print("功能：")
    print("1. 测试两步骤资源清理流程")
    print("2. 验证配置文件是否完全删除")
    print("3. 检查浏览器额度释放情况")
    print("\n使用方法：")
    print("python test_enhanced_adspower_cleanup.py")
    print("\n注意事项：")
    print("1. 请修改代码中的 test_profiles 列表，添加实际的配置文件ID")
    print("2. 确保AdsPower正在运行")
    print("3. 测试会真实删除配置文件，请谨慎操作")
    print("=" * 50)

if __name__ == "__main__":
    print_usage()
    
    # 确认是否继续
    try:
        user_input = input("\n是否继续执行测试？(y/N): ").strip().lower()
        if user_input != 'y':
            print("测试已取消")
            sys.exit(0)
    except KeyboardInterrupt:
        print("\n测试已取消")
        sys.exit(0)
    
    # 运行测试
    asyncio.run(test_enhanced_cleanup())
    
    # 运行验证测试
    print("\n" + "="*50)
    asyncio.run(test_verification())
