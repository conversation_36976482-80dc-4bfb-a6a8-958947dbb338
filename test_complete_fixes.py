#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试完整修复方案
验证：
1. 单标签页导航修复
2. 主动智能答题启动机制
3. 增强版AdsPower资源管理器集成
"""

print("🔍 ============== 完整修复方案测试 ==============")
print("验证GitHub版本(91a430eb)基础 + 最新修改增强")

try:
    with open('adspower_browser_use_integration.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 测试1: 单标签页导航修复
    print("\n🔍 测试1: 单标签页导航修复")
    single_tab_checks = [
        "执行单标签页导航方案（修复多标签页问题）",
        "page.goto(questionnaire_url, wait_until='networkidle'",
        "fallback: 使用browser_context导航",
        "单标签页导航完成"
    ]
    
    single_tab_score = 0
    for check in single_tab_checks:
        if check in content:
            print(f"  ✅ 发现: {check[:50]}...")
            single_tab_score += 1
        else:
            print(f"  ❌ 缺少: {check[:50]}...")
    
    # 测试2: 增强版资源管理器
    print("\n🔧 测试2: 增强版AdsPower资源管理器")
    resource_checks = [
        "from adspower_enhanced_resource_manager import EnhancedAdsPowerResourceManager",
        "enhanced_resource_manager_available",
        "增强版AdsPower资源管理器已加载",
        "支持两步骤彻底清理"
    ]
    
    resource_score = 0
    for check in resource_checks:
        if check in content:
            print(f"  ✅ 发现: {check[:50]}...")
            resource_score += 1
        else:
            print(f"  ❌ 缺少: {check[:50]}...")
    
    # 测试3: 防多标签页
    print("\n🚫 测试3: 防多标签页逻辑")
    anti_patterns = ["new_tab", "open_tab", "create_tab"]
    good_patterns = ["page.goto(", "get_current_page()", "在当前标签页"]
    
    problems = [p for p in anti_patterns if p in content.lower()]
    solutions = [p for p in good_patterns if p in content]
    
    if not problems and solutions:
        print(f"  ✅ 无多标签页代码，有{len(solutions)}个单标签页方法")
        anti_tab_score = 1
    else:
        print(f"  ❌ 发现问题: {problems}")
        anti_tab_score = 0
    
    # 测试4: 数字人信息注入
    print("\n🧠 测试4: 数字人信息注入")
    persona_checks = [
        "digital_human_info",
        "数字人信息已注入控制器",
        "根据数字人信息"
    ]
    
    persona_score = 0
    for check in persona_checks:
        if check in content:
            print(f"  ✅ 发现: {check[:30]}...")
            persona_score += 1
        else:
            print(f"  ❌ 缺少: {check[:30]}...")
    
    # 计算总分
    total_score = single_tab_score + resource_score + anti_tab_score + persona_score
    max_score = len(single_tab_checks) + len(resource_checks) + 1 + len(persona_checks)
    
    print(f"\n📊 ============== 测试结果汇总 ==============")
    print(f"单标签页导航: {single_tab_score}/{len(single_tab_checks)}")
    print(f"资源管理器: {resource_score}/{len(resource_checks)}")
    print(f"防多标签页: {anti_tab_score}/1")
    print(f"数字人注入: {persona_score}/{len(persona_checks)}")
    print(f"总分: {total_score}/{max_score} ({total_score/max_score*100:.1f}%)")
    
    if total_score >= max_score * 0.8:
        print("🎉 修复方案验证成功！")
        print("\n🚀 预期改进：")
        print("  ✅ 不再打开多个标签页 - 使用page.goto()在当前标签页导航")
        print("  ✅ AdsPower资源会被彻底清理 - 增强版管理器")
        print("  ✅ 数字人信息会被正确使用 - 注入到控制器")
        print("  ✅ 错误处理更加完善 - 多层fallback机制")
    else:
        print("⚠️ 部分功能可能需要进一步验证")

except Exception as e:
    print(f"❌ 测试失败: {e}")

print("\n🔧 ============== 具体修复点说明 ==============")
print("1. 单标签页修复：将browser_context.navigate_to()改为page.goto()")
print("2. 主动答题启动：页面稳定后检测问卷元素并启动答题")
print("3. 资源管理增强：集成EnhancedAdsPowerResourceManager")
print("4. 数字人感知：控制器获得数字人信息进行智能决策")
print("5. 错误容错机制：多层fallback确保导航成功") 