#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔥 五层融合智能问卷系统 - 完整启动脚本
整合了所有必要的功能：
1. AdsPower浏览器管理
2. 五层融合智能架构
3. 数字人问卷填写
4. 资源自动清理
"""

import asyncio
import json
import logging
import sys
import time
from typing import Dict, Any, Optional

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('five_layer_system.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class FiveLayerQuestionnaireSystem:
    """🔥 五层融合智能问卷系统主控制器"""
    
    def __init__(self):
        self.lifecycle_manager = None
        self.integration_system = None
        self.current_browser_env = None
        
    async def initialize_system(self):
        """🚀 初始化五层融合系统"""
        try:
            # 导入依赖模块
            from enhanced_adspower_lifecycle import AdsPowerLifecycleManager
            from adspower_browser_use_integration import AdsPowerBrowserUseIntegration
            
            self.lifecycle_manager = AdsPowerLifecycleManager()
            
            # 检查AdsPower服务状态
            if not await self.lifecycle_manager.check_service_status():
                logger.error("❌ AdsPower服务未运行，请先启动AdsPower")
                return False
            
            logger.info("✅ 五层融合系统初始化完成")
            return True
            
        except ImportError as e:
            logger.error(f"❌ 模块导入失败: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ 系统初始化失败: {e}")
            return False
    
    async def run_complete_questionnaire(self, 
                                      questionnaire_url: str,
                                      digital_human_info: Dict[str, Any],
                                      max_execution_time: int = 1800) -> Dict[str, Any]:
        """🔥 运行完整的五层融合问卷系统"""
        
        start_time = time.time()
        result = {
            'success': False,
            'execution_time': 0,
            'questionnaire_url': questionnaire_url,
            'digital_human': digital_human_info.get('name', '未知'),
            'error_message': '',
            'details': {}
        }
        
        try:
            logger.info("🔥 启动五层融合智能问卷系统")
            logger.info(f"🎯 问卷链接: {questionnaire_url}")
            logger.info(f"👤 数字人: {digital_human_info.get('name', '未知')}")
            
            # 第一阶段：创建浏览器环境
            logger.info("📍 第一阶段：创建AdsPower浏览器环境")
            
            persona_id = digital_human_info.get('id', int(time.time()) % 1000)
            persona_name = digital_human_info.get('name', 'TestPerson')
            
            self.current_browser_env = await self.lifecycle_manager.create_complete_browser_environment(
                persona_id=persona_id,
                persona_name=persona_name
            )
            
            if not self.current_browser_env or not self.current_browser_env.get('profile_id'):
                result['error_message'] = "浏览器环境创建失败"
                return result
            
            logger.info(f"✅ 浏览器环境创建成功: {self.current_browser_env['profile_id']}")
            
            # 第二阶段：初始化五层融合系统
            logger.info("📍 第二阶段：初始化五层融合集成系统")
            
            from adspower_browser_use_integration import AdsPowerBrowserUseIntegration
            
            self.integration_system = AdsPowerBrowserUseIntegration(
                self.current_browser_env['profile_id']
            )
            
            # 构建WebSocket端点
            debug_port = self.current_browser_env.get('debug_port')
            if not debug_port:
                result['error_message'] = "未获取到调试端口"
                return result
            
            ws_endpoint = f"ws://127.0.0.1:{debug_port}"
            
            # 初始化浏览器连接
            if not await self.integration_system.initialize_browser(ws_endpoint):
                result['error_message'] = "五层融合系统初始化失败"
                return result
            
            # 创建智能Agent
            if not await self.integration_system.create_intelligent_agent(
                digital_human_info,
                f"完成问卷填写任务: {questionnaire_url}",
                None
            ):
                result['error_message'] = "智能Agent创建失败"
                return result
            
            logger.info("✅ 五层融合系统初始化完成")
            
            # 第三阶段：执行智能问卷
            logger.info("📍 第三阶段：执行五层融合智能问卷")
            
            questionnaire_result = await self.integration_system.run_intelligent_questionnaire(
                questionnaire_url,
                max_execution_time
            )
            
            # 更新结果
            result.update({
                'success': questionnaire_result.get('success', False),
                'details': questionnaire_result,
                'execution_time': time.time() - start_time
            })
            
            if questionnaire_result.get('success'):
                logger.info("🎉 五层融合问卷系统执行完成")
            else:
                result['error_message'] = questionnaire_result.get('error_message', '未知错误')
                logger.warning(f"⚠️ 问卷执行未完全成功: {result['error_message']}")
            
            return result
            
        except Exception as e:
            result['error_message'] = f"系统执行异常: {e}"
            result['execution_time'] = time.time() - start_time
            logger.error(f"❌ 五层融合系统执行异常: {e}")
            return result
        
        finally:
            # 确保资源清理
            await self.cleanup_resources()
    
    async def cleanup_resources(self):
        """🔧 清理系统资源"""
        try:
            if self.current_browser_env and self.lifecycle_manager:
                profile_id = self.current_browser_env.get('profile_id')
                if profile_id:
                    logger.info("🔧 开始清理浏览器资源...")
                    await self.lifecycle_manager.force_cleanup_browser(
                        profile_id, 
                        "系统完成清理"
                    )
                    logger.info("✅ 资源清理完成")
        except Exception as e:
            logger.warning(f"⚠️ 资源清理异常: {e}")

async def main():
    """🔥 主入口函数"""
    print("🔥 五层融合智能问卷系统启动")
    print("=" * 60)
    
    # 默认配置
    questionnaire_config = {
        'url': 'http://www.jinshengsurveys.com/?type=qtaskgoto&id=31393&token=EAE8C0EC7F66F828D47F753B487D435CA108172CDC98352371611CC8E8036C152943D234D38237BF4A524D0363900563576EC030D48CB7A13DFC735B9999C94D',
        'digital_human': {
            'id': 1001,
            'name': '张小雅',
            'age': 28,
            'gender': '女',
            'profession': '产品经理',
            'income': '12000',
            'education': '本科',
            'location': '北京市',
            'interests': ['科技', '阅读', '健身'],
            'personality': '理性消费者'
        },
        'max_execution_time': 1800  # 30分钟
    }
    
    # 创建系统实例
    system = FiveLayerQuestionnaireSystem()
    
    try:
        # 初始化系统
        if not await system.initialize_system():
            print("❌ 系统初始化失败")
            return
        
        print("✅ 系统初始化成功")
        print(f"🎯 问卷URL: {questionnaire_config['url']}")
        print(f"👤 数字人: {questionnaire_config['digital_human']['name']}")
        print("🚀 开始执行五层融合问卷系统...")
        print()
        
        # 执行问卷
        result = await system.run_complete_questionnaire(
            questionnaire_config['url'],
            questionnaire_config['digital_human'],
            questionnaire_config['max_execution_time']
        )
        
        # 显示结果
        print()
        print("📊 执行结果报告")
        print("=" * 60)
        print(f"🎯 执行状态: {'✅ 成功' if result['success'] else '❌ 失败'}")
        print(f"⏰ 执行时间: {result['execution_time']:.1f}秒")
        print(f"👤 数字人: {result['digital_human']}")
        
        if result.get('error_message'):
            print(f"❌ 错误信息: {result['error_message']}")
        
        if result.get('details'):
            details = result['details']
            print(f"📝 答题数量: {details.get('questions_answered', 0)}")
            print(f"📄 页面数量: {details.get('pages_navigated', 0)}")
            print(f"✅ 完成原因: {details.get('completion_reason', '未知')}")
            
            if details.get('five_layer_status'):
                print("🔥 五层架构状态:")
                for layer, status in details['five_layer_status'].items():
                    print(f"   {layer}: {status}")
        
        # 保存结果
        with open('questionnaire_result.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print()
        print("📄 详细结果已保存至: questionnaire_result.json")
        
        if result['success']:
            print("🎉 五层融合智能问卷系统执行完成！")
        else:
            print("❌ 系统执行未完全成功，请查看日志了解详情")
            
    except KeyboardInterrupt:
        print("\n⏸️ 用户中断执行")
        await system.cleanup_resources()
    except Exception as e:
        print(f"\n❌ 系统执行异常: {e}")
        await system.cleanup_resources()

if __name__ == "__main__":
    """🔥 五层融合智能问卷系统入口"""
    asyncio.run(main()) 