#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
🔧 CustomController修复补丁
解决register_intelligent_dropdown_engine方法缺失问题
"""

import logging
import asyncio
from typing import Dict
from browser_use.browser.context import BrowserContext
from browser_use.agent.views import ActionResult

logger = logging.getLogger(__name__)

def patch_custom_controller(controller):
    """为CustomController添加缺失的方法"""
    
    def register_intelligent_dropdown_engine(self):
        """🎯 注册智能下拉框引擎"""
        try:
            logger.info("🎯 注册智能下拉框引擎...")
            
            # 初始化智能下拉框引擎
            if not hasattr(self, 'dropdown_engine'):
                try:
                    from intelligent_dropdown_engine import IntelligentDropdownEngine
                    self.dropdown_engine = IntelligentDropdownEngine()
                    if hasattr(self, 'digital_human_info'):
                        self.dropdown_engine.set_digital_human_info(self.digital_human_info)
                    logger.info("✅ 智能下拉框引擎初始化成功")
                except ImportError:
                    logger.warning("⚠️ 智能下拉框引擎模块不可用，使用内置处理器")
                    self.dropdown_engine = None
            
            # 注册智能下拉框处理动作
            @self.registry.action(
                'Intelligent dropdown selection with digital human matching',
            )
            async def intelligent_dropdown_selection(
                index: int, 
                browser: BrowserContext,
                target_value: str = ""
            ) -> ActionResult:
                """🎯 智能下拉框选择"""
                try:
                    logger.info(f"🎯 启动智能下拉框选择: index={index}, target='{target_value}'")
                    
                    # 获取数字人信息
                    digital_human_info = self._get_digital_human_info_safely()
                    
                    # 获取页面和元素
                    page = await browser.get_current_page()
                    selector_map = await browser.get_selector_map()
                    
                    if index >= len(selector_map):
                        return ActionResult(error=f"Element index {index} out of range")
                    
                    target_element = selector_map[index]
                    
                    # 智能选项匹配
                    if self.dropdown_engine:
                        matching_result = self.dropdown_engine.intelligent_option_matching(target_value)
                        if matching_result.get('matches'):
                            for match_value in matching_result['matches']:
                                try:
                                    # 尝试选择匹配的选项
                                    await page.select_option(f"xpath={target_element.xpath}", value=match_value)
                                    logger.info(f"✅ 智能下拉选择成功: {match_value}")
                                    return ActionResult(
                                        extracted_content=f"Successfully selected: {match_value}",
                                        include_in_memory=True
                                    )
                                except Exception as select_error:
                                    logger.debug(f"⚠️ 选择 {match_value} 失败: {select_error}")
                                    continue
                    
                    # 回退到基础选择
                    if target_value:
                        await page.select_option(f"xpath={target_element.xpath}", value=target_value)
                        return ActionResult(
                            extracted_content=f"Selected: {target_value}",
                            include_in_memory=True
                        )
                    else:
                        return ActionResult(error="No target value provided for dropdown selection")
                        
                except Exception as e:
                    logger.error(f"❌ 智能下拉框选择失败: {e}")
                    return ActionResult(error=f"Dropdown selection failed: {str(e)}")
            
            logger.info("✅ 智能下拉框引擎注册完成")
            
        except Exception as e:
            logger.error(f"❌ 智能下拉框引擎注册失败: {e}")
    
    # 将方法绑定到controller实例
    import types
    controller.register_intelligent_dropdown_engine = types.MethodType(register_intelligent_dropdown_engine, controller)
    
    logger.info("✅ CustomController修复补丁已应用")
    return controller 