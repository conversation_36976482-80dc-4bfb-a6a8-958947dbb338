"""
🔍 启动人类视觉模拟智能问卷系统
"""

import asyncio
import logging
import json

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def start_system():
    """启动系统"""
    
    print("""
╔══════════════════════════════════════════════════════════════════════════════════╗
║                  🔍 人类视觉模拟智能问卷系统 v1.0                                  ║
║                                                                                  ║
║  🎯 核心功能：                                                                    ║
║    • 第一屏优先扫描 - 防止忽略高匹配度选项                                       ║
║    • 全局记忆机制 - 滚动过程中记住所有选项                                       ║
║    • 断崖式高分识别 - 智能识别最佳匹配选项                                       ║
║    • 人类行为模拟 - 完美模拟人类的扫描、滚动、选择行为                           ║
║                                                                                  ║
║  ✅ 解决的问题：                                                                  ║
║    ❌ 第一屏「中国人」被忽略 → ✅ 第一屏优先，高分直接选择                        ║
║    ❌ 缺乏全局记忆 → ✅ 智能滚动，保持所有选项记忆                               ║
║    ❌ 评分不准确 → ✅ 断崖式评分算法，准确识别最佳匹配                           ║
║                                                                                  ║
╚══════════════════════════════════════════════════════════════════════════════════╝
    """)
    
    try:
        # 测试人类视觉模拟引擎
        from intelligent_nationality_region_engine import IntelligentNationalityRegionEngine
        
        engine = IntelligentNationalityRegionEngine()
        
        # 数字人信息
        digital_human_info = {
            'name': '张小雅',
            'nationality': '中国',
            'location': '中国上海',
            'residence': '上海市'
        }
        
        logger.info(f"👤 数字人信息: {json.dumps(digital_human_info, ensure_ascii=False)}")
        
        # 测试评分
        test_options = ["中国人", "Chinese", "华人", "其他"]
        logger.info("🧠 测试智能评分:")
        for option in test_options:
            score = engine._calculate_smart_matching_score(option, "中国")
            confidence = "高" if score >= 0.8 else "中" if score >= 0.5 else "低"
            logger.info(f"  '{option}' - 评分: {score:.3f} (置信度: {confidence})")
        
        logger.info("✅ 人类视觉模拟引擎已就绪！")
        logger.info("🚀 可以开始处理问卷页面了")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 系统启动失败: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(start_system()) 