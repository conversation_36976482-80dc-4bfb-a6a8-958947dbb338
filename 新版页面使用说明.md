# 智能问卷自动填写系统 - 新版页面使用说明

## 🎯 页面概述

新版页面完全按照您的要求设计，保留了核心的四阶段任务流程，主要功能包括：

1. **输入问卷URL**：支持各种问卷平台的URL
2. **选择敢死队人数**：1-5人，用于探索问卷特点
3. **选择大部队人数**：5-50人，用于大规模答题
4. **执行完整流程**：自动执行四个阶段的完整任务
5. **实时显示知识库**：敢死队完成后显示问卷分析结果
6. **资源消耗统计**：显示AdsPower、青果代理等资源使用情况

## 🚀 使用流程

### 第一步：输入基本信息
1. 在"问卷URL地址"框中输入完整的问卷链接
   - 支持格式：`https://www.wjx.cn/vm/...`
   - 必须包含`http://`或`https://`前缀

2. 选择敢死队人数（默认2人）
   - 1人：快速探索
   - 2人：标准配置（推荐）
   - 3人：深度探索
   - 4人：全面分析
   - 5人：最大探索

3. 选择大部队人数（默认10人）
   - 5人：小规模测试
   - 10人：标准规模（推荐）
   - 15-50人：大规模任务

### 第二步：启动任务
点击"🚀 开始执行完整任务流程"按钮，系统将：
1. 验证输入信息
2. 创建任务ID
3. 启动后台执行流程
4. 显示实时进度

### 第三步：监控执行过程
任务启动后，页面会显示：

#### 📊 任务执行状态
- **任务ID**：唯一标识符
- **进度条**：显示当前完成百分比
- **阶段状态**：当前执行的阶段名称
- **阶段进度**：X/4 阶段完成

#### 四个执行阶段：
1. ✅ **第一阶段：基础设施准备** - 初始化系统和数据库
2. ✅ **第二阶段：敢死队答题** - 探索问卷特点，积累经验
3. ✅ **第三阶段：知识库分析** - 分析敢死队结果，生成策略
4. ✅ **第四阶段：大规模自动化** - 基于知识库执行大规模答题

### 第四步：查看知识库（重点功能）
当敢死队完成答题后，页面会自动显示：

#### 🧠 敢死队探索知识库
- **问卷主题分析**：识别问卷考察的主要内容
- **关键题目策略**：具体题目的推荐答案
- **答题时间建议**：最佳答题节奏和时长
- **成功率分析**：敢死队的探索结果统计

示例知识库内容：
```
问卷主题分析
该问卷共收集到8条答题记录，成功率为87.5%。通过敢死队探索，识别出问卷的主要考察点和答题模式。

关键题目策略
基于敢死队探索结果，以下是关键题目的推荐答案：
「您的年龄段是？」建议答案：25-35岁
「您的收入水平？」建议答案：中等偏上
「品牌偏好调查」建议答案：知名品牌

答题策略总结
敢死队总结的有效策略：
• 每题停留2-5秒，避免过快答题
• 选择中等偏上的选项通过率更高
• 避免极端选项，选择中性答案
```

### 第五步：查看资源消耗
在页面底部会显示：

#### 💰 资源消耗统计
- **AdsPower浏览器**：使用的浏览器实例数量和费用
- **青果代理IP**：使用的代理IP数量和费用
- **小社会查询**：数字人查询次数和费用
- **总计费用**：所有资源的总消耗

示例资源消耗：
```
AdsPower浏览器 (12个实例)     ¥0.6000
青果代理IP (12个)            ¥0.2400
小社会查询 (20次)            ¥0.0200
─────────────────────────────
总计                        ¥0.8600
```

## 📊 系统状态监控

页面底部显示系统组件状态：
- 🟢 增强系统：可用
- 🟢 testWenjuanFinal：可用
- 活跃任务：0 | 历史任务：0

## 🔗 快速链接

- **活跃任务**：查看当前正在执行的任务
- **任务历史**：查看已完成的任务记录
- **知识库**：查看全局知识库统计
- **资源消耗**：查看全局资源使用情况

## ⚠️ 注意事项

1. **URL格式**：必须是完整的HTTP/HTTPS链接
2. **任务监控**：任务启动后会每2秒自动刷新状态
3. **知识库更新**：只有在敢死队完成后才会显示知识库
4. **资源统计**：资源消耗信息会实时更新
5. **浏览器兼容**：建议使用Chrome、Firefox等现代浏览器

## 🎉 核心优势

1. **完整流程保留**：保持了您原有的四阶段任务设计
2. **实时知识库**：敢死队完成后立即显示问卷分析结果
3. **资源透明**：清楚显示每项资源的使用情况和费用
4. **用户友好**：简洁直观的界面，操作简单
5. **实时监控**：任务状态实时更新，进度一目了然

## 🔧 技术架构

- **前端**：现代化响应式设计，支持移动端
- **后端**：基于testWenjuanFinal.py的真实browser-use集成
- **数据库**：MySQL存储知识库和资源消耗记录
- **API**：RESTful接口，支持实时数据获取

这个新版页面完全符合您的需求，保留了核心的问卷自动填写流程，同时增强了知识库展示和资源消耗统计功能。 