#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AdsPower增强资源清理集成补丁
解决配置文件无法从AdsPower应用列表中完全移除的问题

修复要点：
1. 修复"User_id is not open"错误处理 - 这是正常状态
2. 实现完整的两步骤清理：停止浏览器 + 删除配置文件
3. 即使停止失败也要尝试删除配置文件
4. 保护智能答题功能不受影响
"""

import asyncio
import logging

# 尝试导入增强版资源管理器
try:
    from adspower_enhanced_resource_manager import EnhancedAdsPowerResourceManager
    ENHANCED_CLEANUP_AVAILABLE = True
    logger = logging.getLogger(__name__)
    logger.info("✅ 增强版AdsPower资源管理器已加载")
except ImportError:
    ENHANCED_CLEANUP_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("⚠️ 增强版AdsPower资源管理器未找到")

def monkey_patch_adspower_cleanup():
    """
    猴子补丁：为AdsPowerResourceManager添加增强清理功能
    """
    try:
        # 尝试导入主模块
        import adspower_browser_use_integration
        
        # 获取原始类
        AdsPowerResourceManager = adspower_browser_use_integration.AdsPowerResourceManager
        
        # 保存原始方法
        original_cleanup = AdsPowerResourceManager.cleanup_adspower_resources
        original_stop_browser = AdsPowerResourceManager._stop_browser
        original_delete_profile = AdsPowerResourceManager._delete_profile
        
        async def enhanced_cleanup_adspower_resources(self, profile_id: str, completion_result: dict) -> dict:
            """增强版清理方法"""
            try:
                should_cleanup = completion_result.get("should_cleanup", False)
                success_type = completion_result.get("success_type", "unknown")
                persona_name = completion_result.get("persona_name", "未知数字人")
                
                self.logger.info(f"🧹 AdsPower资源清理评估 (增强版):")
                self.logger.info(f"   配置文件ID: {profile_id}")
                self.logger.info(f"   完成类型: {success_type}")
                self.logger.info(f"   建议清理: {should_cleanup}")
                
                # 🔒 智能答题保护
                if success_type in ["incomplete_with_errors", "incomplete_in_progress", "uncertain"]:
                    self.logger.info("🔄 保留AdsPower浏览器供手动操作")
                    self.logger.info("🛡️ 答题保护模式：保持浏览器连接以确保智能答题功能正常")
                    return {
                        "cleanup_performed": False,
                        "browser_stopped": False,
                        "profile_deleted": False,
                        "full_cleanup": False,
                        "reason": f"答题{success_type}，保留浏览器供手动确认",
                        "connection_protected": True,
                        "intelligent_answering_preserved": True
                    }
                
                if should_cleanup:
                    self.logger.info("🚀 开始AdsPower增强版两步骤清理流程...")
                    
                    # 🔥 尝试使用增强版资源管理器
                    if ENHANCED_CLEANUP_AVAILABLE:
                        try:
                            self.logger.info("✨ 使用增强版AdsPower资源管理器进行彻底清理")
                            enhanced_manager = EnhancedAdsPowerResourceManager()
                            enhanced_result = await enhanced_manager.complete_cleanup_adspower_profile(
                                profile_id=profile_id,
                                persona_name=persona_name
                            )
                            
                            # 转换增强版结果格式为标准格式
                            cleanup_result = {
                                "cleanup_performed": True,
                                "browser_stopped": enhanced_result.get("browser_stopped", False),
                                "profile_deleted": enhanced_result.get("profile_deleted", False),
                                "full_cleanup": enhanced_result.get("fully_released", False),
                                "reason": f"答题{success_type}，使用增强版两步骤资源释放",
                                "profile_removed_from_list": enhanced_result.get("profile_deleted", False),
                                "enhanced_cleanup_used": True,
                                "method": "enhanced_two_step_cleanup"
                            }
                            
                            if cleanup_result["full_cleanup"]:
                                self.logger.info("✅ 增强版AdsPower资源两步骤清理完全成功")
                                self.logger.info("🎯 配置文件已从AdsPower应用列表中完全移除")
                                self.logger.info("💾 浏览器额度已释放")
                            else:
                                self.logger.warning("⚠️ 增强版AdsPower资源清理部分失败")
                                
                            return cleanup_result
                            
                        except Exception as enhanced_error:
                            self.logger.warning(f"⚠️ 增强版清理失败: {enhanced_error}")
                            self.logger.info("🔄 回退到原始清理流程...")
                    
                    # 回退到原始方法
                    return await original_cleanup(self, profile_id, completion_result)
                
                else:
                    self.logger.info("🔄 保留AdsPower浏览器供手动操作")
                    self.logger.info("🛡️ 连接保护：维持浏览器状态以支持后续智能答题")
                    return {
                        "cleanup_performed": False,
                        "browser_stopped": False,
                        "profile_deleted": False,
                        "full_cleanup": False,
                        "reason": f"答题{success_type}，保留浏览器供手动确认",
                        "intelligent_answering_preserved": True
                    }
                    
            except Exception as e:
                self.logger.error(f"❌ 增强版AdsPower资源清理失败: {e}")
                # 回退到原始方法
                return await original_cleanup(self, profile_id, completion_result)
        
        async def enhanced_stop_browser(self, profile_id: str) -> bool:
            """增强版停止浏览器方法 - 正确处理'User_id is not open'"""
            try:
                import requests
                
                self.logger.info(f"⏹️ 停止AdsPower浏览器实例: {profile_id}")
                
                url = f"{self.adspower_base_url}/browser/stop"
                params = {"user_id": profile_id}
                
                response = requests.get(url, params=params, timeout=15)
                self.logger.info(f"📊 API响应状态: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    self.logger.info(f"📋 API响应内容: {result}")
                    
                    if result.get("code") == 0:
                        self.logger.info("✅ 浏览器停止成功")
                        return True
                    elif result.get("code") == -1 and "User_id is not open" in result.get("msg", ""):
                        # 🔑 关键修复：这不是错误，是正常状态
                        self.logger.info("ℹ️ 浏览器已处于关闭状态（正常状态）")
                        self.logger.info("ℹ️ 'User_id is not open' 表示浏览器已关闭，可以继续删除配置文件")
                        return True
                    else:
                        self.logger.warning(f"⚠️ 浏览器停止失败: {result.get('msg', '未知错误')}")
                        return False
                else:
                    self.logger.warning(f"⚠️ API请求失败，状态码: {response.status_code}")
                    return False
                    
            except Exception as e:
                self.logger.warning(f"⚠️ 停止浏览器异常: {e}")
                self.logger.info("ℹ️ 浏览器可能已被手动关闭，这不影响配置文件删除")
                return False
        
        # 应用补丁
        AdsPowerResourceManager.cleanup_adspower_resources = enhanced_cleanup_adspower_resources
        AdsPowerResourceManager._stop_browser = enhanced_stop_browser
        
        logger.info("✅ AdsPower增强清理补丁已应用")
        return True
        
    except Exception as e:
        logger.error(f"❌ 补丁应用失败: {e}")
        return False

# 自动应用补丁
if __name__ != "__main__":
    monkey_patch_adspower_cleanup()
