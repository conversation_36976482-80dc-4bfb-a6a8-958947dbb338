#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔥 修复版五层融合智能问卷系统 - 完整启动脚本
解决了WebSocket连接问题，确保系统正常运行
"""

import asyncio
import json
import logging
import sys
import time
from typing import Dict, Any, Optional

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('fixed_five_layer_system.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class FixedFiveLayerQuestionnaireSystem:
    """🔥 修复版五层融合智能问卷系统主控制器"""
    
    def __init__(self):
        self.lifecycle_manager = None
        self.integration_system = None
        self.current_browser_env = None
        
    async def initialize_system(self):
        """🚀 初始化修复版五层融合系统"""
        try:
            # 导入依赖模块
            from enhanced_adspower_lifecycle import AdsPowerLifecycleManager
            from adspower_browser_use_integration_fixed import FixedAdsPowerBrowserUseIntegration
            
            self.lifecycle_manager = AdsPowerLifecycleManager()
            
            # 检查AdsPower服务状态
            if not await self.lifecycle_manager.check_service_status():
                logger.error("❌ AdsPower服务未运行，请先启动AdsPower")
                return False
            
            logger.info("✅ 修复版五层融合系统初始化完成")
            return True
            
        except ImportError as e:
            logger.error(f"❌ 模块导入失败: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ 系统初始化失败: {e}")
            return False
    
    async def run_complete_questionnaire(self, 
                                      questionnaire_url: str,
                                      digital_human_info: Dict[str, Any],
                                      max_execution_time: int = 1800) -> Dict[str, Any]:
        """🔥 运行完整的修复版五层融合问卷系统"""
        
        start_time = time.time()
        result = {
            'success': False,
            'execution_time': 0,
            'questionnaire_url': questionnaire_url,
            'digital_human': digital_human_info.get('name', '未知'),
            'error_message': '',
            'details': {},
            'websocket_fix_applied': True  # 标记已应用WebSocket修复
        }
        
        try:
            logger.info("🔥 启动修复版五层融合智能问卷系统")
            logger.info(f"🎯 问卷链接: {questionnaire_url}")
            logger.info(f"👤 数字人: {digital_human_info.get('name', '未知')}")
            logger.info("🔧 已应用WebSocket连接修复")
            
            # 第一阶段：创建浏览器环境
            logger.info("📍 第一阶段：创建AdsPower浏览器环境")
            
            persona_id = digital_human_info.get('id', int(time.time()) % 1000)
            persona_name = digital_human_info.get('name', 'TestPerson')
            
            self.current_browser_env = await self.lifecycle_manager.create_complete_browser_environment(
                persona_id=persona_id,
                persona_name=persona_name
            )
            
            if not self.current_browser_env or not self.current_browser_env.get('success'):
                result['error_message'] = "浏览器环境创建失败"
                return result
            
            profile_id = self.current_browser_env['profile_id']
            debug_port = self.current_browser_env['debug_port']
            
            logger.info(f"✅ 浏览器环境创建成功: {profile_id}")
            logger.info(f"🔗 Debug端口: {debug_port}")
            
            # 第二阶段：初始化修复版五层融合系统
            logger.info("📍 第二阶段：初始化修复版五层融合集成系统")
            
            from adspower_browser_use_integration_fixed import FixedAdsPowerBrowserUseIntegration
            
            self.integration_system = FixedAdsPowerBrowserUseIntegration(profile_id)
            
            # 🔑 关键修复：使用新的方法获取正确的WebSocket URL
            correct_ws_url = await self.integration_system._get_correct_websocket_url(str(debug_port))
            if not correct_ws_url:
                result['error_message'] = "无法获取正确的WebSocket URL"
                return result
            
            logger.info(f"✅ 获取到正确的WebSocket URL: {correct_ws_url}")
            
            # 初始化浏览器连接（使用正确的WebSocket URL）
            if not await self.integration_system.initialize_browser(correct_ws_url):
                result['error_message'] = "修复版五层融合系统初始化失败"
                return result
            
            # 创建智能Agent
            if not await self.integration_system.create_intelligent_agent(
                digital_human_info,
                f"完成问卷填写任务: {questionnaire_url}",
                None
            ):
                result['error_message'] = "智能Agent创建失败"
                return result
            
            logger.info("✅ 修复版五层融合系统初始化完成")
            
            # 第三阶段：执行智能问卷
            logger.info("📍 第三阶段：执行修复版五层融合智能问卷")
            
            questionnaire_result = await self.integration_system.run_intelligent_questionnaire(
                questionnaire_url,
                max_execution_time
            )
            
            # 更新结果
            result.update({
                'success': questionnaire_result.get('success', False),
                'details': questionnaire_result,
                'execution_time': time.time() - start_time,
                'websocket_status': 'fixed_and_working'
            })
            
            if questionnaire_result.get('success'):
                logger.info("🎉 修复版五层融合问卷系统执行完成")
                logger.info("✅ WebSocket连接修复已生效")
            else:
                result['error_message'] = questionnaire_result.get('error_message', '未知错误')
                logger.warning(f"⚠️ 问卷执行未完全成功: {result['error_message']}")
            
            return result
            
        except Exception as e:
            result['error_message'] = f"系统执行异常: {e}"
            result['execution_time'] = time.time() - start_time
            logger.error(f"❌ 修复版五层融合系统执行异常: {e}")
            return result
        
        finally:
            # 确保资源清理
            await self.cleanup_resources()
    
    async def cleanup_resources(self):
        """🔧 清理系统资源"""
        try:
            if self.current_browser_env and self.lifecycle_manager:
                profile_id = self.current_browser_env.get('profile_id')
                if profile_id:
                    logger.info("🔧 开始清理浏览器资源...")
                    await self.lifecycle_manager.force_cleanup_browser(
                        profile_id, 
                        "修复版系统完成清理"
                    )
                    logger.info("✅ 资源清理完成")
        except Exception as e:
            logger.warning(f"⚠️ 资源清理异常: {e}")

async def test_websocket_fix_with_real_questionnaire():
    """🔍 使用真实问卷测试WebSocket修复效果"""
    print("🔍 测试WebSocket修复效果 - 使用真实问卷")
    print("=" * 70)
    
    system = FixedFiveLayerQuestionnaireSystem()
    
    if not await system.initialize_system():
        print("❌ 系统初始化失败")
        return False
    
    # 测试数字人信息
    digital_human_info = {
        'id': 6666,
        'name': 'WebSocket修复测试员',
        'age': 28,
        'gender': '女',
        'profession': '产品经理',
        'income': '12000',
        'education': '本科',
        'location': '北京市',
        'interests': ['科技', '阅读', '健身'],
        'personality': '理性消费者'
    }
    
    # 真实问卷URL
    questionnaire_url = 'http://www.jinshengsurveys.com/?type=qtaskgoto&id=31393&token=EAE8C0EC7F66F828D47F753B487D435CA108172CDC98352371611CC8E8036C152943D234D38237BF4A524D0363900563576EC030D48CB7A13DFC735B9999C94D'
    
    # 执行问卷（限制执行时间避免过长）
    result = await system.run_complete_questionnaire(
        questionnaire_url=questionnaire_url,
        digital_human_info=digital_human_info,
        max_execution_time=300  # 5分钟测试
    )
    
    # 输出结果
    print("\n📊 测试结果")
    print("=" * 70)
    print(f"🎯 执行成功: {'✅ 是' if result.get('success') else '❌ 否'}")
    print(f"⏰ 执行时间: {result.get('execution_time', 0):.1f}秒")
    print(f"🔧 WebSocket修复: ✅ 已应用")
    print(f"👤 数字人: {result.get('digital_human', '未知')}")
    
    if result.get('success'):
        details = result.get('details', {})
        print(f"📝 答题数量: {details.get('answers_count', 0)}")
        print(f"📄 页面数量: {details.get('pages_visited', 0)}")
        print(f"✅ 完成状态: {details.get('completion_status', '未知')}")
    else:
        print(f"❌ 错误信息: {result.get('error_message', '未知错误')}")
    
    return result.get('success', False)

async def run_simple_test():
    """🔍 运行简单测试验证系统正常工作"""
    print("🔍 运行简单功能测试")
    print("=" * 50)
    
    system = FixedFiveLayerQuestionnaireSystem()
    
    if not await system.initialize_system():
        print("❌ 系统初始化失败")
        return False
    
    # 简单测试数字人信息
    digital_human_info = {
        'id': 5555,
        'name': '简单测试',
        'age': 25,
        'gender': '男',
        'profession': '测试工程师',
        'income': '10000',
        'education': '本科',
        'location': '上海市',
        'interests': ['测试', '技术'],
        'personality': '细致认真'
    }
    
    # 使用百度首页作为简单测试
    test_url = "https://www.baidu.com"
    
    # 执行简单测试（限制执行时间）
    result = await system.run_complete_questionnaire(
        questionnaire_url=test_url,
        digital_human_info=digital_human_info,
        max_execution_time=60  # 1分钟简单测试
    )
    
    print(f"\n📊 简单测试结果: {'✅ 成功' if result.get('success') else '❌ 失败'}")
    if not result.get('success'):
        print(f"   错误信息: {result.get('error_message', '未知')}")
    
    return result.get('success', False)

async def main():
    """🔥 主入口函数"""
    print("🔥 修复版五层融合智能问卷系统")
    print("=" * 70)
    print("🔧 已修复WebSocket连接问题")
    print("✅ 确保使用正确的Chrome DevTools Protocol端点")
    print("=" * 70)
    
    # 询问用户想要运行哪种测试
    print("\n请选择测试模式:")
    print("1. 简单功能测试 (推荐)")
    print("2. 真实问卷测试")
    print("3. 两个测试都运行")
    
    choice = input("\n请输入选择 (1/2/3): ").strip()
    
    if choice == "1":
        success = await run_simple_test()
        if success:
            print("\n🎉 简单功能测试成功！WebSocket修复生效！")
        else:
            print("\n❌ 简单功能测试失败，需要进一步排查")
    
    elif choice == "2":
        success = await test_websocket_fix_with_real_questionnaire()
        if success:
            print("\n🎉 真实问卷测试成功！WebSocket修复完全生效！")
        else:
            print("\n⚠️ 真实问卷测试需要进一步调试")
    
    elif choice == "3":
        print("\n🔍 运行完整测试套件...")
        
        # 先运行简单测试
        simple_success = await run_simple_test()
        
        if simple_success:
            print("\n✅ 简单测试通过，继续真实问卷测试...")
            await asyncio.sleep(3)  # 等待资源释放
            
            real_success = await test_websocket_fix_with_real_questionnaire()
            
            if real_success:
                print("\n🎉 所有测试都成功！系统完全正常！")
            else:
                print("\n✅ 基础功能正常，真实问卷需要进一步优化")
        else:
            print("\n❌ 基础测试失败，跳过真实问卷测试")
    
    else:
        print("❌ 无效选择，默认运行简单测试")
        await run_simple_test()

if __name__ == "__main__":
    asyncio.run(main())