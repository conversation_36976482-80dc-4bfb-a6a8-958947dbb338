#!/usr/bin/env python3

# 读取文件
with open('adspower_browser_use_integration.py', 'r', encoding='utf-8') as f:
    lines = f.readlines()

# 修复第10851-10853行的缩进问题
lines[10850] = "                        if universal_wait_result.get(\"waited\"):\n"  # 第10851行
lines[10851] = "                            framework = universal_wait_result.get(\"framework\", \"unknown\")\n"  # 第10852行  
lines[10852] = "                            logger.info(f\"✅ {framework}智能等待完成: {universal_wait_result.get('message')}\")\n"  # 第10853行

# 写回文件
with open('adspower_browser_use_integration.py', 'w', encoding='utf-8') as f:
    f.writelines(lines)

print("✅ 最终缩进修复完成")
