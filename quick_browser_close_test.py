#!/usr/bin/env python3
"""
快速测试AdsPower浏览器关闭检测和强制清理功能
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from adspower_browser_use_integration import AdsPowerResourceManager
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_force_cleanup_method():
    """测试强制清理方法是否存在并可调用"""
    try:
        # 创建资源管理器
        resource_manager = AdsPowerResourceManager(logger)
        
        # 检查方法是否存在
        if hasattr(resource_manager, 'force_cleanup_browser_closed_resources'):
            logger.info("✅ force_cleanup_browser_closed_resources 方法存在")
            
            # 测试方法调用（使用虚拟参数）
            test_result = await resource_manager.force_cleanup_browser_closed_resources(
                "test_profile_id", "测试数字人"
            )
            
            logger.info(f"📊 测试调用结果: {test_result}")
            
            # 检查返回结果结构
            required_keys = ["cleanup_performed", "browser_stopped", "profile_deleted", "full_cleanup"]
            missing_keys = [key for key in required_keys if key not in test_result]
            
            if not missing_keys:
                logger.info("✅ 强制清理方法返回结构正确")
                return True
            else:
                logger.warning(f"⚠️ 返回结构缺少字段: {missing_keys}")
                return False
        else:
            logger.error("❌ force_cleanup_browser_closed_resources 方法不存在")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试强制清理方法失败: {e}")
        return False

async def test_browser_status_check():
    """测试浏览器状态检查方法"""
    try:
        from src.agent.browser_use.browser_use_agent import AdsPowerResourceManager as AgentResourceManager
        
        # 创建Agent资源管理器
        agent_manager = AgentResourceManager("test_profile", "http://local.adspower.net:50325")
        
        # 检查状态检查方法是否存在
        if hasattr(agent_manager, '_check_browser_status'):
            logger.info("✅ _check_browser_status 方法存在")
            
            # 测试状态检查
            status = await agent_manager._check_browser_status()
            logger.info(f"📊 浏览器状态检查结果: {status}")
            
            return True
        else:
            logger.error("❌ _check_browser_status 方法不存在")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试浏览器状态检查失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🧪 AdsPower浏览器关闭检测和强制清理功能快速测试")
    print("=" * 60)
    
    # 测试1：强制清理方法
    print("📍 测试1：强制清理方法...")
    test1_result = await test_force_cleanup_method()
    
    # 测试2：浏览器状态检查
    print("\n📍 测试2：浏览器状态检查...")
    test2_result = await test_browser_status_check()
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   强制清理方法: {'✅' if test1_result else '❌'}")
    print(f"   状态检查方法: {'✅' if test2_result else '❌'}")
    
    overall_success = test1_result and test2_result
    print(f"   整体功能: {'✅ 正常' if overall_success else '❌ 异常'}")
    
    if overall_success:
        print("\n🎉 核心功能测试通过！")
        print("✅ 浏览器关闭检测和强制清理功能已就绪")
        print("💡 建议：运行实际问卷任务，手动关闭浏览器测试完整流程")
    else:
        print("\n❌ 核心功能测试失败！")
        print("⚠️ 需要检查代码修复是否正确应用")
    
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main()) 