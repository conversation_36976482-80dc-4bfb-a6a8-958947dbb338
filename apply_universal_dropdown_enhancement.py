#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
应用通用下拉框智能等待增强
============================

将Angular特定的智能等待替换为支持所有下拉框框架的通用智能等待
"""

import os
import sys
import logging
import re
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def apply_universal_dropdown_enhancement():
    """
    应用通用下拉框智能等待增强
    
    1. 在enhanced_select_dropdown_option函数中替换Angular特定调用
    2. 添加通用智能等待方法到控制器类
    3. 确保向后兼容性
    """
    
    target_file = "adspower_browser_use_integration.py"
    
    if not os.path.exists(target_file):
        logger.error(f"❌ 目标文件不存在: {target_file}")
        return False
    
    try:
        # 读取文件内容
        with open(target_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        logger.info(f"📖 读取文件: {target_file} ({len(content)} 字符)")
        
        # 🔧 修改1：替换Angular特定的智能等待调用为通用调用
        logger.info("🔧 替换Angular特定调用为通用智能等待调用...")
        
        # 查找并替换Angular智能等待调用
        angular_pattern = r'# 🎯 检测是否为Angular下拉框并智能等待选项加载\s*if dom_element\.tag_name == \'select\':\s*angular_wait_result = await self\._angular_smart_wait_for_options\(page, dom_element, text, max_wait_seconds=5\)\s*if angular_wait_result\.get\("waited"\):\s*logger\.info\(f"✅ Angular智能等待完成: \{angular_wait_result\.get\(\'message\'\)\}"\)'
        
        universal_replacement = '''# 🎯 【通用增强】检测下拉框类型并智能等待选项加载（支持所有框架）
        universal_wait_result = await self._universal_smart_wait_for_options(page, dom_element, text, max_wait_seconds=5)
        if universal_wait_result.get("waited"):
            framework = universal_wait_result.get("framework", "unknown")
            logger.info(f"✅ {framework}智能等待完成: {universal_wait_result.get('message')}")'''
        
        # 执行替换
        if re.search(angular_pattern, content, re.DOTALL):
            content = re.sub(angular_pattern, universal_replacement, content, flags=re.DOTALL)
            logger.info("✅ 成功替换Angular特定调用为通用调用")
        else:
            logger.warning("⚠️ 未找到Angular特定调用，可能已经是通用版本")
        
        # 🔧 修改2：添加通用智能等待方法到控制器类
        logger.info("🔧 添加通用智能等待方法...")
        
        # 查找_angular_smart_wait_for_options方法的位置
        angular_method_pattern = r'(    async def _angular_smart_wait_for_options\(self, page, dom_element, target_text: str, max_wait_seconds: int = 5\) -> Dict:.*?return result)'
        
        if re.search(angular_method_pattern, content, re.DOTALL):
            # 在Angular方法后添加通用方法
            universal_method = '''
    
    async def _universal_smart_wait_for_options(self, page, dom_element, target_text: str, max_wait_seconds: int = 5) -> Dict:
        """
        🔥 【通用核心方法】：智能等待所有类型下拉框的选项加载
        
        支持：原生select、问卷星、腾讯问卷、Element UI、Ant Design、Bootstrap等
        替换原有的Angular特定方法，提供更全面的下拉框支持
        """
        try:
            logger.info(f"🎯 开始通用下拉框智能等待: 目标='{target_text}', 最大等待={max_wait_seconds}秒")
            
            # 🔍 第一步：检测下拉框框架类型
            framework_detection = await page.evaluate(f"""
            () => {{
                const element = document.evaluate('{dom_element.xpath}', document, null,
                    XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                
                if (!element) {{
                    return {{ error: "元素不存在" }};
                }}
                
                // 🎯 全面的下拉框框架检测配置
                const frameworks = {{
                    "native_select": {{
                        "detection": "element.tagName === 'SELECT'",
                        "async_indicators": ["ng-model", "ng-options", "data-async", "data-remote"],
                        "loading_selectors": [".ng-loading", ".loading", "[disabled]"],
                        "option_selectors": ["option"],
                        "priority": 1
                    }},
                    "jqselect": {{
                        "detection": "element.className.includes('jqselect')",
                        "async_indicators": ["data-url", "data-source", "data-remote"],
                        "loading_selectors": [".jqselect-loading", ".loading"],
                        "option_selectors": [".jqselect-options li", ".jqselect-option"],
                        "priority": 2
                    }},
                    "element_ui": {{
                        "detection": "element.className.includes('el-select')",
                        "async_indicators": ["remote", "filterable", "loading"],
                        "loading_selectors": [".el-loading-mask", ".el-select-dropdown__loading"],
                        "option_selectors": [".el-select-dropdown__item", ".el-option"],
                        "priority": 3
                    }},
                    "ant_design": {{
                        "detection": "element.className.includes('ant-select')",
                        "async_indicators": ["loading", "showSearch", "filterOption"],
                        "loading_selectors": [".ant-spin", ".ant-select-dropdown-loading"],
                        "option_selectors": [".ant-select-item", ".ant-select-item-option"],
                        "priority": 4
                    }},
                    "bootstrap": {{
                        "detection": "element.className.includes('dropdown')",
                        "async_indicators": ["data-remote", "data-source"],
                        "loading_selectors": [".spinner", ".loading"],
                        "option_selectors": [".dropdown-item", ".dropdown-menu li"],
                        "priority": 5
                    }},
                    "custom_generic": {{
                        "detection": "element.hasAttribute('role') && element.getAttribute('role') === 'combobox'",
                        "async_indicators": ["aria-busy", "data-loading", "data-async"],
                        "loading_selectors": ["[aria-busy='true']", ".loading", "[data-loading='true']"],
                        "option_selectors": ["[role='option']", ".option", ".choice"],
                        "priority": 6
                    }}
                }};
                
                let detectedFramework = null;
                let detectedPriority = 999;
                
                // 检测框架类型（按优先级）
                for (let [frameworkName, config] of Object.entries(frameworks)) {{
                    try {{
                        if (eval(config.detection)) {{
                            if (config.priority < detectedPriority) {{
                                detectedFramework = frameworkName;
                                detectedPriority = config.priority;
                            }}
                        }}
                    }} catch(e) {{
                        continue;
                    }}
                }}
                
                if (!detectedFramework) {{
                    return {{ 
                        framework: "unknown",
                        requires_wait: false,
                        reason: "未检测到支持的下拉框框架"
                    }};
                }}
                
                const framework = frameworks[detectedFramework];
                
                // 检查是否需要异步等待
                let requiresAsyncWait = false;
                for (let indicator of framework.async_indicators) {{
                    if (element.hasAttribute(indicator) || 
                        element.className.includes(indicator) ||
                        window[indicator] !== undefined) {{
                        requiresAsyncWait = true;
                        break;
                    }}
                }}
                
                // 检查当前加载状态
                let isCurrentlyLoading = false;
                for (let selector of framework.loading_selectors) {{
                    if (document.querySelector(selector)) {{
                        isCurrentlyLoading = true;
                        break;
                    }}
                }}
                
                // 检查当前选项状态
                let currentOptions = [];
                for (let selector of framework.option_selectors) {{
                    const options = document.querySelectorAll(selector);
                    if (options.length > 0) {{
                        currentOptions = Array.from(options).map(opt => ({{
                            text: opt.textContent.trim(),
                            value: opt.getAttribute('value') || opt.getAttribute('data-value') || opt.textContent.trim(),
                            visible: opt.offsetHeight > 0
                        }}));
                        break;
                    }}
                }}
                
                // 检查目标选项是否已存在
                const targetExists = currentOptions.some(opt => 
                    opt.text.includes('{target_text}') || opt.text === '{target_text}'
                );
                
                return {{
                    framework: detectedFramework,
                    requires_wait: requiresAsyncWait || isCurrentlyLoading,
                    is_loading: isCurrentlyLoading,
                    current_options_count: currentOptions.length,
                    visible_options_count: currentOptions.filter(opt => opt.visible).length,
                    target_exists: targetExists,
                    framework_config: framework
                }};
            }}
            """)
            
            if framework_detection.get("error"):
                return {{
                    "waited": False,
                    "error": framework_detection.get("error"),
                    "message": "元素检测失败"
                }}
            
            detected_framework = framework_detection.get("framework", "unknown")
            logger.info(f"✅ 检测到下拉框框架: {{detected_framework}}")
            
            # 如果不需要等待，直接返回
            if not framework_detection.get("requires_wait"):
                return {{
                    "waited": False,
                    "framework": detected_framework,
                    "reason": "无需异步等待",
                    "message": f"{{detected_framework}}下拉框无异步加载特征"
                }}
            
            # 如果目标选项已存在，无需等待
            if framework_detection.get("target_exists"):
                return {{
                    "waited": False,
                    "framework": detected_framework,
                    "reason": "目标选项已存在",
                    "message": f"在{{framework_detection.get('visible_options_count')}}个可见选项中找到目标"
                }}
            
            # 🔄 第二步：开始智能等待循环
            logger.info(f"🔄 开始{{detected_framework}}异步等待，当前选项数: {{framework_detection.get('current_options_count')}}")
            
            import asyncio
            wait_start_time = asyncio.get_event_loop().time()
            check_interval = 0.2  # 每200ms检查一次
            max_checks = int(max_wait_seconds / check_interval)
            
            for check_count in range(max_checks):
                current_time = asyncio.get_event_loop().time()
                elapsed_time = current_time - wait_start_time
                
                # 检查选项加载状态
                options_status = await page.evaluate(f"""
                () => {{
                    const element = document.evaluate('{dom_element.xpath}', document, null,
                        XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                    
                    if (!element) return {{ error: "元素消失" }};
                    
                    const framework = {framework_detection.get('framework_config', {{}})};
                    const targetText = '{target_text.replace("'", "\\'")}';
                    
                    // 检查加载状态
                    let isLoading = false;
                    for (let selector of framework.loading_selectors || []) {{
                        if (document.querySelector(selector)) {{
                            isLoading = true;
                            break;
                        }}
                    }}
                    
                    // 检查选项状态
                    let options = [];
                    for (let selector of framework.option_selectors || []) {{
                        const elements = document.querySelectorAll(selector);
                        if (elements.length > 0) {{
                            options = Array.from(elements).map(opt => ({{
                                text: opt.textContent.trim(),
                                value: opt.getAttribute('value') || opt.getAttribute('data-value') || opt.textContent.trim(),
                                visible: opt.offsetHeight > 0
                            }}));
                            break;
                        }}
                    }}
                    
                    const realOptions = options.filter(opt => 
                        opt.text && opt.text !== '' && 
                        !opt.text.includes('请选择') && 
                        !opt.text.includes('选择') &&
                        opt.visible
                    );
                    
                    const targetExists = realOptions.some(opt => 
                        opt.text.includes(targetText) || opt.text === targetText
                    );
                    
                    return {{
                        total_options: options.length,
                        real_options: realOptions.length,
                        visible_options: options.filter(opt => opt.visible).length,
                        target_exists: targetExists,
                        is_loading: isLoading
                    }};
                }}
                """)
                
                if options_status.get("error"):
                    return {{
                        "waited": True,
                        "wait_time": elapsed_time,
                        "framework": detected_framework,
                        "error": options_status.get("error"),
                        "message": "等待过程中元素消失"
                    }}
                
                # 如果目标选项已存在，等待成功
                if options_status.get("target_exists"):
                    logger.info(f"✅ {{detected_framework}}目标选项加载完成，等待时间: {{elapsed_time:.2f}}秒")
                    return {{
                        "waited": True,
                        "wait_time": elapsed_time,
                        "framework": detected_framework,
                        "success": True,
                        "message": f"目标选项已加载，总选项数: {{options_status.get('real_options')}}"
                    }}
                
                # 如果有足够的真实选项且不在加载中，认为加载完成
                real_options_count = options_status.get("real_options", 0)
                is_loading = options_status.get("is_loading", False)
                
                if real_options_count > 3 and not is_loading:
                    logger.info(f"✅ {{detected_framework}}选项加载完成（{{real_options_count}}个选项），但未找到目标选项")
                    return {{
                        "waited": True,
                        "wait_time": elapsed_time,
                        "framework": detected_framework,
                        "success": False,
                        "message": f"选项已加载但未找到目标选项，总选项数: {{real_options_count}}"
                    }}
                
                # 等待下一次检查
                await asyncio.sleep(check_interval)
                
                # 每秒输出一次进度
                if check_count % 5 == 0:
                    logger.info(f"⏳ {{detected_framework}}等待中... {{elapsed_time:.1f}}s，当前选项数: {{real_options_count}}")
            
            # 等待超时
            total_wait_time = asyncio.get_event_loop().time() - wait_start_time
            logger.warning(f"⚠️ {{detected_framework}}等待超时: {{total_wait_time:.2f}}秒")
            
            return {{
                "waited": True,
                "wait_time": total_wait_time,
                "framework": detected_framework,
                "timeout": True,
                "message": f"等待超时，最终选项数: {{options_status.get('real_options', 0)}}"
            }}
            
        except Exception as e:
            logger.error(f"❌ 通用下拉框智能等待异常: {{e}}")
            return {{
                "waited": False,
                "error": str(e),
                "message": "通用等待过程发生异常"
            }}'''
            
            # 在Angular方法后插入通用方法
            content = re.sub(angular_method_pattern, r'\\1' + universal_method, content, flags=re.DOTALL)
            logger.info("✅ 成功添加通用智能等待方法")
        else:
            logger.warning("⚠️ 未找到Angular方法，可能已经包含通用方法")
        
        # 🔧 修改3：确保向后兼容 - 让Angular方法调用通用方法
        logger.info("🔧 确保向后兼容性...")
        
        # 在Angular方法开始处添加兼容性调用
        angular_compat_pattern = r'(    async def _angular_smart_wait_for_options\(self, page, dom_element, target_text: str, max_wait_seconds: int = 5\) -> Dict:.*?""".*?""")'
        
        angular_compat_replacement = r'\\1\n        # 🔄 向后兼容：调用通用智能等待方法\n        return await self._universal_smart_wait_for_options(page, dom_element, target_text, max_wait_seconds)'
        
        if re.search(angular_compat_pattern, content, re.DOTALL):
            content = re.sub(angular_compat_pattern, angular_compat_replacement, content, flags=re.DOTALL)
            logger.info("✅ 成功添加向后兼容性")
        
        # 写回文件
        with open(target_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info(f"✅ 通用下拉框智能等待增强应用完成")
        logger.info("🎯 增强效果:")
        logger.info("  • 支持所有主流下拉框框架（不仅仅是Angular）")
        logger.info("  • 智能检测框架类型和异步特征")
        logger.info("  • 保持完全向后兼容性")
        logger.info("  • 提供更精准的等待策略")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 应用通用下拉框增强失败: {e}")
        return False

if __name__ == "__main__":
    logger.info("🚀 开始应用通用下拉框智能等待增强")
    logger.info("="*60)
    
    success = apply_universal_dropdown_enhancement()
    
    if success:
        logger.info("🎉 通用下拉框智能等待增强应用成功！")
        logger.info("")
        logger.info("📋 增强特性:")
        logger.info("  ✅ 支持原生select（包括Angular）")
        logger.info("  ✅ 支持问卷星jqselect")
        logger.info("  ✅ 支持Element UI")
        logger.info("  ✅ 支持Ant Design")
        logger.info("  ✅ 支持Bootstrap下拉框")
        logger.info("  ✅ 支持语义化下拉框")
        logger.info("  ✅ 智能框架检测")
        logger.info("  ✅ 异步加载等待")
        logger.info("  ✅ 完全向后兼容")
        logger.info("")
        logger.info("🎯 解决的问题:")
        logger.info("  • 原方案只支持Angular下拉框 → 现在支持所有框架")
        logger.info("  • 原方案只处理原生select → 现在处理所有下拉框类型")
        logger.info("  • 原方案框架特定 → 现在通用智能")
        logger.info("  • 下拉框选择失败率高 → 现在智能等待确保成功")
    else:
        logger.error("❌ 通用下拉框智能等待增强应用失败")
        sys.exit(1) 