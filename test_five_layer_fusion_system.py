#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔥 五层融合智能问卷系统 - 完整测试脚本
测试所有五层架构功能的集成效果
"""

import asyncio
import json
import logging
import sys
import time
from typing import Dict, Any

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('five_layer_fusion_test.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

async def test_five_layer_fusion_system():
    """🔥 测试五层融合智能问卷系统"""
    
    logger.info("🔥 开始测试五层融合智能问卷系统")
    
    # 🎯 测试配置
    test_config = {
        'profile_id': 'test_profile',  # 测试用AdsPower配置文件ID
        'questionnaire_url': 'https://www.surveymonkey.com/r/example',  # 测试问卷URL
        'digital_human_info': {
            'name': '李小华',
            'age': 32,
            'gender': '女',
            'profession': '市场经理',
            'income': '月收入15000元',
            'location': '上海市',
            'education': '硕士',
            'marital_status': '已婚',
            'interests': ['旅游', '美食', '健身'],
            'lifestyle': '品质生活',
            'consumption_habits': '注重品质'
        },
        'llm_config': {
            'model': 'gpt-4',
            'api_key': 'sk-test-key',  # 请替换为真实API密钥
            'base_url': 'https://api.openai.com/v1',
            'temperature': 0.7,
            'max_tokens': 4000
        },
        'task_description': """
        🎯 五层融合智能问卷测试任务：
        
        请完成这个在线问卷调查，展示五层融合架构的所有能力：
        
        1. 🔥 智能停止决策：只有在真正完成时才停止
        2. 🧠 答题一致性：相同问题必须给出相同答案
        3. 🔧 资源管理：实时监控浏览器状态
        4. 🚀 永不放弃：克服所有技术困难
        5. 🧠 深度集成：场景感知智能推理
        
        测试重点：
        - 多页面问卷的完整答题流程
        - 重复问题的一致性处理
        - 页面加载问题的智能恢复
        - 浏览器关闭的资源清理
        - 完成判断的准确性
        """
    }
    
    try:
        # 🔥 导入五层融合集成系统
        from adspower_browser_use_integration import AdsPowerBrowserUseIntegration
        
        # 🚀 创建集成系统实例
        logger.info("🚀 创建五层融合集成系统实例")
        integration = AdsPowerBrowserUseIntegration(test_config['profile_id'])
        
        # 📊 测试结果收集
        test_results = {
            'browser_startup': False,
            'browser_connection': False,
            'agent_creation': False,
            'questionnaire_execution': False,
            'five_layer_status': {},
            'execution_time': 0,
            'error_messages': []
        }
        
        test_start_time = time.time()
        
        # 🎯 第一阶段：浏览器启动测试
        logger.info("🎯 第一阶段：测试AdsPower浏览器启动")
        try:
            ws_endpoint = await integration.start_browser_session()
            if ws_endpoint:
                test_results['browser_startup'] = True
                logger.info("✅ 浏览器启动测试通过")
            else:
                logger.error("❌ 浏览器启动测试失败")
                test_results['error_messages'].append("浏览器启动失败")
                return test_results
        except Exception as e:
            logger.error(f"❌ 浏览器启动异常: {e}")
            test_results['error_messages'].append(f"浏览器启动异常: {e}")
            return test_results
        
        # 🔧 第二阶段：浏览器连接测试
        logger.info("🔧 第二阶段：测试浏览器连接初始化")
        try:
            if await integration.initialize_browser(ws_endpoint):
                test_results['browser_connection'] = True
                logger.info("✅ 浏览器连接测试通过")
            else:
                logger.error("❌ 浏览器连接测试失败")
                test_results['error_messages'].append("浏览器连接失败")
                return test_results
        except Exception as e:
            logger.error(f"❌ 浏览器连接异常: {e}")
            test_results['error_messages'].append(f"浏览器连接异常: {e}")
            return test_results
        
        # 🧠 第三阶段：五层融合Agent创建测试
        logger.info("🧠 第三阶段：测试五层融合智能Agent创建")
        try:
            if await integration.create_intelligent_agent(
                test_config['digital_human_info'],
                test_config['task_description'],
                test_config['llm_config']
            ):
                test_results['agent_creation'] = True
                logger.info("✅ 五层融合Agent创建测试通过")
                
                # 验证五层架构组件
                if hasattr(integration.agent, 'memory_bank'):
                    logger.info("✅ 第二层：答题一致性保障系统 - 已激活")
                if hasattr(integration.agent, 'resource_manager'):
                    logger.info("✅ 第三层：AdsPower资源智能管理 - 已激活")
                if hasattr(integration.agent, 'never_give_up_mode'):
                    logger.info("✅ 第四层：永不放弃执行引擎 - 已激活")
                
            else:
                logger.error("❌ 五层融合Agent创建测试失败")
                test_results['error_messages'].append("Agent创建失败")
                return test_results
        except Exception as e:
            logger.error(f"❌ Agent创建异常: {e}")
            test_results['error_messages'].append(f"Agent创建异常: {e}")
            return test_results
        
        # 🔥 第四阶段：五层融合问卷执行测试
        logger.info("🔥 第四阶段：测试五层融合问卷执行")
        try:
            execution_result = await integration.run_intelligent_questionnaire(
                test_config['questionnaire_url'],
                max_execution_time=1800  # 30分钟测试时间
            )
            
            if execution_result['success']:
                test_results['questionnaire_execution'] = True
                test_results['five_layer_status'] = execution_result.get('five_layer_status', {})
                logger.info("✅ 五层融合问卷执行测试通过")
                
                # 详细结果分析
                logger.info("📊 执行结果详细分析:")
                logger.info(f"  执行时间: {execution_result['execution_time']:.1f}秒")
                logger.info(f"  答题数量: {execution_result['questions_answered']}")
                logger.info(f"  页面导航: {execution_result['pages_navigated']}")
                logger.info(f"  完成原因: {execution_result['completion_reason']}")
                
                # 五层架构状态分析
                if execution_result.get('five_layer_status'):
                    logger.info("🔥 五层架构执行状态:")
                    for layer, status in execution_result['five_layer_status'].items():
                        logger.info(f"  {layer}: {status}")
                
            else:
                logger.error("❌ 五层融合问卷执行测试失败")
                test_results['error_messages'].append(f"问卷执行失败: {execution_result.get('error_message', '未知错误')}")
                
        except Exception as e:
            logger.error(f"❌ 问卷执行异常: {e}")
            test_results['error_messages'].append(f"问卷执行异常: {e}")
        
        # 🔧 第五阶段：资源清理测试
        logger.info("🔧 第五阶段：测试资源清理")
        try:
            await integration.cleanup_resources()
            logger.info("✅ 资源清理测试通过")
        except Exception as e:
            logger.error(f"❌ 资源清理异常: {e}")
            test_results['error_messages'].append(f"资源清理异常: {e}")
        
        # 📊 计算总执行时间
        test_results['execution_time'] = time.time() - test_start_time
        
        # 🎉 生成测试报告
        await generate_test_report(test_results, test_config)
        
        return test_results
        
    except ImportError as e:
        logger.error(f"❌ 导入五层融合系统失败: {e}")
        logger.info("💡 请确保已正确安装所有依赖项")
        return {'error': f'导入失败: {e}'}
    
    except Exception as e:
        logger.error(f"❌ 测试执行异常: {e}")
        return {'error': f'测试异常: {e}'}

async def generate_test_report(test_results: Dict[str, Any], test_config: Dict[str, Any]):
    """📊 生成详细的测试报告"""
    
    logger.info("📊 生成五层融合系统测试报告")
    
    report = {
        'test_summary': {
            'test_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'total_execution_time': test_results.get('execution_time', 0),
            'digital_human': test_config['digital_human_info']['name'],
            'test_url': test_config['questionnaire_url']
        },
        'component_status': {
            'browser_startup': test_results.get('browser_startup', False),
            'browser_connection': test_results.get('browser_connection', False),
            'agent_creation': test_results.get('agent_creation', False),
            'questionnaire_execution': test_results.get('questionnaire_execution', False)
        },
        'five_layer_architecture': test_results.get('five_layer_status', {}),
        'error_messages': test_results.get('error_messages', []),
        'recommendations': []
    }
    
    # 🎯 生成建议
    if not test_results.get('browser_startup'):
        report['recommendations'].append("检查AdsPower是否正常运行，确认配置文件ID正确")
    
    if not test_results.get('agent_creation'):
        report['recommendations'].append("检查LLM配置，确认API密钥有效")
    
    if test_results.get('error_messages'):
        report['recommendations'].append("查看错误日志，解决具体问题后重新测试")
    
    if test_results.get('questionnaire_execution'):
        report['recommendations'].append("五层融合系统运行正常，可以用于生产环境")
    
    # 📄 保存测试报告
    report_filename = f"five_layer_fusion_test_report_{int(time.time())}.json"
    with open(report_filename, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    logger.info(f"📄 测试报告已保存: {report_filename}")
    
    # 🎉 显示测试总结
    logger.info("🎉 五层融合系统测试总结:")
    logger.info(f"  浏览器启动: {'✅' if report['component_status']['browser_startup'] else '❌'}")
    logger.info(f"  浏览器连接: {'✅' if report['component_status']['browser_connection'] else '❌'}")
    logger.info(f"  Agent创建: {'✅' if report['component_status']['agent_creation'] else '❌'}")
    logger.info(f"  问卷执行: {'✅' if report['component_status']['questionnaire_execution'] else '❌'}")
    
    if report['five_layer_architecture']:
        logger.info("🔥 五层架构状态:")
        for layer, status in report['five_layer_architecture'].items():
            logger.info(f"  {layer}: {status}")
    
    if report['error_messages']:
        logger.info("❌ 错误信息:")
        for error in report['error_messages']:
            logger.info(f"  - {error}")
    
    if report['recommendations']:
        logger.info("💡 改进建议:")
        for rec in report['recommendations']:
            logger.info(f"  - {rec}")

if __name__ == "__main__":
    """🔥 五层融合智能问卷系统测试入口"""
    
    print("🔥 启动五层融合智能问卷系统测试...")
    print("📋 测试内容:")
    print("  1. 🚀 AdsPower浏览器启动")
    print("  2. 🔧 浏览器连接初始化")
    print("  3. 🧠 五层融合Agent创建")
    print("  4. 🔥 智能问卷执行")
    print("  5. 🔧 资源清理")
    print("  6. 📊 测试报告生成")
    print()
    
    # 完整系统测试
    asyncio.run(test_five_layer_fusion_system()) 