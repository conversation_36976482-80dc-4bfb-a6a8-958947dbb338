"""
测试人类视觉模拟国籍选择引擎
验证完美处理长选项列表的能力
"""

import asyncio
import logging
from intelligent_nationality_region_engine import IntelligentNationalityRegionEngine

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_human_vision_nationality_selection():
    """测试人类视觉模拟国籍选择功能"""
    try:
        # 初始化引擎
        engine = IntelligentNationalityRegionEngine()
        
        # 测试各种国籍的智能评分
        test_cases = [
            # 测试中国国籍
            ("中国人", "中国"),
            ("Chinese", "中国"),  
            ("中华人民共和国", "中国"),
            ("华人", "中国"),
            ("汉族", "中国"),
            
            # 测试其他国籍
            ("美国人", "美国"),
            ("American", "美国"),
            ("日本人", "日本"),
            ("Japanese", "日本"),
            ("韩国人", "韩国"),
            ("Korean", "韩国"),
            
            # 测试干扰项
            ("其他", "中国"),
            ("不想回答", "中国"),
            ("欧亚人", "中国"),
            ("亚洲其他", "中国")
        ]
        
        logger.info("🧠 开始测试智能评分算法")
        
        for option_text, target_nationality in test_cases:
            score = engine._calculate_smart_matching_score(option_text, target_nationality)
            
            # 判断是否高分匹配
            is_high_match = score >= 0.8
            match_level = "🎯 高分匹配" if is_high_match else "⚠️ 低分匹配"
            
            logger.info(f"  选项: '{option_text}' | 目标: '{target_nationality}' | 评分: {score:.3f} | {match_level}")
        
        logger.info("✅ 智能评分测试完成")
        
        # 测试记忆功能
        logger.info("🧠 测试选项记忆功能")
        
        # 模拟选项列表
        first_screen_options = [
            {"text": "中国人", "score": 0.95, "screen_position": "first_screen"},
            {"text": "美国人", "score": 0.2, "screen_position": "first_screen"},
            {"text": "日本人", "score": 0.3, "screen_position": "first_screen"}
        ]
        
        new_option = {"text": "中国人", "score": 0.9, "screen_position": "scroll_1"}
        
        # 测试去重功能
        is_duplicate = engine._is_option_already_in_memory(new_option, first_screen_options)
        logger.info(f"  重复检测: {'✅ 正确识别重复' if is_duplicate else '❌ 未识别重复'}")
        
        # 测试新选项
        new_unique_option = {"text": "韩国人", "score": 0.8, "screen_position": "scroll_1"}
        is_unique = not engine._is_option_already_in_memory(new_unique_option, first_screen_options)
        logger.info(f"  新选项检测: {'✅ 正确识别新选项' if is_unique else '❌ 误判为重复'}")
        
        logger.info("✅ 记忆功能测试完成")
        
        # 测试断崖式高分识别
        logger.info("🎯 测试断崖式高分识别")
        
        test_options = [
            "中国人",      # 应该得到最高分 (1.0)
            "华人",        # 应该得到高分 (0.85)
            "亚洲人",      # 应该得到中等分
            "其他",        # 应该得到低分
            "不想回答"     # 应该得到最低分
        ]
        
        scores = []
        for option in test_options:
            score = engine._calculate_smart_matching_score(option, "中国")
            scores.append((option, score))
        
        # 按分数排序
        scores.sort(key=lambda x: x[1], reverse=True)
        
        logger.info("  断崖式评分结果:")
        for i, (option, score) in enumerate(scores):
            logger.info(f"    {i+1}. '{option}' - 评分: {score:.3f}")
        
        # 验证最高分确实是断崖式的
        if len(scores) >= 2:
            highest_score = scores[0][1]
            second_score = scores[1][1]
            score_gap = highest_score - second_score
            
            if score_gap >= 0.1:  # 至少0.1的评分差距
                logger.info(f"  ✅ 断崖式识别成功 - 评分差距: {score_gap:.3f}")
            else:
                logger.warning(f"  ⚠️ 评分差距较小: {score_gap:.3f}")
        
        logger.info("🎊 所有测试完成！人类视觉模拟国籍选择引擎已就绪")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False

async def test_integration_with_controller():
    """测试与控制器的集成"""
    logger.info("🔧 测试引擎集成能力")
    
    # 模拟数字人信息
    digital_human_info = {
        'nationality': '中国',
        'residence': '上海市',
        'location': '中国上海',
        'name': '张小雅'
    }
    
    engine = IntelligentNationalityRegionEngine()
    
    # 测试目标值确定
    target_nationality = "中国"  # 应该根据数字人信息确定为中国
    
    logger.info(f"  数字人信息: {digital_human_info}")
    logger.info(f"  确定的目标国籍: {target_nationality}")
    
    # 测试各种情况下的评分
    test_scenarios = [
        "中国人",
        "Chinese", 
        "华人",
        "亚洲人",
        "其他亚洲种族",
        "其他"
    ]
    
    logger.info("  评分测试结果:")
    for option in test_scenarios:
        score = engine._calculate_smart_matching_score(option, target_nationality)
        confidence = "高" if score >= 0.8 else "中" if score >= 0.5 else "低"
        logger.info(f"    '{option}' -> 评分: {score:.3f} (置信度: {confidence})")
    
    logger.info("✅ 集成测试完成")

async def test_basic_functionality():
    """测试基础功能"""
    logger.info("🧠 开始测试智能评分算法")
    
    # 这里可以添加具体的测试逻辑
    test_cases = [
        ("中国人", "中国", 0.95),
        ("Chinese", "中国", 0.85),
        ("其他", "中国", 0.1)
    ]
    
    for option_text, target, expected_min_score in test_cases:
        # 模拟评分计算
        logger.info(f"测试: '{option_text}' -> 目标: '{target}'")
    
    logger.info("✅ 基础测试完成")

if __name__ == "__main__":
    async def main():
        logger.info("🚀 启动人类视觉模拟国籍选择引擎测试")
        
        # 基础功能测试
        await test_human_vision_nationality_selection()
        
        # 集成测试
        await test_integration_with_controller()
        
        # 基础功能测试
        await test_basic_functionality()
        
        logger.info("🎉 所有测试完成！")
    
    asyncio.run(main()) 