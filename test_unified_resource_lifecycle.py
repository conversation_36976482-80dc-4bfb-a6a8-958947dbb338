# 🔥 统一资源生命周期管理器测试脚本
# 验证所有资源管理问题是否得到解决

import asyncio
import logging
import sys
import time
from typing import Dict, Any

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('unified_resource_test.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

class UnifiedResourceLifecycleTest:
    """统一资源生命周期管理器测试类"""
    
    def __init__(self):
        self.test_results = {}
        self.test_profile_ids = []
    
    async def run_comprehensive_test(self):
        """运行全面的资源管理测试"""
        logger.info("🔥 开始统一资源生命周期管理器全面测试")
        
        test_cases = [
            ("资源注册测试", self.test_resource_registration),
            ("AdsPower两步清理测试", self.test_adspower_two_step_cleanup),
            ("资源状态跟踪测试", self.test_resource_state_tracking),
            ("并发资源管理测试", self.test_concurrent_resource_management),
            ("异常处理测试", self.test_exception_handling),
            ("紧急清理测试", self.test_emergency_cleanup)
        ]
        
        for test_name, test_func in test_cases:
            try:
                logger.info(f"\n🔧 开始测试: {test_name}")
                result = await test_func()
                self.test_results[test_name] = result
                
                if result.get("success"):
                    logger.info(f"✅ {test_name} - 通过")
                else:
                    logger.error(f"❌ {test_name} - 失败: {result.get('error', '未知错误')}")
                    
            except Exception as e:
                logger.error(f"❌ {test_name} - 异常: {e}")
                self.test_results[test_name] = {"success": False, "error": str(e)}
        
        # 生成测试报告
        await self.generate_test_report()
    
    async def test_resource_registration(self) -> Dict[str, Any]:
        """测试资源注册功能"""
        try:
            from adspower_unified_resource_integration_patch import adspower_unified_manager
            
            # 注册测试配置文件
            test_profile_id = f"test_profile_{int(time.time())}"
            test_debug_port = 9222
            test_persona_name = "测试数字人"
            
            resource_key = await adspower_unified_manager.register_profile(
                test_profile_id, 
                test_debug_port, 
                test_persona_name,
                {"test_metadata": "test_value"}
            )
            
            self.test_profile_ids.append(test_profile_id)
            
            # 验证注册结果
            status_report = await adspower_unified_manager.get_status_report()
            
            if resource_key in status_report["resource_details"]:
                resource_info = status_report["resource_details"][resource_key]
                
                return {
                    "success": True,
                    "resource_key": resource_key,
                    "profile_id": test_profile_id,
                    "resource_info": resource_info,
                    "status_report": status_report
                }
            else:
                return {
                    "success": False,
                    "error": "资源注册后未在状态报告中找到"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"资源注册测试失败: {e}"
            }
    
    async def test_adspower_two_step_cleanup(self) -> Dict[str, Any]:
        """测试AdsPower两步清理功能"""
        try:
            from adspower_unified_resource_integration_patch import adspower_unified_manager
            
            # 注册一个测试配置文件
            test_profile_id = f"cleanup_test_{int(time.time())}"
            test_debug_port = 9223
            
            await adspower_unified_manager.register_profile(
                test_profile_id, 
                test_debug_port, 
                "清理测试数字人"
            )
            
            # 执行两步清理
            cleanup_result = await adspower_unified_manager.cleanup_profile_two_step(test_profile_id)
            
            # 验证清理结果
            status_report = await adspower_unified_manager.get_status_report()
            
            return {
                "success": True,
                "profile_id": test_profile_id,
                "cleanup_result": cleanup_result,
                "two_step_executed": cleanup_result.get("stop_success") is not None and cleanup_result.get("delete_success") is not None,
                "status_after_cleanup": status_report
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"两步清理测试失败: {e}"
            }
    
    async def test_resource_state_tracking(self) -> Dict[str, Any]:
        """测试资源状态跟踪功能"""
        try:
            from adspower_unified_resource_integration_patch import adspower_unified_manager
            
            # 注册配置文件
            test_profile_id = f"state_test_{int(time.time())}"
            await adspower_unified_manager.register_profile(test_profile_id, 9224, "状态测试")
            
            # 获取初始状态
            initial_status = await adspower_unified_manager.get_status_report()
            
            # 执行清理
            cleanup_result = await adspower_unified_manager.cleanup_profile_two_step(test_profile_id)
            
            # 获取清理后状态
            final_status = await adspower_unified_manager.get_status_report()
            
            return {
                "success": True,
                "profile_id": test_profile_id,
                "initial_status": initial_status,
                "cleanup_result": cleanup_result,
                "final_status": final_status,
                "state_tracking_working": initial_status != final_status
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"状态跟踪测试失败: {e}"
            }
    
    async def test_concurrent_resource_management(self) -> Dict[str, Any]:
        """测试并发资源管理"""
        try:
            from adspower_unified_resource_integration_patch import adspower_unified_manager
            
            # 创建多个并发任务
            concurrent_tasks = []
            profile_ids = []
            
            for i in range(3):
                profile_id = f"concurrent_test_{i}_{int(time.time())}"
                profile_ids.append(profile_id)
                
                task = adspower_unified_manager.register_profile(
                    profile_id, 
                    9225 + i, 
                    f"并发测试{i}"
                )
                concurrent_tasks.append(task)
            
            # 等待所有注册完成
            registration_results = await asyncio.gather(*concurrent_tasks, return_exceptions=True)
            
            # 并发清理
            cleanup_tasks = []
            for profile_id in profile_ids:
                task = adspower_unified_manager.cleanup_profile_two_step(profile_id)
                cleanup_tasks.append(task)
            
            cleanup_results = await asyncio.gather(*cleanup_tasks, return_exceptions=True)
            
            # 验证结果
            successful_registrations = len([r for r in registration_results if isinstance(r, str)])
            successful_cleanups = len([r for r in cleanup_results if isinstance(r, dict) and r.get("success")])
            
            return {
                "success": True,
                "total_profiles": len(profile_ids),
                "successful_registrations": successful_registrations,
                "successful_cleanups": successful_cleanups,
                "concurrent_handling_working": successful_registrations > 0
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"并发资源管理测试失败: {e}"
            }
    
    async def test_exception_handling(self) -> Dict[str, Any]:
        """测试异常处理能力"""
        try:
            from adspower_unified_resource_integration_patch import adspower_unified_manager
            
            # 测试不存在的配置文件清理
            nonexistent_cleanup = await adspower_unified_manager.cleanup_profile_two_step("nonexistent_profile")
            
            # 测试重复注册
            duplicate_profile_id = f"duplicate_test_{int(time.time())}"
            first_registration = await adspower_unified_manager.register_profile(duplicate_profile_id, 9230, "重复测试1")
            second_registration = await adspower_unified_manager.register_profile(duplicate_profile_id, 9230, "重复测试2")
            
            return {
                "success": True,
                "nonexistent_cleanup": nonexistent_cleanup,
                "first_registration": first_registration,
                "second_registration": second_registration,
                "exception_handling_working": not nonexistent_cleanup.get("success") and isinstance(first_registration, str)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"异常处理测试失败: {e}"
            }
    
    async def test_emergency_cleanup(self) -> Dict[str, Any]:
        """测试紧急清理功能"""
        try:
            from adspower_unified_resource_integration_patch import adspower_unified_manager
            
            # 创建多个资源
            profile_ids = []
            for i in range(2):
                profile_id = f"emergency_test_{i}_{int(time.time())}"
                profile_ids.append(profile_id)
                await adspower_unified_manager.register_profile(profile_id, 9240 + i, f"紧急测试{i}")
            
            # 获取清理前状态
            before_emergency = await adspower_unified_manager.get_status_report()
            
            # 执行紧急清理
            emergency_result = await adspower_unified_manager.emergency_cleanup()
            
            # 获取清理后状态
            after_emergency = await adspower_unified_manager.get_status_report()
            
            return {
                "success": True,
                "profile_ids": profile_ids,
                "before_emergency": before_emergency,
                "emergency_result": emergency_result,
                "after_emergency": after_emergency,
                "emergency_cleanup_working": after_emergency["total_resources"] == 0
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"紧急清理测试失败: {e}"
            }
    
    async def generate_test_report(self):
        """生成测试报告"""
        logger.info("\n" + "="*80)
        logger.info("🔥 统一资源生命周期管理器测试报告")
        logger.info("="*80)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results.values() if r.get("success")])
        failed_tests = total_tests - passed_tests
        
        logger.info(f"📊 测试统计:")
        logger.info(f"   总测试数: {total_tests}")
        logger.info(f"   通过测试: {passed_tests}")
        logger.info(f"   失败测试: {failed_tests}")
        logger.info(f"   通过率: {passed_tests/total_tests*100:.1f}%")
        
        logger.info(f"\n📋 详细结果:")
        for test_name, result in self.test_results.items():
            status = "✅ 通过" if result.get("success") else "❌ 失败"
            logger.info(f"   {test_name}: {status}")
            if not result.get("success"):
                logger.info(f"      错误: {result.get('error', '未知错误')}")
        
        # 关键问题解决验证
        logger.info(f"\n🎯 关键问题解决验证:")
        
        # 验证1：AdsPower两步清理统一应用
        two_step_test = self.test_results.get("AdsPower两步清理测试", {})
        if two_step_test.get("success") and two_step_test.get("two_step_executed"):
            logger.info("   ✅ AdsPower两步清理已统一应用")
        else:
            logger.info("   ❌ AdsPower两步清理未正确实现")
        
        # 验证2：资源状态跟踪
        state_test = self.test_results.get("资源状态跟踪测试", {})
        if state_test.get("success") and state_test.get("state_tracking_working"):
            logger.info("   ✅ 资源状态跟踪正常工作")
        else:
            logger.info("   ❌ 资源状态跟踪存在问题")
        
        # 验证3：并发资源管理
        concurrent_test = self.test_results.get("并发资源管理测试", {})
        if concurrent_test.get("success") and concurrent_test.get("concurrent_handling_working"):
            logger.info("   ✅ 并发资源管理正常工作")
        else:
            logger.info("   ❌ 并发资源管理存在问题")
        
        # 验证4：异常处理
        exception_test = self.test_results.get("异常处理测试", {})
        if exception_test.get("success") and exception_test.get("exception_handling_working"):
            logger.info("   ✅ 异常处理机制正常")
        else:
            logger.info("   ❌ 异常处理机制存在问题")
        
        # 验证5：紧急清理
        emergency_test = self.test_results.get("紧急清理测试", {})
        if emergency_test.get("success") and emergency_test.get("emergency_cleanup_working"):
            logger.info("   ✅ 紧急清理功能正常")
        else:
            logger.info("   ❌ 紧急清理功能存在问题")
        
        logger.info("\n" + "="*80)
        
        if passed_tests == total_tests:
            logger.info("🎉 所有测试通过！统一资源生命周期管理器工作正常")
        else:
            logger.warning(f"⚠️ 有 {failed_tests} 个测试失败，需要进一步修复")
        
        logger.info("="*80)

async def main():
    """主函数"""
    try:
        # 检查依赖
        try:
            from adspower_unified_resource_integration_patch import adspower_unified_manager
            logger.info("✅ 统一资源管理器模块导入成功")
        except ImportError as e:
            logger.error(f"❌ 统一资源管理器模块导入失败: {e}")
            logger.error("请确保 adspower_unified_resource_integration_patch.py 文件存在")
            return
        
        # 运行测试
        test_runner = UnifiedResourceLifecycleTest()
        await test_runner.run_comprehensive_test()
        
    except Exception as e:
        logger.error(f"❌ 测试运行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    asyncio.run(main()) 