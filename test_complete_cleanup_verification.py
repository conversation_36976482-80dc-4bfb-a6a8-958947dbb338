#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AdsPower完全清理功能验证测试
验证选择A方案：关闭浏览器后，立即从AdsPower列表中完全消失，释放所有资源
"""

import asyncio
import logging
import requests
import time
from typing import Dict, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class AdsPowerCompleteCleanupTest:
    """AdsPower完全清理测试类"""
    
    def __init__(self, adspower_host: str = "http://local.adspower.net:50325"):
        self.adspower_host = adspower_host
    
    async def test_complete_cleanup_workflow(self):
        """测试完整的清理工作流"""
        logger.info("🚀 开始测试AdsPower完全清理工作流")
        
        # 1. 创建测试配置文件
        profile_id = await self._create_test_profile()
        if not profile_id:
            logger.error("❌ 测试配置文件创建失败")
            return False
        
        try:
            # 2. 启动浏览器
            browser_info = await self._start_browser(profile_id)
            if not browser_info:
                logger.error("❌ 浏览器启动失败")
                return False
            
            logger.info("✅ 浏览器启动成功，等待3秒...")
            await asyncio.sleep(3)
            
            # 3. 验证浏览器在AdsPower列表中
            logger.info("🔍 验证浏览器在AdsPower列表中...")
            before_cleanup = await self._check_profile_exists(profile_id)
            logger.info(f"清理前配置文件存在: {before_cleanup}")
            
            # 4. 执行完全清理
            logger.info("🧹 开始执行完全清理...")
            cleanup_result = await self._complete_cleanup_adspower_profile(profile_id, "测试数字人")
            
            # 5. 验证清理结果
            logger.info("🔍 验证清理结果...")
            await asyncio.sleep(3)  # 等待清理完成
            
            after_cleanup = await self._check_profile_exists(profile_id)
            logger.info(f"清理后配置文件存在: {after_cleanup}")
            
            # 6. 输出测试结果
            success = cleanup_result["success"] and before_cleanup and not after_cleanup
            
            logger.info("📊 测试结果总结:")
            logger.info(f"   创建配置文件: ✅")
            logger.info(f"   启动浏览器: ✅")
            logger.info(f"   清理前存在: {'✅' if before_cleanup else '❌'}")
            logger.info(f"   浏览器停止: {'✅' if cleanup_result['browser_stopped'] else '❌'}")
            logger.info(f"   配置文件删除: {'✅' if cleanup_result['profile_deleted'] else '❌'}")
            logger.info(f"   清理后消失: {'✅' if not after_cleanup else '❌'}")
            logger.info(f"   整体成功: {'✅' if success else '❌'}")
            
            if success:
                logger.info("🎉 完全清理功能测试成功！")
                logger.info("🎯 浏览器已从AdsPower列表中完全移除")
            else:
                logger.warning("⚠️ 完全清理功能测试部分失败")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ 测试过程中出现异常: {e}")
            # 清理残留资源
            try:
                await self._complete_cleanup_adspower_profile(profile_id, "测试清理")
            except:
                pass
            return False
    
    async def _create_test_profile(self) -> Optional[str]:
        """创建测试配置文件"""
        try:
            logger.info("📝 创建测试配置文件...")
            
            url = f"{self.adspower_host}/api/v1/user/create"
            data = {
                "name": f"完全清理测试_{int(time.time())}",
                "domain_name": "facebook.com",
                "open_tabs": ["https://www.browserscan.net"],
                "username": "test_user",
                "remark": "完全清理功能测试配置文件",
                "fingerprint_config": {
                    "automatic_timezone": 1,
                    "language": ["zh-CN", "zh", "en-US", "en"],
                    "screen_resolution": "1920_1080",
                    "fonts": ["Arial", "Times New Roman", "Helvetica"]
                }
            }
            
            response = requests.post(url, json=data, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            
            if result.get("code") == 0:
                profile_id = result["data"]["id"]
                logger.info(f"✅ 测试配置文件创建成功: {profile_id}")
                return profile_id
            else:
                logger.error(f"❌ 配置文件创建失败: {result.get('msg')}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 创建测试配置文件异常: {e}")
            return None
    
    async def _start_browser(self, profile_id: str) -> Optional[Dict]:
        """启动浏览器"""
        try:
            logger.info(f"🌐 启动测试浏览器: {profile_id}")
            
            url = f"{self.adspower_host}/api/v1/browser/start"
            params = {"user_id": profile_id}
            
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            
            if result.get("code") == 0:
                browser_info = result["data"]
                logger.info(f"✅ 浏览器启动成功，调试端口: {browser_info.get('debug_port')}")
                return browser_info
            else:
                logger.error(f"❌ 浏览器启动失败: {result.get('msg')}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 启动浏览器异常: {e}")
            return None
    
    async def _check_profile_exists(self, profile_id: str) -> bool:
        """检查配置文件是否存在"""
        try:
            url = f"{self.adspower_host}/api/v1/user/list"
            params = {"page": 1, "page_size": 100}
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            
            if result.get("code") == 0:
                profiles = result.get("data", {}).get("list", [])
                exists = any(profile.get("user_id") == profile_id for profile in profiles)
                return exists
            else:
                logger.warning(f"⚠️ 检查配置文件存在性失败: {result.get('msg')}")
                return False
                
        except Exception as e:
            logger.warning(f"⚠️ 检查配置文件存在性异常: {e}")
            return False
    
    async def _complete_cleanup_adspower_profile(self, profile_id: str, persona_name: str) -> Dict:
        """完全清理AdsPower配置文件"""
        try:
            logger.info(f"🚀 开始完全清理AdsPower资源: {persona_name} ({profile_id})")
            
            # 1. 停止浏览器实例
            stop_success = await self._stop_browser_instance(profile_id)
            if stop_success:
                logger.info(f"✅ 浏览器实例已停止: {profile_id}")
                await asyncio.sleep(2)  # 等待停止完成
            else:
                logger.warning(f"⚠️ 浏览器停止失败，继续尝试删除配置文件")
            
            # 2. 删除配置文件（完全清理）
            delete_success = await self._delete_browser_profile(profile_id)
            
            result = {
                "success": stop_success and delete_success,
                "browser_stopped": stop_success,
                "profile_deleted": delete_success,
                "profile_id": profile_id,
                "persona_name": persona_name
            }
            
            if result["success"]:
                logger.info(f"✅ {persona_name} AdsPower资源完全清理成功")
                logger.info(f"🎯 配置文件已从AdsPower应用列表中完全移除")
            else:
                logger.warning(f"⚠️ {persona_name} AdsPower资源清理部分失败")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 完全清理AdsPower资源失败: {e}")
            return {
                "success": False,
                "browser_stopped": False,
                "profile_deleted": False,
                "profile_id": profile_id,
                "persona_name": persona_name,
                "error": str(e)
            }
    
    async def _stop_browser_instance(self, profile_id: str) -> bool:
        """停止AdsPower浏览器实例"""
        try:
            url = f"{self.adspower_host}/api/v1/browser/stop"
            params = {"user_id": profile_id}
            
            logger.info(f"⏹️ 停止浏览器实例: {profile_id}")
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            
            if result.get("code") == 0:
                logger.info("✅ 浏览器实例停止成功")
                return True
            else:
                logger.warning(f"⚠️ 浏览器停止失败: {result.get('msg', '未知错误')}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 停止浏览器实例异常: {e}")
            return False
    
    async def _delete_browser_profile(self, profile_id: str) -> bool:
        """删除AdsPower配置文件（从列表中完全移除）"""
        try:
            url = f"{self.adspower_host}/api/v1/user/delete"
            data = {"user_ids": [profile_id]}
            
            logger.info(f"🗑️ 删除AdsPower配置文件: {profile_id}")
            
            response = requests.post(url, json=data, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            
            if result.get("code") == 0:
                logger.info("✅ AdsPower配置文件删除成功")
                logger.info("🎯 配置文件已从AdsPower应用列表中完全移除")
                return True
            else:
                logger.warning(f"⚠️ 配置文件删除失败: {result.get('msg', '未知错误')}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 删除配置文件异常: {e}")
            return False

async def main():
    """主测试函数"""
    print("🧪 AdsPower完全清理功能验证测试")
    print("=" * 50)
    
    tester = AdsPowerCompleteCleanupTest()
    
    try:
        success = await tester.test_complete_cleanup_workflow()
        
        print("\n" + "=" * 50)
        if success:
            print("🎉 测试结果: 完全清理功能正常工作")
            print("✅ 选择A方案实现成功：关闭浏览器后，立即从AdsPower列表中完全消失")
        else:
            print("❌ 测试结果: 完全清理功能存在问题")
            print("⚠️ 需要检查AdsPower API连接或权限设置")
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现异常: {e}")

if __name__ == "__main__":
    asyncio.run(main()) 