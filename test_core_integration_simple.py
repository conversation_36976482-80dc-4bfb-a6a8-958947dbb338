#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 核心集成简化测试
验证CustomController和系统架构修复的关键功能
"""

import sys
import os
import traceback

# 设置路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_core_integration():
    """🔍 测试核心集成功能"""
    
    print("🚀 开始核心集成测试...")
    
    results = []
    
    # 测试1: CustomController基本功能
    print("\n" + "="*50)
    print("🧪 测试1: CustomController基本功能")
    
    try:
        from src.controller.custom_controller import CustomController
        
        controller = CustomController()
        print("✅ CustomController创建成功")
        
        # 测试数字人信息设置
        test_info = {
            'name': '测试用户',
            'age': 25,
            'gender': '女',
            'profession': '设计师',
            'location': '上海',
            'residence': '中国'
        }
        
        if hasattr(controller, 'set_digital_human_info'):
            controller.set_digital_human_info(test_info)
            print("✅ 数字人信息设置成功")
        else:
            print("⚠️ 缺少set_digital_human_info方法")
        
        results.append(("CustomController基本功能", "PASS"))
        
    except Exception as e:
        print(f"❌ CustomController测试失败: {e}")
        results.append(("CustomController基本功能", "FAIL", str(e)))
    
    # 测试2: 智能引擎方法检查
    print("\n" + "="*50)
    print("🧪 测试2: 智能引擎方法检查")
    
    try:
        # 检查关键方法
        key_methods = [
            'register_intelligent_nationality_region_engine',
            '_is_country_selection_element',
            '_determine_target_nationality'
        ]
        
        missing_methods = []
        for method in key_methods:
            if not hasattr(controller, method):
                missing_methods.append(method)
        
        if missing_methods:
            print(f"⚠️ 缺少方法: {missing_methods}")
            results.append(("智能引擎方法检查", "PARTIAL", f"缺少{len(missing_methods)}个方法"))
        else:
            print("✅ 所有关键方法都存在")
            
            # 测试方法调用
            controller.register_intelligent_nationality_region_engine()
            print("✅ 智能引擎激活成功")
            
            results.append(("智能引擎方法检查", "PASS"))
        
    except Exception as e:
        print(f"❌ 智能引擎测试失败: {e}")
        results.append(("智能引擎方法检查", "FAIL", str(e)))
    
    # 测试3: AdsPowerWebUIIntegration类检查
    print("\n" + "="*50)
    print("🧪 测试3: AdsPowerWebUIIntegration类检查")
    
    try:
        from adspower_browser_use_integration import AdsPowerWebUIIntegration
        
        integrator = AdsPowerWebUIIntegration()
        print("✅ AdsPowerWebUIIntegration创建成功")
        
        # 检查新增方法
        new_methods = [
            '_create_comprehensive_webui_task_prompt',
            '_force_activate_intelligent_engines'
        ]
        
        missing_integration_methods = []
        for method in new_methods:
            if not hasattr(integrator, method):
                missing_integration_methods.append(method)
        
        if missing_integration_methods:
            print(f"⚠️ 缺少集成方法: {missing_integration_methods}")
            results.append(("AdsPowerWebUIIntegration检查", "PARTIAL", f"缺少{len(missing_integration_methods)}个方法"))
        else:
            print("✅ 所有集成方法都存在")
            
            # 测试任务提示词创建
            task_prompt = integrator._create_comprehensive_webui_task_prompt(
                test_info, 
                "https://test.com"
            )
            
            if len(task_prompt) > 100 and '测试用户' in task_prompt:
                print("✅ 任务提示词创建成功")
                results.append(("AdsPowerWebUIIntegration检查", "PASS"))
            else:
                print("⚠️ 任务提示词生成异常")
                results.append(("AdsPowerWebUIIntegration检查", "PARTIAL", "任务提示词异常"))
        
    except Exception as e:
        print(f"❌ AdsPowerWebUIIntegration测试失败: {e}")
        results.append(("AdsPowerWebUIIntegration检查", "FAIL", str(e)))
    
    # 测试4: 国家选择功能测试
    print("\n" + "="*50)
    print("🧪 测试4: 国家选择功能测试")
    
    try:
        # 测试国家选择检测
        test_elements = [
            {'text': '中国', 'tag': 'option'},
            {'text': 'China', 'tag': 'option'},
            {'text': '选择国家', 'tag': 'select'},
            {'text': 'Select Country', 'tag': 'select'}
        ]
        
        country_detections = []
        for element in test_elements:
            try:
                is_country = controller._is_country_selection_element(element)
                country_detections.append(is_country)
            except:
                country_detections.append(False)
        
        print(f"🔍 国家选择检测结果: {country_detections}")
        
        # 测试目标国籍确定
        target_nationality = controller._determine_target_nationality(test_info)
        print(f"🎯 目标国籍: {target_nationality}")
        
        if target_nationality and ('中国' in str(target_nationality) or 'China' in str(target_nationality)):
            print("✅ 国家选择功能正常")
            results.append(("国家选择功能测试", "PASS"))
        else:
            print("⚠️ 国家选择功能可能有问题")
            results.append(("国家选择功能测试", "PARTIAL", f"目标国籍: {target_nationality}"))
        
    except Exception as e:
        print(f"❌ 国家选择功能测试失败: {e}")
        results.append(("国家选择功能测试", "FAIL", str(e)))
    
    # 生成测试报告
    print("\n" + "="*60)
    print("📊 核心集成测试报告")
    print("="*60)
    
    total_tests = len(results)
    passed_tests = len([r for r in results if r[1] == "PASS"])
    partial_tests = len([r for r in results if r[1] == "PARTIAL"])
    failed_tests = len([r for r in results if r[1] == "FAIL"])
    
    success_rate = (passed_tests + partial_tests * 0.5) / total_tests * 100
    
    print(f"📈 总测试数: {total_tests}")
    print(f"✅ 完全通过: {passed_tests}")
    print(f"⚠️ 部分通过: {partial_tests}")
    print(f"❌ 完全失败: {failed_tests}")
    print(f"🎯 综合成功率: {success_rate:.1f}%")
    
    print("\n📋 详细结果:")
    for i, result in enumerate(results, 1):
        if result[1] == "PASS":
            print(f"{i}. ✅ {result[0]}: 完全通过")
        elif result[1] == "PARTIAL":
            print(f"{i}. ⚠️ {result[0]}: 部分通过 - {result[2] if len(result) > 2 else ''}")
        else:
            print(f"{i}. ❌ {result[0]}: 失败 - {result[2] if len(result) > 2 else ''}")
    
    # 系统状态评估
    print("\n" + "="*60)
    print("🔍 系统集成状态评估")
    print("="*60)
    
    if success_rate >= 90:
        print("🎉 系统核心功能完美集成！")
        status = "EXCELLENT"
    elif success_rate >= 75:
        print("✅ 系统核心功能基本集成成功")
        status = "GOOD"
    elif success_rate >= 50:
        print("⚠️ 系统核心功能部分集成")
        status = "PARTIAL"
    else:
        print("❌ 系统核心功能集成存在问题")
        status = "CRITICAL"
    
    # 具体建议
    print("\n🎯 集成状态分析:")
    
    if passed_tests >= 3:
        print("✅ CustomController和智能引擎基本正常")
    if partial_tests > 0:
        print("⚠️ 部分功能需要完善")
    if failed_tests > 0:
        print("❌ 存在需要修复的严重问题")
    
    print(f"\n🏁 最终评估: {status}")
    print(f"📊 综合成功率: {success_rate:.1f}%")
    
    return {
        "status": status,
        "success_rate": success_rate,
        "results": results,
        "summary": {
            "total": total_tests,
            "passed": passed_tests,
            "partial": partial_tests,
            "failed": failed_tests
        }
    }

if __name__ == "__main__":
    try:
        result = test_core_integration()
        
        # 根据结果设置退出码
        if result["success_rate"] >= 75:
            print("\n🎉 核心集成测试基本成功！")
            sys.exit(0)
        else:
            print("\n⚠️ 核心集成测试需要改进")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        print(f"错误详情: {traceback.format_exc()}")
        sys.exit(1) 