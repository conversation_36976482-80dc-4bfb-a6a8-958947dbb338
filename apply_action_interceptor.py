#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
应用Action拦截器补丁
实现100%智能化处理所有click_element_by_index动作
"""

import sys
import logging
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

logger = logging.getLogger(__name__)

def main():
    """主函数：应用Action拦截器补丁"""
    try:
        print("🔥 开始应用Action拦截器补丁...")
        
        # 导入补丁函数
        from action_interceptor_patch import apply_action_interceptor_patch
        
        # 导入CustomController
        from src.controller.custom_controller import CustomController
        
        # 创建CustomController实例
        controller = CustomController()
        
        # 应用补丁
        success = apply_action_interceptor_patch(controller)
        
        if success:
            print("✅ Action拦截器补丁应用成功！")
            print("🎯 现在所有click_element_by_index动作都将经过智能处理")
            print("🧠 智能化特性已激活：")
            print("   - 🗺️ 国家选择智能化")
            print("   - 🗣️ 语言选择智能化") 
            print("   - 👤 个人信息智能化")
            print("   - 📊 态度偏好智能化")
            print("   - 🔄 通用选择智能化")
            print("   - 🛡️ 反作弊策略集成")
            return True
        else:
            print("❌ Action拦截器补丁应用失败")
            return False
            
    except Exception as e:
        print(f"❌ 应用补丁时发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 