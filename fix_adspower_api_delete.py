#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AdsPower API删除配置文件修复脚本
修复主要问题：确保正确调用AdsPower API来完全释放资源
"""

import requests
import logging
import asyncio
from typing import Dict

logger = logging.getLogger(__name__)

class FixedAdsPowerResourceManager:
    """修复版的AdsPower资源管理器"""
    
    def __init__(self, adspower_host: str = "http://local.adspower.net:50325"):
        self.adspower_host = adspower_host
    
    async def complete_cleanup_adspower_profile(self, profile_id: str, persona_name: str = "未知") -> Dict:
        """完全清理AdsPower配置文件 - 修复版"""
        try:
            logger.info(f"🚀 开始完全清理AdsPower资源: {persona_name} ({profile_id})")
            
            # 1. 停止浏览器实例
            stop_success = await self._stop_browser_instance(profile_id)
            if stop_success:
                logger.info(f"✅ 浏览器实例已停止: {profile_id}")
                await asyncio.sleep(2)
            else:
                logger.warning(f"⚠️ 浏览器停止失败，继续尝试删除配置文件")
            
            # 2. 删除配置文件（完全清理）
            delete_success = await self._delete_browser_profile(profile_id)
            
            result = {
                "success": stop_success and delete_success,
                "browser_stopped": stop_success,
                "profile_deleted": delete_success,
                "profile_id": profile_id,
                "persona_name": persona_name
            }
            
            if result["success"]:
                logger.info(f"✅ {persona_name} AdsPower资源完全清理成功")
                logger.info(f"🎯 配置文件已从AdsPower应用列表中完全移除")
            else:
                logger.warning(f"⚠️ {persona_name} AdsPower资源清理部分失败")
                
            return result
            
        except Exception as e:
            logger.error(f"❌ 完全清理AdsPower资源失败: {e}")
            return {
                "success": False,
                "browser_stopped": False,
                "profile_deleted": False,
                "profile_id": profile_id,
                "persona_name": persona_name,
                "error": str(e)
            }
    
    async def _stop_browser_instance(self, profile_id: str) -> bool:
        """停止AdsPower浏览器实例"""
        try:
            url = f"{self.adspower_host}/api/v1/browser/stop"
            params = {"user_id": profile_id}
            
            logger.info(f"⏹️ 停止浏览器实例: {profile_id}")
            logger.info(f"🔗 API请求URL: {url}")
            
            response = requests.get(url, params=params, timeout=10)
            logger.info(f"📊 API响应状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"📋 API响应内容: {result}")
                
                if result.get("code") == 0:
                    logger.info("✅ 浏览器实例停止成功")
                    return True
                else:
                    logger.warning(f"⚠️ 浏览器停止失败: {result.get('msg', '未知错误')}")
                    return False
            else:
                logger.error(f"❌ API请求失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 停止浏览器实例异常: {e}")
            return False
    
    async def _delete_browser_profile(self, profile_id: str) -> bool:
        """删除AdsPower配置文件（从列表中完全移除）"""
        try:
            url = f"{self.adspower_host}/api/v1/user/delete"
            data = {"user_ids": [profile_id]}
            
            logger.info(f"🗑️ 删除AdsPower配置文件: {profile_id}")
            logger.info(f"🔗 API请求URL: {url}")
            
            response = requests.post(url, json=data, timeout=10)
            logger.info(f"📊 API响应状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"📋 API响应内容: {result}")
                
                if result.get("code") == 0:
                    logger.info("✅ AdsPower配置文件删除成功")
                    logger.info("🎯 配置文件已从AdsPower应用列表中完全移除")
                    return True
                else:
                    logger.warning(f"⚠️ 配置文件删除失败: {result.get('msg', '未知错误')}")
                    return False
            else:
                logger.error(f"❌ API请求失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 删除配置文件异常: {e}")
            return False

# 全局实例
fixed_adspower_cleanup = FixedAdsPowerResourceManager()

async def apply_adspower_cleanup_fix(integration_instance, profile_id: str, persona_name: str = "未知") -> Dict:
    """
    应用AdsPower清理修复
    
    参数:
        integration_instance: 原始集成实例
        profile_id: AdsPower配置文件ID
        persona_name: 数字人名称
    
    返回:
        Dict: 清理结果
    """
    logger.info(f"🔧 应用AdsPower资源清理修复: {persona_name}")
    
    # 使用修复版的清理方法
    cleanup_result = await fixed_adspower_cleanup.complete_cleanup_adspower_profile(
        profile_id, persona_name
    )
    
    if cleanup_result["success"]:
        logger.info(f"✅ {persona_name} AdsPower资源修复清理成功")
        logger.info("🎯 现在应该可以在AdsPower应用列表中看不到这个配置文件了")
    else:
        logger.warning(f"⚠️ {persona_name} AdsPower资源修复清理失败")
        logger.warning("⚠️ 配置文件可能仍然在AdsPower应用列表中")
    
    return cleanup_result

if __name__ == "__main__":
    # 测试修复功能
    async def test_fix():
        logging.basicConfig(level=logging.INFO)
        
        # 这里可以测试修复功能
        test_profile_id = "test_profile"
        result = await fixed_adspower_cleanup.complete_cleanup_adspower_profile(
            test_profile_id, "测试数字人"
        )
        
        print(f"测试结果: {result}")
    
    asyncio.run(test_fix()) 