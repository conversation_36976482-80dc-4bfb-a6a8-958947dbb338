#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
通用下拉框智能等待补丁
===================

解决所有类型下拉框的异步加载问题，不仅仅是Angular
支持：原生select、问卷星、腾讯问卷、Element UI、Ant Design、Bootstrap等
"""

import logging

logger = logging.getLogger(__name__)

async def universal_smart_wait_for_options(page, dom_element, target_text: str, max_wait_seconds: int = 5):
    """
    🔥 【通用核心方法】：智能等待所有类型下拉框的选项加载
    
    支持：原生select、问卷星、腾讯问卷、Element UI、Ant Design、Bootstrap等
    """
    try:
        logger.info(f"🎯 开始通用下拉框智能等待: 目标='{target_text}', 最大等待={max_wait_seconds}秒")
        
        # 🔍 检测下拉框框架类型并智能等待
        framework_detection = await page.evaluate(f"""
        () => {{
            const element = document.evaluate('{dom_element.xpath}', document, null,
                XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
            
            if (!element) {{
                return {{ error: "元素不存在" }};
            }}
            
            // 🎯 全面的下拉框框架检测配置
            const frameworks = {{
                "native_select": {{
                    "detection": () => element.tagName === 'SELECT',
                    "async_indicators": ["ng-model", "ng-options", "data-async", "data-remote"],
                    "loading_selectors": [".ng-loading", ".loading", "[disabled]"],
                    "option_selectors": ["option"],
                    "priority": 1
                }},
                "jqselect": {{
                    "detection": () => element.className.includes('jqselect'),
                    "async_indicators": ["data-url", "data-source", "data-remote"],
                    "loading_selectors": [".jqselect-loading", ".loading"],
                    "option_selectors": [".jqselect-options li", ".jqselect-option"],
                    "priority": 2
                }},
                "element_ui": {{
                    "detection": () => element.className.includes('el-select'),
                    "async_indicators": ["remote", "filterable", "loading"],
                    "loading_selectors": [".el-loading-mask", ".el-select-dropdown__loading"],
                    "option_selectors": [".el-select-dropdown__item", ".el-option"],
                    "priority": 3
                }},
                "ant_design": {{
                    "detection": () => element.className.includes('ant-select'),
                    "async_indicators": ["loading", "showSearch", "filterOption"],
                    "loading_selectors": [".ant-spin", ".ant-select-dropdown-loading"],
                    "option_selectors": [".ant-select-item", ".ant-select-item-option"],
                    "priority": 4
                }},
                "bootstrap": {{
                    "detection": () => element.className.includes('dropdown'),
                    "async_indicators": ["data-remote", "data-source"],
                    "loading_selectors": [".spinner", ".loading"],
                    "option_selectors": [".dropdown-item", ".dropdown-menu li"],
                    "priority": 5
                }},
                "custom_generic": {{
                    "detection": () => element.hasAttribute('role') && element.getAttribute('role') === 'combobox',
                    "async_indicators": ["aria-busy", "data-loading", "data-async"],
                    "loading_selectors": ["[aria-busy='true']", ".loading", "[data-loading='true']"],
                    "option_selectors": ["[role='option']", ".option", ".choice"],
                    "priority": 6
                }}
            }};
            
            let detectedFramework = null;
            let detectedPriority = 999;
            
            // 检测框架类型（按优先级）
            for (let [frameworkName, config] of Object.entries(frameworks)) {{
                try {{
                    if (config.detection()) {{
                        if (config.priority < detectedPriority) {{
                            detectedFramework = frameworkName;
                            detectedPriority = config.priority;
                        }}
                    }}
                }} catch(e) {{
                    continue;
                }}
            }}
            
            if (!detectedFramework) {{
                return {{ 
                    framework: "unknown",
                    requires_wait: false,
                    reason: "未检测到支持的下拉框框架"
                }};
            }}
            
            const framework = frameworks[detectedFramework];
            
            // 检查是否需要异步等待
            let requiresAsyncWait = false;
            for (let indicator of framework.async_indicators) {{
                if (element.hasAttribute(indicator) || 
                    element.className.includes(indicator) ||
                    window[indicator] !== undefined) {{
                    requiresAsyncWait = true;
                    break;
                }}
            }}
            
            // 检查当前选项状态
            let currentOptions = [];
            for (let selector of framework.option_selectors) {{
                const options = document.querySelectorAll(selector);
                if (options.length > 0) {{
                    currentOptions = Array.from(options).map(opt => ({{
                        text: opt.textContent.trim(),
                        value: opt.getAttribute('value') || opt.getAttribute('data-value') || opt.textContent.trim(),
                        visible: opt.offsetHeight > 0
                    }}));
                    break;
                }}
            }}
            
            // 检查目标选项是否已存在
            const targetExists = currentOptions.some(opt => 
                opt.text.includes('{target_text}') || opt.text === '{target_text}'
            );
            
            return {{
                framework: detectedFramework,
                requires_wait: requiresAsyncWait,
                current_options_count: currentOptions.length,
                visible_options_count: currentOptions.filter(opt => opt.visible).length,
                target_exists: targetExists,
                framework_config: framework
            }};
        }}
        """)
        
        if framework_detection.get("error"):
            return {
                "waited": False,
                "error": framework_detection.get("error"),
                "message": "元素检测失败"
            }
        
        detected_framework = framework_detection.get("framework", "unknown")
        logger.info(f"✅ 检测到下拉框框架: {detected_framework}")
        
        # 如果不需要等待，直接返回
        if not framework_detection.get("requires_wait"):
            return {
                "waited": False,
                "framework": detected_framework,
                "reason": "无需异步等待",
                "message": f"{detected_framework}下拉框无异步加载特征"
            }
        
        # 如果目标选项已存在，无需等待
        if framework_detection.get("target_exists"):
            return {
                "waited": False,
                "framework": detected_framework,
                "reason": "目标选项已存在",
                "message": f"在{framework_detection.get('visible_options_count')}个可见选项中找到目标"
            }
        
        # 🔄 开始智能等待循环
        logger.info(f"🔄 开始{detected_framework}异步等待，当前选项数: {framework_detection.get('current_options_count')}")
        
        import asyncio
        wait_start_time = asyncio.get_event_loop().time()
        check_interval = 0.2  # 每200ms检查一次
        max_checks = int(max_wait_seconds / check_interval)
        
        for check_count in range(max_checks):
            current_time = asyncio.get_event_loop().time()
            elapsed_time = current_time - wait_start_time
            
            # 检查选项加载状态
            options_status = await page.evaluate(f"""
            () => {{
                const element = document.evaluate('{dom_element.xpath}', document, null,
                    XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                
                if (!element) return {{ error: "元素消失" }};
                
                const targetText = '{target_text.replace("'", "\\'")}';
                
                // 通用选项检测
                const optionSelectors = [
                    'option',  // 原生select
                    '.jqselect-options li', '.jqselect-option',  // 问卷星
                    '.el-select-dropdown__item', '.el-option',  // Element UI
                    '.ant-select-item', '.ant-select-item-option',  // Ant Design
                    '.dropdown-item', '.dropdown-menu li',  // Bootstrap
                    '[role="option"]', '.option', '.choice'  // 通用
                ];
                
                let options = [];
                for (let selector of optionSelectors) {{
                    const elements = document.querySelectorAll(selector);
                    if (elements.length > 0) {{
                        options = Array.from(elements).map(opt => ({{
                            text: opt.textContent.trim(),
                            value: opt.getAttribute('value') || opt.getAttribute('data-value') || opt.textContent.trim(),
                            visible: opt.offsetHeight > 0
                        }}));
                        break;
                    }}
                }}
                
                const realOptions = options.filter(opt => 
                    opt.text && opt.text !== '' && 
                    !opt.text.includes('请选择') && 
                    !opt.text.includes('选择') &&
                    opt.visible
                );
                
                const targetExists = realOptions.some(opt => 
                    opt.text.includes(targetText) || opt.text === targetText
                );
                
                return {{
                    total_options: options.length,
                    real_options: realOptions.length,
                    target_exists: targetExists
                }};
            }}
            """)
            
            if options_status.get("error"):
                return {
                    "waited": True,
                    "wait_time": elapsed_time,
                    "framework": detected_framework,
                    "error": options_status.get("error"),
                    "message": "等待过程中元素消失"
                }
            
            # 如果目标选项已存在，等待成功
            if options_status.get("target_exists"):
                logger.info(f"✅ {detected_framework}目标选项加载完成，等待时间: {elapsed_time:.2f}秒")
                return {
                    "waited": True,
                    "wait_time": elapsed_time,
                    "framework": detected_framework,
                    "success": True,
                    "message": f"目标选项已加载，总选项数: {options_status.get('real_options')}"
                }
            
            # 如果有足够的真实选项，认为加载完成
            real_options_count = options_status.get("real_options", 0)
            if real_options_count > 3:
                logger.info(f"✅ {detected_framework}选项加载完成（{real_options_count}个选项），但未找到目标选项")
                return {
                    "waited": True,
                    "wait_time": elapsed_time,
                    "framework": detected_framework,
                    "success": False,
                    "message": f"选项已加载但未找到目标选项，总选项数: {real_options_count}"
                }
            
            # 等待下一次检查
            await asyncio.sleep(check_interval)
            
            # 每秒输出一次进度
            if check_count % 5 == 0:
                logger.info(f"⏳ {detected_framework}等待中... {elapsed_time:.1f}s，当前选项数: {real_options_count}")
        
        # 等待超时
        total_wait_time = asyncio.get_event_loop().time() - wait_start_time
        logger.warning(f"⚠️ {detected_framework}等待超时: {total_wait_time:.2f}秒")
        
        return {
            "waited": True,
            "wait_time": total_wait_time,
            "framework": detected_framework,
            "timeout": True,
            "message": f"等待超时，最终选项数: {options_status.get('real_options', 0)}"
        }
        
    except Exception as e:
        logger.error(f"❌ 通用下拉框智能等待异常: {e}")
        return {
            "waited": False,
            "error": str(e),
            "message": "通用等待过程发生异常"
        }

# 使用说明
if __name__ == "__main__":
    print("🔧 通用下拉框智能等待补丁")
    print("="*50)
    print("📋 支持的下拉框框架:")
    print("  1. ✅ 原生Select（包括Angular）")
    print("  2. ✅ 问卷星jqselect")
    print("  3. ✅ Element UI")
    print("  4. ✅ Ant Design") 
    print("  5. ✅ Bootstrap下拉框")
    print("  6. ✅ 语义化下拉框")
    print()
    print("🎯 核心优势:")
    print("  • 🌍 通用性：支持所有主流下拉框框架")
    print("  • 🧠 智能性：自动检测框架类型和异步特征")
    print("  • ⚡ 高效性：每200ms检查，最快0.2秒完成")
    print("  • 🛡️ 安全性：5秒超时保护，优雅降级")
    print("  • 🔄 兼容性：完全兼容现有系统，零影响")
