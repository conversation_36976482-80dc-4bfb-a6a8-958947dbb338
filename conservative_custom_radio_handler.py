# 🎯 保守的自定义元素检测和处理系统 - 完美融合现有代码
import asyncio
import logging
from typing import Dict

logger = logging.getLogger(__name__)

async def _detect_special_custom_elements(page, dom_element, element_text: str) -> Dict:
    """🔍 保守检测特殊自定义元素 - 轻量级、非侵入式"""
    try:
        detection_result = {
            "needs_special_handling": False,
            "type": "standard",
            "confidence": 0.0,
            "special_attributes": {}
        }
        
        xpath = '//' + dom_element.xpath
        
        # 快速轻量级检测 - 只检测最关键的自定义Radio特征
        js_quick_check = f"""
        () => {{
            const element = document.evaluate('{xpath}', document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
            if (!element) return {{ detected: false }};
            
            let confidence = 0;
            const attributes = {{}};
            
            // 检测1: SVG图标 (常见于自定义Radio)
            const hasSvgIcon = element.querySelector('span.fir-icon, .svg-icon, svg') || 
                              element.closest('.element')?.querySelector('span.fir-icon, .svg-icon, svg');
            if (hasSvgIcon) {{
                confidence += 0.4;
                attributes.hasSvgIcon = true;
            }}
            
            // 检测2: 隐藏的Radio输入
            const hiddenRadio = element.querySelector('input[type="radio"].fir-hidden, input.hidden') ||
                               element.closest('.element, .clickableCell')?.querySelector('input[type="radio"]');
            if (hiddenRadio) {{
                confidence += 0.5;
                attributes.hasHiddenRadio = true;
                attributes.radioId = hiddenRadio.id;
                attributes.radioName = hiddenRadio.name;
            }}
            
            // 检测3: 特殊容器类名
            const specialContainer = element.closest('.element.clickableCell, .custom-radio, .radio-wrapper');
            if (specialContainer) {{
                confidence += 0.1;
                attributes.hasSpecialContainer = true;
            }}
            
            return {{
                detected: confidence >= 0.5,  // 只有高置信度才启用特殊处理
                confidence: confidence,
                attributes: attributes
            }};
        }}
        """
        
        result = await page.evaluate(js_quick_check)
        
        if result and result.get("detected", False):
            detection_result.update({
                "needs_special_handling": True,
                "type": "custom_radio",
                "confidence": result.get("confidence", 0.0),
                "special_attributes": result.get("attributes", {})
            })
            logger.info(f"🎯 轻量级检测到自定义Radio: 置信度={result.get('confidence', 0.0):.2f}")
        
        return detection_result
        
    except Exception as e:
        logger.debug(f"🔍 特殊元素检测失败，使用标准处理: {e}")
        return {
            "needs_special_handling": False,
            "type": "standard",
            "confidence": 0.0,
            "special_attributes": {}
        }

async def _handle_special_custom_element_click(page, dom_element, element_text: str, detection_result: Dict) -> Dict:
    """🎯 保守处理特殊自定义元素点击 - 快速失败，不影响主流程"""
    try:
        if detection_result["type"] == "custom_radio":
            return await _handle_custom_radio_click_conservative(
                page, dom_element, element_text, detection_result["special_attributes"]
            )
        
        # 其他类型的特殊元素可以在这里扩展
        return {"success": False, "error": "未知的特殊元素类型"}
        
    except Exception as e:
        logger.debug(f"🔍 特殊元素处理异常，回退到标准流程: {e}")
        return {"success": False, "error": str(e)}

async def _handle_custom_radio_click_conservative(page, dom_element, element_text: str, special_attributes: Dict) -> Dict:
    """🎯 保守的自定义Radio点击处理 - 快速尝试几种策略"""
    try:
        # 策略1: JavaScript直接触发 (最可靠)
        if await _try_javascript_radio_trigger(page, element_text, special_attributes):
            return {"success": True, "strategy": "javascript_trigger"}
        
        # 策略2: 容器点击 (最常用)
        if await _try_container_click(page, dom_element):
            return {"success": True, "strategy": "container_click"}
        
        # 策略3: SVG图标点击 (针对特殊UI)
        if special_attributes.get("hasSvgIcon") and await _try_svg_icon_click(page, dom_element):
            return {"success": True, "strategy": "svg_icon_click"}
        
        # 快速失败，不浪费时间
        return {"success": False, "error": "所有保守策略都失败"}
        
    except Exception as e:
        logger.debug(f"🔍 自定义Radio处理失败: {e}")
        return {"success": False, "error": str(e)}

async def _try_javascript_radio_trigger(page, element_text: str, special_attributes: Dict) -> bool:
    """🎯 尝试JavaScript Radio触发 - 轻量级实现"""
    try:
        radio_id = special_attributes.get("radioId", "")
        radio_name = special_attributes.get("radioName", "")
        
        # 简化的JavaScript触发
        js_script = f"""
        () => {{
            const cleanText = '{element_text}'.trim();
            
            // 方法1: 通过ID直接操作
            if ('{radio_id}') {{
                const radio = document.getElementById('{radio_id}');
                if (radio) {{
                    radio.checked = true;
                    radio.dispatchEvent(new Event('change', {{ bubbles: true }}));
                    return true;
                }}
            }}
            
            // 方法2: 通过name查找并触发
            if ('{radio_name}') {{
                const radios = document.querySelectorAll(`input[name="{radio_name}"]`);
                for (const radio of radios) {{
                    const container = radio.closest('.element, .clickableCell, label');
                    if (container && container.textContent.includes(cleanText)) {{
                        radio.checked = true;
                        radio.dispatchEvent(new Event('change', {{ bubbles: true }}));
                        return true;
                    }}
                }}
            }}
            
            return false;
        }}
        """
        
        result = await page.evaluate(js_script)
        return bool(result)
        
    except Exception as e:
        logger.debug(f"🔍 JavaScript触发失败: {e}")
        return False

async def _try_container_click(page, dom_element) -> bool:
    """🎯 尝试容器点击 - 轻量级实现"""
    try:
        xpath = '//' + dom_element.xpath
        
        # 尝试点击父容器
        container_selectors = [
            f"xpath={xpath}/ancestor::div[contains(@class, 'element')]",
            f"xpath={xpath}/ancestor::div[contains(@class, 'clickableCell')]",
            f"xpath={xpath}/ancestor::label"
        ]
        
        for selector in container_selectors:
            try:
                element = page.locator(selector)
                if await element.count() > 0:
                    await element.first.click()
                    await asyncio.sleep(0.2)  # 短暂等待
                    return True
            except:
                continue
        
        return False
        
    except Exception as e:
        logger.debug(f"🔍 容器点击失败: {e}")
        return False

async def _try_svg_icon_click(page, dom_element) -> bool:
    """🎯 尝试SVG图标点击 - 轻量级实现"""
    try:
        xpath = '//' + dom_element.xpath
        
        # 查找并点击SVG图标
        svg_selectors = [
            f"xpath={xpath}//span[contains(@class, 'fir-icon')]",
            f"xpath={xpath}//svg"
        ]
        
        for selector in svg_selectors:
            try:
                element = page.locator(selector)
                if await element.count() > 0:
                    await element.first.click()
                    await asyncio.sleep(0.2)
                    return True
            except:
                continue
        
        return False
        
    except Exception as e:
        logger.debug(f"🔍 SVG图标点击失败: {e}")
        return False 