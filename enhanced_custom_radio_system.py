# Enhanced Custom Radio Button Handling System
# 增强自定义Radio按钮处理系统 - 完整解决方案

import asyncio
import random
import logging
from typing import Dict, List
from browser_use.browser.context import BrowserContext
from browser_use.agent.views import ActionResult

logger = logging.getLogger(__name__)

class EnhancedCustomRadioSystem:
    """🎯 增强自定义Radio按钮处理系统 - 完整解决方案"""
    
    def __init__(self):
        self.detection_cache = {}
        self.success_strategies = {}
        
    async def apply_anti_detection_behavior(self, page) -> None:
        """🛡️ 应用反检测行为模式"""
        try:
            # 人类化随机延迟
            await asyncio.sleep(random.uniform(0.3, 0.8))
            
            # 模拟人类鼠标移动
            await page.mouse.move(
                random.randint(100, 800), 
                random.randint(100, 600)
            )
            
            # 短暂停顿，模拟思考
            await asyncio.sleep(random.uniform(0.1, 0.3))
            
        except Exception as e:
            logger.debug(f"🔍 反检测行为应用失败: {e}")

    async def analyze_element_comprehensively_v2(
        self, page, dom_element, index: int, element_text: str, element_tag: str
    ) -> Dict:
        """🔍 全面分析元素特征，识别各种特殊类型"""
        try:
            analysis = {
                "is_selection_element": False,
                "is_custom_radio": False,
                "is_custom_checkbox": False,
                "is_dropdown": False,
                "is_button": False,
                "is_input": False,
                "is_svg_interactive": False,
                "special_attributes": {},
                "click_strategy": "standard",
                "confidence": 0.0
            }
            
            # 基础选择元素检测
            analysis["is_selection_element"] = self._is_selection_element(element_text, element_tag)
            
            # 🎯 自定义Radio按钮检测
            custom_radio_indicators = await self._detect_custom_radio_indicators(page, dom_element)
            if custom_radio_indicators["detected"]:
                analysis["is_custom_radio"] = True
                analysis["click_strategy"] = "custom_radio"
                analysis["confidence"] = custom_radio_indicators["confidence"]
                analysis["special_attributes"].update(custom_radio_indicators["attributes"])
                logger.info(f"🔍 检测到自定义Radio按钮: 置信度={analysis['confidence']:.2f}")
            
            # 🎯 其他特殊元素检测
            await self._detect_other_special_elements(page, dom_element, analysis)
            
            return analysis
            
        except Exception as e:
            logger.error(f"❌ 元素综合分析失败: {e}")
            return {
                "is_selection_element": self._is_selection_element(element_text, element_tag),
                "click_strategy": "standard",
                "confidence": 0.0
            }

    def _is_selection_element(self, element_text: str, element_tag: str) -> bool:
        """判断是否是选择类型的元素"""
        selection_keywords = [
            "不想回答", "prefer not", "其他", "other", 
            "中国", "china", "美国", "usa", "philippines", "菲律宾",
            "中文", "chinese", "english", "英文", "简体", "繁体",
            "男", "女", "male", "female", "性别"
        ]
        
        selection_tags = ["button", "option", "radio", "checkbox"]
        
        text_matches = any(keyword.lower() in element_text.lower() for keyword in selection_keywords)
        tag_matches = any(tag in element_tag.lower() for tag in selection_tags)
        
        return text_matches or tag_matches

    async def _detect_custom_radio_indicators(self, page, dom_element) -> Dict:
        """🔍 检测自定义Radio按钮指标"""
        try:
            indicators = {
                "detected": False,
                "confidence": 0.0,
                "attributes": {}
            }
            
            xpath = '//' + dom_element.xpath
            
            # 检测特征的JavaScript
            js_check = f"""
            () => {{
                const element = document.evaluate('{xpath}', document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                if (!element) return {{"detected": false}};
                
                const result = {{
                    detected: false,
                    confidence: 0.0,
                    attributes: {{}}
                }};
                
                // 检测特征1: SVG图标
                const svgIcon = element.querySelector('span.fir-icon, .svg-icon, svg') || 
                               element.closest('.element')?.querySelector('span.fir-icon, .svg-icon, svg');
                if (svgIcon) {{
                    result.confidence += 0.3;
                    result.attributes.hasSvgIcon = true;
                }}
                
                // 检测特征2: 隐藏的radio input
                const hiddenRadio = element.querySelector('input[type="radio"].fir-hidden, input.hidden') ||
                                  element.closest('.element, .clickableCell')?.querySelector('input[type="radio"]');
                if (hiddenRadio) {{
                    result.confidence += 0.4;
                    result.attributes.hasHiddenRadio = true;
                    result.attributes.radioId = hiddenRadio.id;
                    result.attributes.radioName = hiddenRadio.name;
                }}
                
                // 检测特征3: 特殊容器类名
                const specialContainer = element.closest('.element.clickableCell, .custom-radio, .radio-wrapper');
                if (specialContainer) {{
                    result.confidence += 0.2;
                    result.attributes.hasSpecialContainer = true;
                }}
                
                // 检测特征4: Label关联
                const label = element.tagName === 'LABEL' ? element : element.closest('label');
                if (label && label.getAttribute('for')) {{
                    result.confidence += 0.1;
                    result.attributes.hasLabelFor = label.getAttribute('for');
                }}
                
                result.detected = result.confidence >= 0.3;
                return result;
            }}
            """
            
            result = await page.evaluate(js_check)
            return result or {"detected": False, "confidence": 0.0, "attributes": {}}
            
        except Exception as e:
            logger.error(f"❌ 自定义Radio指标检测失败: {e}")
            return {"detected": False, "confidence": 0.0, "attributes": {}}

    async def _detect_other_special_elements(self, page, dom_element, analysis: Dict) -> None:
        """🔍 检测其他特殊元素类型"""
        try:
            xpath = '//' + dom_element.xpath
            
            js_check = f"""
            () => {{
                const element = document.evaluate('{xpath}', document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                if (!element) return {{}};
                
                const result = {{}};
                
                if (element.tagName === 'SELECT' || element.querySelector('select')) {{
                    result.isDropdown = true;
                }}
                
                if (element.tagName === 'BUTTON' || element.type === 'submit' || element.type === 'button') {{
                    result.isButton = true;
                }}
                
                if (element.tagName === 'INPUT' && ['text', 'email', 'password', 'tel'].includes(element.type)) {{
                    result.isInput = true;
                }}
                
                if (element.tagName === 'SVG' || element.querySelector('svg')) {{
                    result.isSvgInteractive = true;
                }}
                
                return result;
            }}
            """
            
            result = await page.evaluate(js_check)
            if result:
                analysis.update(result)
                
        except Exception as e:
            logger.debug(f"🔍 其他特殊元素检测失败: {e}")

    async def execute_enhanced_element_click(
        self, page, dom_element, index: int, element_text: str, analysis: Dict
    ) -> Dict:
        """🎯 执行增强的元素点击策略"""
        try:
            click_strategy = analysis.get("click_strategy", "standard")
            
            if click_strategy == "custom_radio":
                return await self._execute_custom_radio_click_strategies(
                    page, dom_element, element_text, analysis
                )
            elif analysis.get("is_dropdown"):
                return await self._execute_dropdown_click_strategy(page, dom_element, element_text)
            elif analysis.get("is_svg_interactive"):
                return await self._execute_svg_click_strategy(page, dom_element, element_text)
            else:
                return await self._execute_standard_enhanced_click(page, dom_element, element_text)
                
        except Exception as e:
            logger.error(f"❌ 增强元素点击失败: {e}")
            return {"success": False, "error": str(e)}

    async def _execute_custom_radio_click_strategies(
        self, page, dom_element, element_text: str, analysis: Dict
    ) -> Dict:
        """🎯 执行自定义Radio按钮的多种点击策略"""
        try:
            strategies = [
                ("javascript_trigger", "JavaScript直接触发"),
                ("label_click", "Label标签点击"),
                ("container_click", "容器元素点击"),
                ("svg_icon_click", "SVG图标点击"),
                ("force_activation", "强制激活")
            ]
            
            for strategy_name, strategy_desc in strategies:
                logger.info(f"🎯 尝试自定义Radio策略: {strategy_desc}")
                
                try:
                    success = False
                    
                    if strategy_name == "javascript_trigger":
                        success = await self._javascript_radio_trigger_v2(page, element_text, analysis)
                    elif strategy_name == "label_click":
                        success = await self._label_click_strategy(page, element_text, analysis)
                    elif strategy_name == "container_click":
                        success = await self._container_click_strategy(page, dom_element, analysis)
                    elif strategy_name == "svg_icon_click":
                        success = await self._svg_icon_click_strategy(page, dom_element, analysis)
                    elif strategy_name == "force_activation":
                        success = await self._force_radio_activation(page, element_text, analysis)
                    
                    if success:
                        # 验证点击效果
                        await asyncio.sleep(0.5)
                        verification = await self._verify_radio_selection_v2(page, element_text, analysis)
                        
                        if verification:
                            logger.info(f"✅ 自定义Radio策略成功: {strategy_desc}")
                            return {
                                "success": True,
                                "strategy": strategy_name,
                                "verification": True
                            }
                        else:
                            logger.warning(f"⚠️ 策略执行成功但验证失败: {strategy_desc}")
                            continue
                    
                except Exception as e:
                    logger.warning(f"⚠️ 策略异常 {strategy_desc}: {e}")
                    continue
            
            return {"success": False, "error": "所有自定义Radio策略都失败"}
            
        except Exception as e:
            logger.error(f"❌ 自定义Radio点击策略执行失败: {e}")
            return {"success": False, "error": str(e)}

    async def _javascript_radio_trigger_v2(self, page, element_text: str, analysis: Dict) -> bool:
        """🎯 增强版JavaScript Radio触发"""
        try:
            radio_id = analysis.get("special_attributes", {}).get("radioId", "")
            radio_name = analysis.get("special_attributes", {}).get("radioName", "")
            
            js_script = f"""
            () => {{
                const cleanText = '{element_text}'.trim();
                console.log('JavaScript增强Radio触发，目标文本:', cleanText);
                
                // 方法1: 如果知道radio ID，直接操作
                if ('{radio_id}') {{
                    const radio = document.getElementById('{radio_id}');
                    if (radio) {{
                        radio.checked = true;
                        radio.dispatchEvent(new Event('change', {{ bubbles: true }}));
                        radio.dispatchEvent(new Event('click', {{ bubbles: true }}));
                        console.log('通过ID成功触发Radio:', radio);
                        return true;
                    }}
                }}
                
                // 方法2: 通过name属性查找
                if ('{radio_name}') {{
                    const radios = document.querySelectorAll(`input[name="{radio_name}"]`);
                    for (const radio of radios) {{
                        const container = radio.closest('.element, .clickableCell, label');
                        if (container && container.textContent.includes(cleanText)) {{
                            radio.checked = true;
                            radio.dispatchEvent(new Event('change', {{ bubbles: true }}));
                            radio.dispatchEvent(new Event('click', {{ bubbles: true }}));
                            console.log('通过name成功触发Radio:', radio);
                            return true;
                        }}
                    }}
                }}
                
                // 方法3: 通过文本匹配查找
                const allLabels = document.querySelectorAll('label');
                for (const label of allLabels) {{
                    if (label.textContent.trim() === cleanText) {{
                        if (label.getAttribute('for')) {{
                            const radio = document.getElementById(label.getAttribute('for'));
                            if (radio && radio.type === 'radio') {{
                                radio.checked = true;
                                radio.dispatchEvent(new Event('change', {{ bubbles: true }}));
                                radio.dispatchEvent(new Event('click', {{ bubbles: true }}));
                                console.log('通过label成功触发Radio:', radio);
                                return true;
                            }}
                        }}
                        
                        // 直接点击label
                        label.click();
                        console.log('直接点击label:', label);
                        return true;
                    }}
                }}
                
                return false;
            }}
            """
            
            result = await page.evaluate(js_script)
            return bool(result)
            
        except Exception as e:
            logger.error(f"❌ JavaScript Radio触发失败: {e}")
            return False

    async def _label_click_strategy(self, page, element_text: str, analysis: Dict) -> bool:
        """🎯 Label点击策略"""
        try:
            label_selectors = [
                f'label:has-text("{element_text}")',
                f'label[for]:has-text("{element_text}")',
                f'.element:has-text("{element_text}") label'
            ]
            
            for selector in label_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        await elements[0].click()
                        await asyncio.sleep(0.3)
                        return True
                except Exception as e:
                    logger.debug(f"Label选择器失败 {selector}: {e}")
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Label点击策略失败: {e}")
            return False

    async def _container_click_strategy(self, page, dom_element, analysis: Dict) -> bool:
        """🎯 容器点击策略"""
        try:
            xpath = '//' + dom_element.xpath
            
            container_selectors = [
                f"xpath={xpath}/ancestor::div[contains(@class, 'element')]",
                f"xpath={xpath}/ancestor::div[contains(@class, 'clickableCell')]",
                f"xpath={xpath}/ancestor::label",
                f"xpath={xpath}/parent::*",
                f"xpath={xpath}"
            ]
            
            for selector in container_selectors:
                try:
                    element = page.locator(selector)
                    if await element.count() > 0:
                        await element.first.click()
                        await asyncio.sleep(0.3)
                        return True
                except Exception as e:
                    logger.debug(f"容器选择器失败 {selector}: {e}")
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"❌ 容器点击策略失败: {e}")
            return False

    async def _svg_icon_click_strategy(self, page, dom_element, analysis: Dict) -> bool:
        """🎯 SVG图标点击策略"""
        try:
            xpath = '//' + dom_element.xpath
            
            svg_selectors = [
                f"xpath={xpath}//span[contains(@class, 'fir-icon')]",
                f"xpath={xpath}//svg",
                f"xpath={xpath}//*[name()='svg']",
                f"xpath={xpath}/ancestor::*//span[contains(@class, 'fir-icon')]"
            ]
            
            for selector in svg_selectors:
                try:
                    element = page.locator(selector)
                    if await element.count() > 0:
                        await element.first.click()
                        await asyncio.sleep(0.3)
                        return True
                except Exception as e:
                    logger.debug(f"SVG选择器失败 {selector}: {e}")
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"❌ SVG图标点击策略失败: {e}")
            return False

    async def _force_radio_activation(self, page, element_text: str, analysis: Dict) -> bool:
        """🎯 强制Radio激活策略"""
        try:
            js_script = f"""
            () => {{
                const cleanText = '{element_text}'.trim();
                console.log('强制Radio激活，目标文本:', cleanText);
                
                const allElements = Array.from(document.querySelectorAll('*'));
                
                for (const element of allElements) {{
                    if (element.textContent && element.textContent.trim() === cleanText) {{
                        const container = element.closest('.element, .clickableCell, form, fieldset');
                        if (container) {{
                            const radio = container.querySelector('input[type="radio"]');
                            if (radio) {{
                                radio.checked = true;
                                
                                const events = ['change', 'click', 'input', 'focus'];
                                events.forEach(eventType => {{
                                    radio.dispatchEvent(new Event(eventType, {{ bubbles: true }}));
                                }});
                                
                                element.dispatchEvent(new MouseEvent('click', {{ bubbles: true }}));
                                
                                console.log('强制激活成功:', radio);
                                return true;
                            }}
                        }}
                        
                        try {{
                            element.click();
                            console.log('直接点击元素:', element);
                            return true;
                        }} catch (e) {{
                            continue;
                        }}
                    }}
                }}
                
                return false;
            }}
            """
            
            result = await page.evaluate(js_script)
            return bool(result)
            
        except Exception as e:
            logger.error(f"❌ 强制Radio激活失败: {e}")
            return False

    async def _verify_radio_selection_v2(self, page, element_text: str, analysis: Dict) -> bool:
        """🔍 增强版Radio选择验证"""
        try:
            radio_id = analysis.get("special_attributes", {}).get("radioId", "")
            
            js_script = f"""
            () => {{
                const cleanText = '{element_text}'.trim();
                
                // 方法1: 通过ID验证
                if ('{radio_id}') {{
                    const radio = document.getElementById('{radio_id}');
                    if (radio && radio.checked) {{
                        console.log('ID验证成功:', radio);
                        return true;
                    }}
                }}
                
                // 方法2: 通过视觉指示器验证
                const visualIndicators = document.querySelectorAll('.fir-selected, .selected, .checked, [aria-checked="true"]');
                for (const indicator of visualIndicators) {{
                    if (indicator.textContent && indicator.textContent.includes(cleanText)) {{
                        console.log('视觉指示器验证成功:', indicator);
                        return true;
                    }}
                }}
                
                // 方法3: 检查是否有任何radio被选中
                const allRadios = document.querySelectorAll('input[type="radio"]:checked');
                if (allRadios.length > 0) {{
                    console.log('找到选中的radio:', allRadios[0]);
                    return true;
                }}
                
                return false;
            }}
            """
            
            result = await page.evaluate(js_script)
            return bool(result)
            
        except Exception as e:
            logger.error(f"❌ Radio选择验证失败: {e}")
            return False

    async def _execute_standard_enhanced_click(self, page, dom_element, element_text: str) -> Dict:
        """🎯 标准增强点击"""
        try:
            xpath = '//' + dom_element.xpath
            element_locator = page.locator(xpath)
            
            await element_locator.scroll_into_view_if_needed()
            await asyncio.sleep(0.2)
            await element_locator.click()
            
            return {"success": True, "strategy": "standard_enhanced"}
            
        except Exception as e:
            logger.error(f"❌ 标准增强点击失败: {e}")
            return {"success": False, "error": str(e)}

    async def _execute_dropdown_click_strategy(self, page, dom_element, element_text: str) -> Dict:
        """🎯 下拉框点击策略"""
        try:
            xpath = '//' + dom_element.xpath
            element_locator = page.locator(xpath)
            
            if dom_element.tag_name.lower() == 'select':
                await element_locator.select_option(label=element_text)
            else:
                await element_locator.click()
            
            return {"success": True, "strategy": "dropdown"}
            
        except Exception as e:
            logger.error(f"❌ 下拉框点击失败: {e}")
            return {"success": False, "error": str(e)}

    async def _execute_svg_click_strategy(self, page, dom_element, element_text: str) -> Dict:
        """🎯 SVG元素点击策略"""
        try:
            xpath = '//' + dom_element.xpath
            element_locator = page.locator(xpath)
            
            await element_locator.click(force=True)
            
            return {"success": True, "strategy": "svg_interactive"}
            
        except Exception as e:
            logger.error(f"❌ SVG点击失败: {e}")
            return {"success": False, "error": str(e)}

    async def verify_and_record_click_result(
        self, page, element_text: str, element_analysis: Dict, click_result: Dict
    ) -> None:
        """🔍 验证并记录点击结果"""
        try:
            await asyncio.sleep(0.5)
            
            if element_analysis.get("is_custom_radio"):
                verified = await self._verify_radio_selection_v2(page, element_text, element_analysis)
                if verified:
                    logger.info(f"✅ 自定义Radio验证成功: {element_text}")
                else:
                    logger.warning(f"⚠️ 自定义Radio验证失败: {element_text}")
            
        except Exception as e:
            logger.debug(f"🔍 点击结果验证失败: {e}")

# 创建全局实例
enhanced_radio_system = EnhancedCustomRadioSystem() 