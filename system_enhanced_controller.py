#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
🔥 系统增强控制器 - 解决所有题型识别和处理问题
"""

import asyncio
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class SystemEnhancedController:
    """🔥 系统增强控制器 - 专门解决当前国家选择等题型问题"""
    
    def __init__(self, base_controller):
        self.base_controller = base_controller
        self.intelligent_engines = {}
        self._initialize_engines()
    
    def _initialize_engines(self):
        """初始化所有智能引擎"""
        # 智能国籍引擎
        try:
            from intelligent_nationality_region_engine import IntelligentNationalityRegionEngine
            self.intelligent_engines['nationality'] = IntelligentNationalityRegionEngine()
            logger.info("✅ 智能国籍引擎加载成功")
        except ImportError:
            logger.warning("⚠️ 智能国籍引擎不可用")
        
        # 其他引擎...
        
    async def enhanced_click_handler(self, index: int, browser_context, element_info=None):
        """增强版点击处理器"""
        try:
            # 获取元素信息
            if not element_info:
                selector_map = await browser_context.get_selector_map()
                if index not in selector_map:
                    return {"success": False, "error": "Element not found"}
                element_info = selector_map[index]
            
            element_text = getattr(element_info, 'text', '') or ''
            
            # 检测题型并处理
            if self._is_country_selection(element_text):
                return await self._handle_country_selection(browser_context, element_info, index)
            
            # 其他题型处理...
            
            # 默认处理
            return await self._default_click(browser_context, index)
            
        except Exception as e:
            logger.error(f"❌ 增强点击处理失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _is_country_selection(self, element_text: str) -> bool:
        """检测是否是国家选择元素"""
        keywords = ["中国", "china", "philippines", "菲律宾", "国家", "nationality"]
        return any(keyword.lower() in element_text.lower() for keyword in keywords)
    
    async def _handle_country_selection(self, browser_context, element_info, index):
        """处理国家选择"""
        if 'nationality' in self.intelligent_engines:
            try:
                engine = self.intelligent_engines['nationality']
                result = await engine.handle_nationality_selection_page(
                    browser_context, target_nationality="中国"
                )
                if result.get('success'):
                    return result
            except Exception as e:
                logger.warning(f"⚠️ 智能国籍引擎失败: {e}")
        
        # 回退到默认点击
        return await self._default_click(browser_context, index)
    
    async def _default_click(self, browser_context, index):
        """默认点击处理"""
        try:
            page = await browser_context.get_current_page()
            selector_map = await browser_context.get_selector_map()
            element = selector_map[index]
            xpath = '//' + element.xpath
            await page.click(xpath)
            return {"success": True, "method": "default_click"}
        except Exception as e:
            return {"success": False, "error": str(e)} 