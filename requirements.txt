# 智能问卷自动填写系统 - Python依赖包
# 项目版本: 1.0.0
# Python版本要求: >= 3.8

# 核心框架
asyncio-mqtt==0.16.1
aiohttp==3.9.1
requests==2.31.0

# Web框架
flask==3.0.0

# 数据库相关
PyMySQL==1.1.0
pymysql[rsa]==1.1.0

# 浏览器自动化
selenium==4.15.2
webdriver-manager==4.0.1
browser-use==0.1.0

# 数据处理
pandas==2.1.4
numpy==1.24.3
statistics==1.0.3.5

# 异步处理
asyncio==3.4.3
concurrent-futures==3.1.1

# 日志和配置
logging==0.4.9.6
python-dotenv==1.0.0
configparser==6.0.0

# 网络请求
urllib3==2.1.0
certifi==2023.11.17
charset-normalizer==3.3.2
idna==3.6

# 数据序列化
json5==0.9.14
pyyaml==6.0.1

# 时间处理
python-dateutil==2.8.2
pytz==2023.3.post1

# 系统工具
psutil==5.9.6
uuid==1.30

# 开发工具
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0

# 可选依赖 (用于未来扩展)
# 机器学习
# scikit-learn==1.3.2
# tensorflow==2.15.0

# Web界面
# flask==3.0.0
# fastapi==0.104.1
# uvicorn==0.24.0

# 图像处理 (验证码识别)
# pillow==10.1.0
# opencv-python==4.8.1.78

# 消息队列
# redis==5.0.1
# celery==5.3.4

# 监控告警
# prometheus-client==0.19.0
# grafana-api==1.0.3
