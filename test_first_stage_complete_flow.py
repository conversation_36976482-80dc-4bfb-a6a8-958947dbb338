#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试第一阶段完整流程
验证：数字人信息 → AdsPower浏览器启动 → 青果代理 → URL导航 → 智能作答
"""

import asyncio
import logging
import time
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_first_stage_complete_flow():
    """测试第一阶段完整流程"""
    
    print("🚀 开始测试第一阶段完整流程")
    print("="*60)
    
    # 1. 准备测试数据
    test_questionnaire_url = "https://wjx.cn/vm/w4e8hc9.aspx"  # 使用默认问卷URL
    test_persona = {
        "id": 1,
        "name": "张三",
        "age": 25,
        "gender": "男",
        "profession": "程序员",
        "education_level": "本科",
        "income_level": "中等",
        "marital_status": "未婚"
    }
    
    print(f"📋 测试配置:")
    print(f"   问卷URL: {test_questionnaire_url}")
    print(f"   数字人: {test_persona['name']} ({test_persona['age']}岁 {test_persona['gender']}性 {test_persona['profession']})")
    print()
    
    try:
        # 2. 导入三阶段智能核心系统
        print("📦 导入三阶段智能核心系统...")
        from intelligent_three_stage_core import ThreeStageIntelligentCore
        
        # 3. 初始化系统
        print("🔧 初始化三阶段智能核心系统...")
        core_system = ThreeStageIntelligentCore()
        
        # 4. 测试单个敢死队任务（第一阶段核心）
        print("🔍 开始测试单个敢死队任务...")
        print("-" * 40)
        
        session_id = f"test_{int(time.time())}"
        scout_index = 1
        
        # 调用单个侦察任务
        experiences = await core_system._execute_single_scout_mission(
            session_id=session_id,
            questionnaire_url=test_questionnaire_url,
            persona=test_persona,
            scout_index=scout_index
        )
        
        # 5. 分析结果
        print("\n📊 任务执行结果分析:")
        print("=" * 40)
        
        if experiences and len(experiences) > 0:
            experience = experiences[0]
            
            print(f"✅ 任务执行完成")
            print(f"   侦察员: {experience.scout_name}")
            print(f"   成功状态: {'✅ 成功' if experience.success else '❌ 失败'}")
            print(f"   答题数量: {experience.questions_count}")
            print(f"   完成深度: {experience.completion_depth:.1%}")
            print(f"   错误类型: {experience.error_type}")
            
            if not experience.success:
                print(f"   失败原因: {experience.failure_reason}")
                print(f"   技术详情: {experience.technical_error_details}")
            
            if experience.questions_answered:
                print(f"   答题详情: {len(experience.questions_answered)}个问题")
                for i, qa in enumerate(experience.questions_answered[:3], 1):
                    print(f"     {i}. {qa.get('question', '未知问题')[:50]}...")
        else:
            print("❌ 未获得有效的执行结果")
        
        print("\n🎯 关键检查点:")
        print("-" * 30)
        
        # 检查AdsPower浏览器是否启动
        if hasattr(core_system, 'lifecycle_manager'):
            active_browsers = core_system.lifecycle_manager.active_browsers
            print(f"🌐 AdsPower浏览器状态: {len(active_browsers)}个活跃浏览器")
            
            for profile_name, browser_info in active_browsers.items():
                debug_port = browser_info.get('debug_port')
                print(f"   - {profile_name}: 调试端口 {debug_port}")
        
        # 检查代理状态
        if hasattr(core_system, 'lifecycle_manager'):
            proxy_count = len(core_system.lifecycle_manager.browser_proxies)
            print(f"🔗 代理分配状态: {proxy_count}个代理已分配")
        
        return experiences
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_adspower_connection():
    """测试AdsPower连接"""
    print("\n🔧 测试AdsPower连接...")
    
    try:
        from enhanced_adspower_lifecycle import EnhancedAdsPowerLifecycle
        
        lifecycle = EnhancedAdsPowerLifecycle()
        
        # 测试创建浏览器环境
        test_profile = f"test_profile_{int(time.time())}"
        
        print(f"🚀 创建测试浏览器环境: {test_profile}")
        browser_env = await lifecycle.create_complete_browser_environment(test_profile)
        
        if browser_env.get("status") == "success":
            print("✅ AdsPower浏览器环境创建成功")
            
            browser_config = browser_env.get("browser_config", {})
            debug_port = browser_config.get("debug_port")
            
            print(f"   调试端口: {debug_port}")
            print(f"   配置文件: {browser_config.get('profile_name')}")
            
            # 清理测试环境
            print("🧹 清理测试环境...")
            await lifecycle.cleanup_all_browsers()
            
            return True
        else:
            print(f"❌ AdsPower浏览器环境创建失败: {browser_env.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ AdsPower连接测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 第一阶段完整流程测试")
    print("=" * 60)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 先测试AdsPower连接
    asyncio.run(test_adspower_connection())
    
    print("\n" + "="*60)
    
    # 再测试完整流程
    result = asyncio.run(test_first_stage_complete_flow())
    
    print("\n" + "="*60)
    print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if result:
        print("🎉 测试完成！请检查是否看到了AdsPower浏览器窗口打开")
    else:
        print("❌ 测试失败，请检查错误信息")
