#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Agent修复是否有效
"""

import sys
import os
sys.path.insert(0, '.')

def test_agent_creation():
    """测试Agent创建是否成功"""
    try:
        print("🔧 开始测试Agent创建...")
        
        # 导入必要的模块
        from src.agent.browser_use.browser_use_agent import BrowserUseAgent
        from browser_use.browser.context import BrowserContext
        from langchain_google_genai import ChatGoogleGenerativeAI
        from src.controller.custom_controller import CustomController
        
        print("✅ 模块导入成功")
        
        # 创建测试组件
        llm = ChatGoogleGenerativeAI(
            model="gemini-pro",
            google_api_key="test_key",
            temperature=0.7
        )
        print("✅ LLM创建成功")
        
        # 创建控制器
        controller = CustomController()
        print("✅ Controller创建成功")
        
        # 创建BrowserContext (模拟)
        browser_context = None  # 在实际测试中这里会是真实的BrowserContext
        
        # 尝试创建Agent
        agent = BrowserUseAgent(
            task="测试任务",
            llm=llm,
            browser_context=browser_context,
            controller=controller,
            digital_human_info={"name": "测试用户"},
            max_actions_per_step=10,
            use_vision=True
        )
        
        print("✅ BrowserUseAgent创建成功！")
        print(f"Agent类型: {type(agent)}")
        print(f"Agent任务: {agent.task}")
        print(f"Agent控制器: {type(agent.controller)}")
        print(f"Agent use_vision: {getattr(agent, 'use_vision', 'Not set')}")
        print(f"Agent max_actions_per_step: {getattr(agent, 'max_actions_per_step', 'Not set')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始测试Agent修复...")
    success = test_agent_creation()
    if success:
        print("🎉 测试成功！Agent修复有效。")
    else:
        print("💥 测试失败！需要进一步修复。")
