"""
🌍 智能国籍区域选择引擎 - 人类视觉模拟版本
专门解决复杂的国籍/地区选择页面问题，模拟人类扫描和选择行为
"""

import asyncio
import logging
import random
from typing import Dict, List, Optional
from browser_use.browser.context import BrowserContext

logger = logging.getLogger(__name__)


class IntelligentNationalityRegionEngine:
    """智能国籍区域选择引擎 - 人类视觉模拟"""
    
    def __init__(self):
        self.nationality_keywords = {
            '中国': ['中国', 'china', 'chinese', '中华', 'prc', '中国人'],
            '美国': ['美国', 'usa', 'america', 'united states', 'american', '美国人'],
            '日本': ['日本', 'japan', 'japanese', 'jpn', '日本人'],
            '韩国': ['韩国', 'korea', 'korean', 'south korea', '韩国人'],
            '澳大利亚': ['澳大利亚', 'australia', 'australian', 'aus', '澳大利亚人'],
            '加拿大': ['加拿大', 'canada', 'canadian', '加拿大人'],
            '英国': ['英国', 'uk', 'britain', 'united kingdom', 'british', '英国人'],
            '德国': ['德国', 'germany', 'german', 'deutschland', '德国人'],
            '法国': ['法国', 'france', 'french', '法国人'],
            '意大利': ['意大利', 'italy', 'italian', '意大利人'],
            '新加坡': ['新加坡', 'singapore', 'singaporean', '新加坡人'],
            '印度': ['印度', 'india', 'indian', '印度人'],
            '泰国': ['泰国', 'thailand', 'thai', '泰国人'],
            '马来西亚': ['马来西亚', 'malaysia', 'malaysian', '马来西亚人'],
            '印尼': ['印尼', 'indonesia', 'indonesian', '印尼人'],
            '越南': ['越南', 'vietnam', 'vietnamese', '越南人'],
            '菲律宾': ['菲律宾', 'philippines', 'filipino', '菲律宾人'],
            '俄罗斯': ['俄罗斯', 'russia', 'russian', '俄罗斯人'],
            '巴西': ['巴西', 'brazil', 'brazilian', '巴西人'],
            '墨西哥': ['墨西哥', 'mexico', 'mexican', '墨西哥人']
        }
        
        self.region_keywords = {
            '亚洲': ['asia', 'asian', '亚洲', '亚太'],
            '欧洲': ['europe', 'european', '欧洲'],
            '北美洲': ['north america', 'northern america', '北美', '北美洲'],
            '南美洲': ['south america', 'southern america', '南美', '南美洲'],
            '非洲': ['africa', 'african', '非洲'],
            '大洋洲': ['oceania', 'australia', '大洋洲']
        }
    
    async def handle_nationality_selection_with_human_vision(
        self, 
        browser_context, 
        target_nationality="中国",
        confidence_threshold=0.8
    ):
        """🔍 人类视觉模拟国籍选择 - 完美处理长选项列表"""
        try:
            page = await browser_context.get_current_page()
            logger.info(f"🔍 启动人类视觉模拟国籍选择 - 目标: {target_nationality}")
            
            # 🎯 第一阶段：第一屏幕智能扫描
            first_screen_result = await self._simulate_first_screen_scan(
                page, target_nationality, confidence_threshold
            )
            
            # 如果第一屏发现高置信度匹配，直接选择
            if first_screen_result.get('found_high_confidence_match'):
                logger.info(f"🎯 第一屏直接命中高分选项: {first_screen_result['best_option']['text']} (评分: {first_screen_result['confidence_score']:.2f})")
                
                selection_result = await self._execute_confident_selection(
                    page, first_screen_result['best_option']
                )
                
                if selection_result.get('success'):
                    return {
                        "success": True,
                        "selected_option": first_screen_result['best_option']['text'],
                        "selection_method": "first_screen_confident",
                        "confidence_score": first_screen_result['confidence_score']
                    }
            
            # 🔄 第二阶段：智能滚动收集所有选项（记住之前的）
            logger.info("🔄 第一屏未发现高置信度匹配，开始智能滚动收集所有选项")
            
            all_options_result = await self._collect_all_options_with_memory(
                page, target_nationality, first_screen_result.get('all_first_screen_options', [])
            )
            
            if not all_options_result.get('success'):
                return {"success": False, "error": f"选项收集失败: {all_options_result.get('error')}"}
            
            # 🧠 第三阶段：全局智能评分和选择
            best_choice_result = await self._perform_global_intelligent_evaluation(
                all_options_result['all_options'], target_nationality
            )
            
            if best_choice_result.get('success'):
                final_selection = await self._execute_optimal_selection(
                    page, best_choice_result['best_option']
                )
                
                if final_selection.get('success'):
                    return {
                        "success": True,
                        "selected_option": best_choice_result['best_option']['text'],
                        "selection_method": "global_intelligent",
                        "final_score": best_choice_result['best_option']['final_score'],
                        "total_options_evaluated": len(all_options_result['all_options'])
                    }
            
            return {"success": False, "error": "所有选择策略都失败"}
            
        except Exception as e:
            error_msg = f"❌ 人类视觉模拟国籍选择失败: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    async def _simulate_first_screen_scan(self, page, target_nationality, confidence_threshold):
        """👀 模拟第一屏幕人类视觉扫描"""
        try:
            # 获取第一屏所有可见选项
            first_screen_options = await self._extract_visible_options_smart(page)
            
            if not first_screen_options:
                return {"found_high_confidence_match": False, "options_count": 0}
            
            logger.info(f"👀 第一屏扫描发现 {len(first_screen_options)} 个选项")
            
            # 对每个选项进行智能评分
            scored_options = []
            for option in first_screen_options:
                score = self._calculate_smart_matching_score(option['text'], target_nationality)
                option['score'] = score
                option['screen_position'] = 'first_screen'
                scored_options.append(option)
                
                # 记录评分过程
                logger.debug(f"  选项: {option['text']} - 评分: {score:.3f}")
            
            # 找到最佳选项
            best_option = max(scored_options, key=lambda x: x.get('score', 0))
            
            # 判断是否达到高置信度阈值（断崖式高分）
            high_confidence = best_option['score'] >= confidence_threshold
            
            logger.info(f"👀 第一屏最佳选项: {best_option['text']} (评分: {best_option['score']:.3f}, 高置信度: {high_confidence})")
            
            return {
                "found_high_confidence_match": high_confidence,
                "best_option": best_option,
                "all_first_screen_options": scored_options,
                "confidence_score": best_option['score']
            }
            
        except Exception as e:
            logger.warning(f"⚠️ 第一屏扫描失败: {e}")
            return {"found_high_confidence_match": False, "error": str(e)}
    
    async def _collect_all_options_with_memory(self, page, target_nationality, first_screen_options):
        """🔄 智能滚动收集所有选项 - 带记忆功能"""
        try:
            # 初始化选项记忆库（包含第一屏选项）
            all_options = list(first_screen_options)  # 深拷贝第一屏选项
            
            scroll_attempt = 0
            max_scrolls = 15
            last_option_count = len(all_options)
            stable_count = 0
            
            logger.info(f"🔄 开始智能滚动收集，初始选项数: {len(all_options)}")
            
            while scroll_attempt < max_scrolls:
                # 执行人类化滚动
                await self._perform_human_like_scroll(page, scroll_attempt)
                await asyncio.sleep(random.uniform(0.5, 1.0))  # 人类化延迟
                
                # 获取当前屏幕的新选项
                current_options = await self._extract_visible_options_smart(page)
                
                # 智能合并新发现的选项（去重，保持记忆）
                new_options_found = 0
                for option in current_options:
                    if not self._is_option_already_in_memory(option, all_options):
                        option['discovered_at_scroll'] = scroll_attempt + 1
                        option['screen_position'] = f'scroll_{scroll_attempt + 1}'
                        all_options.append(option)
                        new_options_found += 1
                        
                        # 实时检查是否发现超高分选项
                        score = self._calculate_smart_matching_score(option['text'], target_nationality)
                        option['score'] = score
                        
                        if score >= 0.9:  # 发现极高匹配度选项，可以提前停止
                            logger.info(f"🎯 发现极高匹配选项，提前停止滚动: {option['text']} (评分: {score:.3f})")
                            break
                
                logger.info(f"🔄 滚动 {scroll_attempt + 1}: 新发现 {new_options_found} 个选项，总计 {len(all_options)} 个")
                
                # 检查是否达到页面底部
                if len(all_options) == last_option_count:
                    stable_count += 1
                    if stable_count >= 3:
                        logger.info("🔄 页面已稳定，停止滚动")
                        break
                else:
                    stable_count = 0
                    last_option_count = len(all_options)
                
                scroll_attempt += 1
            
            # 对所有选项进行最终评分
            for option in all_options:
                if 'score' not in option:
                    option['score'] = self._calculate_smart_matching_score(option['text'], target_nationality)
            
            logger.info(f"🔄 滚动收集完成，总共发现 {len(all_options)} 个选项")
            
            return {
                "success": True,
                "all_options": all_options,
                "total_scrolls": scroll_attempt,
                "options_count": len(all_options)
            }
            
        except Exception as e:
            return {"success": False, "error": f"滚动收集失败: {e}"}
    
    async def _extract_visible_options_smart(self, page):
        """🔍 智能提取当前可见的选项"""
        try:
            extract_js = """
            () => {
                const options = [];
                
                // 智能选择器 - 覆盖各种可能的选项类型
                const selectors = [
                    // 输入框相关
                    'input[type="radio"] + label',
                    'label:has(input[type="radio"])',
                    'input[type="checkbox"] + label', 
                    'label:has(input[type="checkbox"])',
                    
                    // 通用选项类
                    '.option', '.choice', '.item', '.selection',
                    '.nationality-option', '.country-option', '.region-option',
                    
                    // 数据属性
                    '[data-value]', '[data-option]', '[data-choice]',
                    
                    // 可点击元素
                    'li[onclick]', 'div[onclick]', 'span[onclick]',
                    'button:not([class*="continue"]):not([class*="next"]):not([class*="submit"])',
                    
                    // 角色属性
                    '[role="option"]', '[role="radio"]', '[role="checkbox"]'
                ];
                
                selectors.forEach(selector => {
                    try {
                        document.querySelectorAll(selector).forEach((element, index) => {
                            // 检查元素是否在当前视口内可见
                            const rect = element.getBoundingClientRect();
                            const isInViewport = rect.height > 0 && rect.width > 0 && 
                                               rect.top >= -50 && rect.bottom <= window.innerHeight + 50;
                            
                            if (isInViewport) {
                                const text = (element.textContent || element.innerText || element.value || '').trim();
                                
                                // 过滤掉无效选项
                                if (text && text.length > 0 && 
                                    !text.includes('继续') && !text.includes('下一步') &&
                                    !text.includes('提交') && !text.includes('确定') &&
                                    !text.includes('条款') && !text.includes('隐私') &&
                                    text.length < 100) {  // 避免长描述文本
                                    
                                    options.push({
                                        text: text,
                                        selector: selector,
                                        element_index: index,
                                        tag: element.tagName.toLowerCase(),
                                        id: element.id || '',
                                        className: element.className || '',
                                        dataValue: element.getAttribute('data-value') || '',
                                        visible: true,
                                        rect: {
                                            top: Math.round(rect.top),
                                            left: Math.round(rect.left),
                                            width: Math.round(rect.width),
                                            height: Math.round(rect.height)
                                        }
                                    });
                                }
                            }
                        });
                    } catch (e) {
                        console.warn(`Selector ${selector} extraction failed:`, e);
                    }
                });
                
                // 智能去重 - 基于文本内容
                const uniqueOptions = [];
                const seenTexts = new Set();
                
                options.forEach(option => {
                    const normalizedText = option.text.toLowerCase().trim();
                    if (!seenTexts.has(normalizedText)) {
                        seenTexts.add(normalizedText);
                        uniqueOptions.push(option);
                    }
                });
                
                // 按位置排序（从上到下，从左到右）
                uniqueOptions.sort((a, b) => {
                    const yDiff = a.rect.top - b.rect.top;
                    return Math.abs(yDiff) < 10 ? a.rect.left - b.rect.left : yDiff;
                });
                
                return uniqueOptions;
            }
            """
            
            result = await page.evaluate(extract_js)
            return result or []
            
        except Exception as e:
            logger.warning(f"⚠️ 选项提取失败: {e}")
            return []
    
    def _calculate_smart_matching_score(self, option_text, target_nationality):
        """🧠 智能计算选项匹配分数 - 断崖式高分识别"""
        try:
            option_lower = option_text.lower().strip()
            target_keywords = self.nationality_keywords.get(target_nationality, [target_nationality.lower()])
            
            score = 0.0
            
            # 🎯 精确匹配（最高分 - 断崖式）
            for keyword in target_keywords:
                keyword_lower = keyword.lower()
                
                if option_lower == keyword_lower:
                    score = 1.0  # 完全匹配 - 最高分
                    break
                elif keyword_lower in option_lower and len(keyword_lower) > 2:
                    # 包含关键词且关键词足够长
                    if option_lower.startswith(keyword_lower) or option_lower.endswith(keyword_lower):
                        score = max(score, 0.9)  # 前缀/后缀匹配 - 很高分
                    else:
                        score = max(score, 0.8)  # 包含匹配 - 高分
            
            # 🔍 特殊国籍识别加分
            if target_nationality == '中国':
                if any(word in option_lower for word in ['中国', '华人', '汉族', 'chinese', 'china']):
                    score = max(score, 0.85)
                elif any(word in option_lower for word in ['中华', '大陆', '内地']):
                    score = max(score, 0.75)
            
            # ⚠️ 负分惩罚
            if any(neg_word in option_lower for neg_word in ['其他', '不想', '拒绝', '无关', '未知']):
                score *= 0.3
            
            # 📏 长度惩罚（避免选择描述性文本）
            if len(option_text) > 30:
                score *= 0.8
            
            return min(1.0, max(0.0, score))
            
        except Exception as e:
            logger.warning(f"⚠️ 评分计算失败: {e}")
            return 0.0
    
    def _is_option_already_in_memory(self, new_option, memory_options):
        """🧠 检查选项是否已在记忆中"""
        new_text = new_option['text'].lower().strip()
        for memory_option in memory_options:
            memory_text = memory_option['text'].lower().strip()
            if new_text == memory_text or (len(new_text) > 5 and new_text in memory_text) or (len(memory_text) > 5 and memory_text in new_text):
                return True
        return False
    
    async def _perform_human_like_scroll(self, page, scroll_attempt):
        """🔄 执行人类化滚动"""
        try:
            # 模拟人类滚动行为 - 速度递增
            if scroll_attempt < 3:
                scroll_distance = random.randint(250, 400)  # 开始时谨慎滚动
            elif scroll_attempt < 8:
                scroll_distance = random.randint(400, 600)  # 中期正常滚动
            else:
                scroll_distance = random.randint(500, 800)  # 后期快速滚动
            
            # 分段滚动（更像人类）
            segments = random.randint(2, 4)
            segment_distance = scroll_distance // segments
            
            for i in range(segments):
                await page.evaluate(f"window.scrollBy(0, {segment_distance})")
                await asyncio.sleep(random.uniform(0.1, 0.25))  # 段间延迟
            
            # 额外的人类化延迟
            await asyncio.sleep(random.uniform(0.2, 0.5))
            
        except Exception as e:
            logger.warning(f"⚠️ 人类化滚动失败: {e}")
    
    async def _perform_global_intelligent_evaluation(self, all_options, target_nationality):
        """🧠 全局智能评估 - 综合所有因素选择最佳选项"""
        try:
            if not all_options:
                return {"success": False, "error": "没有可用选项"}
            
            logger.info(f"🧠 开始全局智能评估 {len(all_options)} 个选项")
            
            # 重新计算所有选项的最终分数
            for option in all_options:
                base_score = self._calculate_smart_matching_score(option['text'], target_nationality)
                
                # 位置加分（第一屏的选项轻微加分，符合人类偏好）
                position_bonus = 0.02 if option.get('screen_position') == 'first_screen' else 0.0
                
                # 元素类型加分（某些元素类型更可靠）
                element_bonus = 0.01 if option.get('tag') in ['input', 'button'] else 0.0
                
                option['final_score'] = base_score + position_bonus + element_bonus
            
            # 找到最佳选项
            best_option = max(all_options, key=lambda x: x.get('final_score', 0))
            
            # 记录详细的评分过程（调试用）
            top_options = sorted(all_options, key=lambda x: x.get('final_score', 0), reverse=True)[:5]
            logger.info("🏆 全局评估前5名:")
            for i, option in enumerate(top_options):
                logger.info(f"  {i+1}. {option['text']} - 最终评分: {option.get('final_score', 0):.3f} (位置: {option.get('screen_position', 'unknown')})")
            
            return {
                "success": True,
                "best_option": best_option,
                "all_scored_options": all_options,
                "top_alternatives": top_options[:3],
                "total_evaluated": len(all_options)
            }
            
        except Exception as e:
            return {"success": False, "error": f"全局评估失败: {e}"}
    
    async def _execute_confident_selection(self, page, option):
        """✅ 执行高置信度选择"""
        try:
            return await self._click_option_with_multiple_strategies(page, option)
        except Exception as e:
            return {"success": False, "error": f"高置信度选择失败: {e}"}
    
    async def _execute_optimal_selection(self, page, option):
        """✅ 执行最优选择"""
        try:
            return await self._click_option_with_multiple_strategies(page, option)
        except Exception as e:
            return {"success": False, "error": f"最优选择失败: {e}"}
    
    async def _click_option_with_multiple_strategies(self, page, option):
        """🖱️ 多策略智能点击选项"""
        try:
            click_strategies = [
                ("ID点击", self._click_by_id),
                ("数据值点击", self._click_by_data_value),
                ("文本点击", self._click_by_text),
                ("坐标点击", self._click_by_coordinates),
                ("CSS选择器点击", self._click_by_css_selector)
            ]
            
            for strategy_name, strategy_func in click_strategies:
                try:
                    result = await strategy_func(page, option)
                    if result.get('success'):
                        logger.info(f"✅ {strategy_name}成功: {option['text']}")
                        return result
                except Exception as e:
                    logger.debug(f"⚠️ {strategy_name}失败: {e}")
                    continue
            
            return {"success": False, "error": "所有点击策略都失败"}
            
        except Exception as e:
            return {"success": False, "error": f"智能点击失败: {e}"}
    
    async def _click_by_id(self, page, option):
        """通过ID点击"""
        if option.get('id'):
            await page.click(f"#{option['id']}")
            return {"success": True, "method": "id_click"}
        raise Exception("没有有效的ID")
    
    async def _click_by_data_value(self, page, option):
        """通过data-value点击"""
        if option.get('dataValue'):
            await page.click(f'[data-value="{option["dataValue"]}"]')
            return {"success": True, "method": "data_value_click"}
        raise Exception("没有有效的data-value")
    
    async def _click_by_text(self, page, option):
        """通过文本点击"""
        # 尝试精确文本匹配
        try:
            await page.click(f'text="{option["text"]}"', timeout=3000)
            return {"success": True, "method": "exact_text_click"}
        except:
            # 尝试部分文本匹配
            await page.click(f'text=/.*{option["text"]}.*/', timeout=3000)
            return {"success": True, "method": "partial_text_click"}
    
    async def _click_by_coordinates(self, page, option):
        """通过坐标点击"""
        rect = option.get('rect', {})
        if rect and rect.get('width', 0) > 0 and rect.get('height', 0) > 0:
            x = rect['left'] + rect['width'] / 2
            y = rect['top'] + rect['height'] / 2
            await page.mouse.click(x, y)
            return {"success": True, "method": "coordinate_click"}
        raise Exception("没有有效的坐标信息")
    
    async def _click_by_css_selector(self, page, option):
        """通过CSS选择器点击"""
        if option.get('className'):
            # 尝试通过类名点击
            classes = option['className'].split()
            if classes:
                await page.click(f".{classes[0]}")
                return {"success": True, "method": "css_class_click"}
        
        # 尝试通过标签和文本组合
        tag = option.get('tag', 'div')
        await page.click(f'{tag}:has-text("{option["text"]}")')
        return {"success": True, "method": "css_tag_text_click"}

    async def handle_nationality_selection_page(
        self, 
        browser_context: BrowserContext,
        target_nationality: str = "中国",
        search_strategy: str = "comprehensive"
    ) -> Dict:
        """🌍 处理国籍选择页面的主入口方法"""
        try:
            page = await browser_context.get_current_page()
            
            logger.info(f"🌍 启动智能国籍选择 - 目标: {target_nationality}")
            
            # 第一阶段：页面结构分析
            structure_analysis = await self._analyze_page_structure(page)
            if not structure_analysis.get('success'):
                return structure_analysis
            
            # 第二阶段：智能选择执行
            selection_result = await self._execute_intelligent_selection(
                page, structure_analysis, target_nationality, search_strategy
            )
            
            return selection_result
            
        except Exception as e:
            error_msg = f"❌ 智能国籍选择失败: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    async def _analyze_page_structure(self, page) -> Dict:
        """🔍 分析页面结构，识别所有可选择的元素"""
        try:
            structure_js = """
            () => {
                // 检测页面是否包含国籍相关内容
                const pageText = document.body.textContent.toLowerCase();
                const nationalityKeywords = [
                    'nationality', 'country', 'region', '国籍', '国家', '地区', 
                    'citizenship', 'origin', '公民', '来源', 'where are you from',
                    'select your country', '选择国家', '选择地区'
                ];
                
                let hasNationalityContent = false;
                for (let keyword of nationalityKeywords) {
                    if (pageText.includes(keyword.toLowerCase())) {
                        hasNationalityContent = true;
                        break;
                    }
                }
                
                if (!hasNationalityContent) {
                    return { 
                        success: false, 
                        error: "页面不包含国籍相关内容",
                        page_type: "unknown"
                    };
                }
                
                // 检测各种交互元素
                const selectionElements = {
                    checkboxes: [],
                    radios: [],
                    select_elements: [],
                    custom_dropdowns: [],
                    clickable_options: [],
                    buttons: []
                };
                
                // 1. 收集checkbox和radio
                document.querySelectorAll('input[type="checkbox"], input[type="radio"]').forEach((input, index) => {
                    const label = input.closest('label') || document.querySelector(`label[for="${input.id}"]`);
                    const text = label ? label.textContent.trim() : 
                                input.nextElementSibling ? input.nextElementSibling.textContent.trim() :
                                input.getAttribute('value') || `选项${index}`;
                    
                    if (text && text.length > 0 && text !== '请选择') {
                        const elementInfo = {
                            text: text,
                            type: input.type,
                            id: input.id,
                            name: input.name,
                            value: input.value,
                            checked: input.checked,
                            xpath: getXPath(input),
                            visible: input.offsetHeight > 0,
                            enabled: !input.disabled
                        };
                        
                        if (input.type === 'checkbox') {
                            selectionElements.checkboxes.push(elementInfo);
                        } else {
                            selectionElements.radios.push(elementInfo);
                        }
                    }
                });
                
                // 2. 收集select元素
                document.querySelectorAll('select').forEach((select, index) => {
                    const selectInfo = {
                        id: select.id,
                        name: select.name,
                        xpath: getXPath(select),
                        visible: select.offsetHeight > 0,
                        enabled: !select.disabled,
                        options: []
                    };
                    
                    [...select.options].forEach(option => {
                        if (option.text && option.text.trim() && option.text.trim() !== '请选择') {
                            selectInfo.options.push({
                                text: option.text.trim(),
                                value: option.value,
                                index: option.index
                            });
                        }
                    });
                    
                    if (selectInfo.options.length > 0) {
                        selectionElements.select_elements.push(selectInfo);
                    }
                });
                
                // 3. 收集自定义下拉框
                const dropdownSelectors = [
                    '.dropdown', '.select', '.combobox', '.picker',
                    '[role="combobox"]', '[role="listbox"]',
                    '.jqselect', '.ui-selectmenu', '.el-select', '.ant-select'
                ];
                
                dropdownSelectors.forEach(selector => {
                    document.querySelectorAll(selector).forEach(element => {
                        if (element.offsetHeight > 0) {
                            selectionElements.custom_dropdowns.push({
                                selector: selector,
                                xpath: getXPath(element),
                                class: element.className,
                                id: element.id,
                                text: element.textContent.trim().substring(0, 100), // 限制长度
                                visible: true
                            });
                        }
                    });
                });
                
                // 4. 收集可点击的选项
                const optionSelectors = [
                    '[data-value]', '.option', '.item', '.choice', '.country-option',
                    '.region-option', '.nationality-option', 'li[onclick]', 
                    'div[onclick]', 'span[onclick]'
                ];
                
                optionSelectors.forEach(selector => {
                    document.querySelectorAll(selector).forEach(element => {
                        if (element.offsetHeight > 0) {
                            const text = element.textContent.trim();
                            if (text && text.length > 0 && text !== '请选择') {
                                selectionElements.clickable_options.push({
                                    text: text,
                                    xpath: getXPath(element),
                                    data_value: element.getAttribute('data-value'),
                                    class: element.className,
                                    onclick: !!element.onclick,
                                    visible: true
                                });
                            }
                        }
                    });
                });
                
                // 5. 收集按钮
                document.querySelectorAll('button, input[type="button"], input[type="submit"]').forEach(btn => {
                    if (btn.offsetHeight > 0) {
                        const text = btn.textContent.trim() || btn.value || btn.getAttribute('title') || '';
                        if (text) {
                            selectionElements.buttons.push({
                                text: text,
                                xpath: getXPath(btn),
                                type: btn.type || 'button',
                                visible: true
                            });
                        }
                    }
                });
                
                // 辅助函数：获取元素的XPath
                function getXPath(element) {
                    if (!element || element.nodeType !== Node.ELEMENT_NODE) return '';
                    
                    if (element.id) {
                        return `//*[@id="${element.id}"]`;
                    }
                    
                    const paths = [];
                    for (; element && element.nodeType === Node.ELEMENT_NODE; element = element.parentNode) {
                        let index = 0;
                        let hasFollowingSiblings = false;
                        for (let sibling = element.previousSibling; sibling; sibling = sibling.previousSibling) {
                            if (sibling.nodeType === Node.DOCUMENT_TYPE_NODE) continue;
                            if (sibling.nodeName === element.nodeName) ++index;
                        }
                        for (let sibling = element.nextSibling; sibling && !hasFollowingSiblings; sibling = sibling.nextSibling) {
                            if (sibling.nodeName === element.nodeName) hasFollowingSiblings = true;
                        }
                        const tagName = element.nodeName.toLowerCase();
                        const pathIndex = (index || hasFollowingSiblings) ? `[${index + 1}]` : '';
                        paths.splice(0, 0, tagName + pathIndex);
                    }
                    return paths.length ? '/' + paths.join('/') : '';
                }
                
                // 分析页面类型
                const totalElements = Object.values(selectionElements).reduce((sum, arr) => sum + arr.length, 0);
                let pageType = 'unknown';
                
                if (selectionElements.checkboxes.length > 5) {
                    pageType = 'multi_select_checkboxes';
                } else if (selectionElements.radios.length > 5) {
                    pageType = 'single_select_radios';
                } else if (selectionElements.select_elements.length > 0) {
                    pageType = 'dropdown_select';
                } else if (selectionElements.custom_dropdowns.length > 0) {
                    pageType = 'custom_dropdown';
                } else if (selectionElements.clickable_options.length > 5) {
                    pageType = 'clickable_options';
                } else if (totalElements > 0) {
                    pageType = 'mixed_elements';
                }
                
                return {
                    success: true,
                    page_type: pageType,
                    total_elements: totalElements,
                    elements: selectionElements,
                    complexity: totalElements > 20 ? 'high' : totalElements > 10 ? 'medium' : 'low'
                };
            }
            """
            
            result = await page.evaluate(structure_js)
            logger.info(f"🔍 页面结构分析完成 - 类型: {result.get('page_type')}, 元素总数: {result.get('total_elements')}")
            return result
            
        except Exception as e:
            return {"success": False, "error": f"页面结构分析失败: {e}"}
    
    async def _execute_intelligent_selection(
        self, 
        page, 
        structure_analysis: Dict, 
        target_nationality: str, 
        search_strategy: str
    ) -> Dict:
        """🔄 执行智能选择逻辑"""
        try:
            page_type = structure_analysis.get('page_type')
            elements = structure_analysis.get('elements', {})
            
            logger.info(f"🔄 开始智能选择 - 页面类型: {page_type}")
            
            # 根据页面类型选择不同的处理策略
            if page_type == 'multi_select_checkboxes':
                return await self._handle_checkbox_selection(page, elements['checkboxes'], target_nationality)
            elif page_type == 'single_select_radios':
                return await self._handle_radio_selection(page, elements['radios'], target_nationality)
            elif page_type == 'dropdown_select':
                return await self._handle_dropdown_selection(page, elements['select_elements'], target_nationality)
            elif page_type == 'custom_dropdown':
                return await self._handle_custom_dropdown_selection(page, elements['custom_dropdowns'], target_nationality)
            elif page_type == 'clickable_options':
                return await self._handle_clickable_options_selection(page, elements['clickable_options'], target_nationality)
            else:
                # 混合类型或未知类型，尝试所有方法
                return await self._handle_mixed_selection(page, elements, target_nationality)
                
        except Exception as e:
            return {"success": False, "error": f"智能选择执行失败: {e}"}
    
    async def _handle_checkbox_selection(self, page, checkboxes: List[Dict], target_nationality: str) -> Dict:
        """处理多选checkbox页面"""
        try:
            logger.info(f"📋 处理多选checkbox页面 - 选项数: {len(checkboxes)}")
            
            # 查找匹配的选项
            matched_options = self._find_matching_options(checkboxes, target_nationality)
            
            if not matched_options:
                return {"success": False, "error": f"未找到匹配的checkbox选项: {target_nationality}"}
            
            selected_count = 0
            selected_options = []
            
            for option in matched_options[:3]:  # 最多选择3个相关选项
                try:
                    if not option.get('checked'):  # 只点击未选中的
                        xpath = option.get('xpath')
                        if xpath:
                            await page.click(f'//{xpath}')
                            selected_count += 1
                            selected_options.append(option['text'])
                            
                            # 添加人类化延迟
                            await asyncio.sleep(random.uniform(0.3, 0.8))
                            
                except Exception as e:
                    logger.warning(f"⚠️ 点击checkbox失败: {e}")
                    continue
            
            if selected_count > 0:
                return {
                    "success": True,
                    "selected_options": selected_options,
                    "selection_count": selected_count,
                    "method": "checkbox_selection"
                }
            else:
                return {"success": False, "error": "所有checkbox点击都失败"}
                
        except Exception as e:
            return {"success": False, "error": f"checkbox处理失败: {e}"}
    
    async def _handle_radio_selection(self, page, radios: List[Dict], target_nationality: str) -> Dict:
        """处理单选radio页面"""
        try:
            logger.info(f"🔘 处理单选radio页面 - 选项数: {len(radios)}")
            
            # 查找最佳匹配
            best_match = self._find_best_match(radios, target_nationality)
            
            if not best_match:
                return {"success": False, "error": f"未找到匹配的radio选项: {target_nationality}"}
            
            try:
                xpath = best_match.get('xpath')
                if xpath:
                    await page.click(f'//{xpath}')
                    return {
                        "success": True,
                        "selected_options": [best_match['text']],
                        "method": "radio_selection"
                    }
                else:
                    return {"success": False, "error": "radio选项缺少xpath"}
                    
            except Exception as e:
                return {"success": False, "error": f"radio点击失败: {e}"}
                
        except Exception as e:
            return {"success": False, "error": f"radio处理失败: {e}"}
    
    async def _handle_dropdown_selection(self, page, selects: List[Dict], target_nationality: str) -> Dict:
        """处理原生下拉框选择"""
        try:
            logger.info(f"🔽 处理原生下拉框 - 数量: {len(selects)}")
            
            for select_info in selects:
                # 在每个select中查找匹配选项
                matched_option = self._find_matching_select_option(select_info['options'], target_nationality)
                
                if matched_option:
                    try:
                        xpath = select_info.get('xpath')
                        option_value = matched_option.get('value')
                        
                        if xpath and option_value:
                            # 使用Playwright的select_option方法
                            await page.select_option(f'//{xpath}', value=option_value)
                            return {
                                "success": True,
                                "selected_options": [matched_option['text']],
                                "method": "dropdown_selection"
                            }
                            
                    except Exception as e:
                        logger.warning(f"⚠️ 下拉框选择失败: {e}")
                        continue
            
            return {"success": False, "error": "所有下拉框选择都失败"}
            
        except Exception as e:
            return {"success": False, "error": f"下拉框处理失败: {e}"}
    
    async def _handle_custom_dropdown_selection(self, page, dropdowns: List[Dict], target_nationality: str) -> Dict:
        """处理自定义下拉框"""
        try:
            logger.info(f"🎛️ 处理自定义下拉框 - 数量: {len(dropdowns)}")
            
            for dropdown in dropdowns:
                try:
                    # 尝试点击展开下拉框
                    xpath = dropdown.get('xpath')
                    if xpath:
                        await page.click(f'//{xpath}')
                        await asyncio.sleep(0.8)  # 等待展开动画
                        
                        # 查找选项容器中的选项
                        options_found = await self._search_dropdown_options(page, target_nationality)
                        
                        if options_found.get('success'):
                            return {
                                "success": True,
                                "selected_options": [options_found.get('selected_text')],
                                "method": "custom_dropdown_selection"
                            }
                            
                except Exception as e:
                    logger.warning(f"⚠️ 自定义下拉框处理失败: {e}")
                    continue
            
            return {"success": False, "error": "所有自定义下拉框处理都失败"}
            
        except Exception as e:
            return {"success": False, "error": f"自定义下拉框处理失败: {e}"}
    
    async def _search_dropdown_options(self, page, target_nationality: str) -> Dict:
        """在下拉框选项中搜索目标选项"""
        try:
            search_js = f"""
            () => {{
                const target = "{target_nationality}";
                const targetKeywords = {self._get_nationality_keywords_js(target_nationality)};
                
                // 查找选项容器
                const containers = document.querySelectorAll(
                    '.dropdown-menu, .select-options, .options-list, ' +
                    '[role="listbox"], .jqselect-options, ' +
                    '.dropdown-content, .select-dropdown'
                );
                
                for (let container of containers) {{
                    if (container.offsetHeight > 0) {{
                        const options = container.querySelectorAll(
                            'li, .option, [role="option"], .item, .choice, ' +
                            '.dropdown-item, .select-option'
                        );
                        
                        for (let option of options) {{
                            if (option.offsetHeight > 0) {{
                                const optionText = option.textContent.trim().toLowerCase();
                                
                                // 检查是否匹配目标关键词
                                for (let keyword of targetKeywords) {{
                                    if (optionText.includes(keyword.toLowerCase())) {{
                                        option.click();
                                        return {{
                                            success: true,
                                            selected_text: option.textContent.trim(),
                                            matched_keyword: keyword
                                        }};
                                    }}
                                }}
                            }}
                        }}
                        
                        // 如果未找到，尝试滚动查找
                        const scrollableContainer = container;
                        const maxScrolls = 10;
                        let scrollCount = 0;
                        
                        while (scrollCount < maxScrolls) {{
                            scrollableContainer.scrollTop += 100;
                            await new Promise(resolve => setTimeout(resolve, 300));
                            
                            const newOptions = container.querySelectorAll(
                                'li, .option, [role="option"], .item, .choice'
                            );
                            
                            for (let option of newOptions) {{
                                if (option.offsetHeight > 0) {{
                                    const optionText = option.textContent.trim().toLowerCase();
                                    
                                    for (let keyword of targetKeywords) {{
                                        if (optionText.includes(keyword.toLowerCase())) {{
                                            option.click();
                                            return {{
                                                success: true,
                                                selected_text: option.textContent.trim(),
                                                matched_keyword: keyword,
                                                scroll_attempts: scrollCount + 1
                                            }};
                                        }}
                                    }}
                                }}
                            }}
                            
                            scrollCount++;
                        }}
                    }}
                }}
                
                return {{ success: false, error: "在下拉框中未找到匹配选项" }};
            }}
            """
            
            result = await page.evaluate(search_js)
            return result
            
        except Exception as e:
            return {"success": False, "error": f"下拉框选项搜索失败: {e}"}
    
    async def _handle_clickable_options_selection(self, page, options: List[Dict], target_nationality: str) -> Dict:
        """处理可点击选项"""
        try:
            logger.info(f"🖱️ 处理可点击选项 - 数量: {len(options)}")
            
            # 查找匹配的选项
            matched_options = self._find_matching_options(options, target_nationality)
            
            if not matched_options:
                return {"success": False, "error": f"未找到匹配的可点击选项: {target_nationality}"}
            
            # 尝试点击最佳匹配选项
            best_match = matched_options[0]
            try:
                xpath = best_match.get('xpath')
                if xpath:
                    await page.click(f'//{xpath}')
                    return {
                        "success": True,
                        "selected_options": [best_match['text']],
                        "method": "clickable_option"
                    }
                else:
                    return {"success": False, "error": "可点击选项缺少xpath"}
                    
            except Exception as e:
                return {"success": False, "error": f"可点击选项点击失败: {e}"}
                
        except Exception as e:
            return {"success": False, "error": f"可点击选项处理失败: {e}"}
    
    async def _handle_mixed_selection(self, page, elements: Dict, target_nationality: str) -> Dict:
        """处理混合类型的选择页面"""
        try:
            logger.info("🔀 处理混合类型选择页面")
            
            # 按优先级尝试不同类型的元素
            strategies = [
                ('checkboxes', self._handle_checkbox_selection),
                ('radios', self._handle_radio_selection),
                ('select_elements', self._handle_dropdown_selection),
                ('custom_dropdowns', self._handle_custom_dropdown_selection),
                ('clickable_options', self._handle_clickable_options_selection)
            ]
            
            for element_type, handler in strategies:
                element_list = elements.get(element_type, [])
                if element_list:
                    result = await handler(page, element_list, target_nationality)
                    if result.get('success'):
                        result['mixed_strategy'] = element_type
                        return result
            
            return {"success": False, "error": "所有混合选择策略都失败"}
            
        except Exception as e:
            return {"success": False, "error": f"混合选择处理失败: {e}"}
    
    def _find_matching_options(self, options: List[Dict], target_nationality: str) -> List[Dict]:
        """查找匹配的选项"""
        try:
            target_keywords = self.nationality_keywords.get(target_nationality, [target_nationality.lower()])
            matched_options = []
            
            for option in options:
                option_text = option.get('text', '').lower()
                match_score = 0
                
                # 计算匹配分数
                for keyword in target_keywords:
                    if keyword.lower() in option_text:
                        # 精确匹配得分更高
                        if keyword.lower() == option_text.strip():
                            match_score += 10
                        elif option_text.startswith(keyword.lower()):
                            match_score += 8
                        elif keyword.lower() in option_text:
                            match_score += 5
                
                if match_score > 0:
                    option['match_score'] = match_score
                    matched_options.append(option)
            
            # 按匹配分数排序
            matched_options.sort(key=lambda x: x.get('match_score', 0), reverse=True)
            
            return matched_options
            
        except Exception as e:
            logger.warning(f"⚠️ 选项匹配失败: {e}")
            return []
    
    def _find_best_match(self, options: List[Dict], target_nationality: str) -> Optional[Dict]:
        """查找最佳匹配选项"""
        matched_options = self._find_matching_options(options, target_nationality)
        return matched_options[0] if matched_options else None
    
    def _find_matching_select_option(self, options: List[Dict], target_nationality: str) -> Optional[Dict]:
        """在select选项中查找匹配项"""
        return self._find_best_match(options, target_nationality)
    
    def _get_nationality_keywords_js(self, target_nationality: str) -> str:
        """获取国籍关键词的JavaScript数组表示"""
        keywords = self.nationality_keywords.get(target_nationality, [target_nationality])
        return str(keywords).replace("'", '"') 