#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
智能问卷系统修正补丁
用于修正adspower_browser_use_integration.py中的execute_intelligent_questionnaire_task方法
"""

import logging
import asyncio
import time
from typing import Dict, Optional, List, Any
from enhanced_adspower_lifecycle import AdsPowerLifecycleManager
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, Page, BrowserContext, Playwright

logger = logging.getLogger(__name__)

"""智能问卷组件"""

class IntelligentQuestionnaire:
    """智能问卷组件"""
    
    def __init__(self):
        """初始化组件"""
        logger.info("✅ 智能问卷组件初始化")
        self.digital_human_info = None
        
    async def initialize(self, digital_human_info: Dict[str, Any]):
        """初始化问卷
        
        Args:
            digital_human_info: 数字人信息
        """
        self.digital_human_info = digital_human_info
        logger.info(f"✅ 模拟初始化问卷，数字人信息: {digital_human_info}")
        await asyncio.sleep(0.1)
        
    async def execute(self, page: Any):
        """执行问卷填写
        
        Args:
            page: 页面实例
        """
        logger.info("✅ 模拟执行问卷填写")
        await asyncio.sleep(0.1)
        
    async def cleanup(self):
        """清理资源"""
        logger.info("✅ 模拟清理问卷资源")
        await asyncio.sleep(0.1)

class QuestionnaireStateManager:
    """智能问卷状态管理器 - 实现精确的作答状态追踪和重复避免"""
    
    def __init__(self, session_id: str, persona_name: str):
        self.session_id = session_id
        self.persona_name = persona_name
        self.answered_questions = set()  # 已答题目的唯一标识
        self.current_page_area = 0       # 当前页面区域
        self.scroll_position = 0         # 滚动位置
        self.total_questions_found = 0   # 发现的题目总数
        self.area_completion_status = {} # 每个区域的完成状态
        self.answer_history = []         # 答题历史记录
        self.last_scroll_time = 0        # 上次滚动时间
        self.consecutive_no_new_questions = 0  # 连续没发现新题目的次数
        self.logger = logging.getLogger(__name__)
        
    def mark_question_answered(self, question_identifier: str, answer_content: str) -> bool:
        """标记题目已答，返回是否为新答题"""
        if question_identifier in self.answered_questions:
            self.logger.debug(f"🔄 题目{question_identifier}已答过，跳过")
            return False
        
        self.answered_questions.add(question_identifier)
        self.answer_history.append({
            "question_id": question_identifier,
            "answer": answer_content,
            "timestamp": time.time(),
            "area": self.current_page_area
        })
        self.logger.info(f"✅ 新答题记录: {question_identifier} -> {answer_content[:50]}")
        return True
        
    def is_question_answered(self, question_identifier: str) -> bool:
        """检查题目是否已答"""
        return question_identifier in self.answered_questions
        
class QuestionnaireAnalyzer:
    """问卷页面分析器 - 负责分析问卷结构和元素定位"""
    
    def __init__(self, page):
        self.page = page
        self.logger = logging.getLogger(__name__)
        
    async def analyze_questionnaire_structure(self) -> Dict:
        """分析当前页面的问卷结构"""
        try:
            # 执行页面分析脚本
            structure = await self.page.evaluate("""
                () => {
                    const structure = {
                        total_questions: 0,
                        radio_questions: [],
                        checkbox_questions: [],
                        text_questions: [],
                        select_questions: [],
                        success: true
                    };
                    
                    // 1. 分析单选题
                    document.querySelectorAll('input[type="radio"]').forEach((radio, index) => {
                        const group = radio.closest('.question-group, .radio-group');
                        if (!group) return;
                        
                        const groupId = group.id || `radio_group_${index}`;
                        const questionText = group.querySelector('.question-text, .title')?.innerText || '';
                        
                        // 检查是否已有该组
                        const existingGroup = structure.radio_questions.find(g => g.name === groupId);
                        if (!existingGroup) {
                            structure.radio_questions.push({
                                type: 'radio',
                                name: groupId,
                                text: questionText,
                                options: [],
                                is_answered: false
                            });
                            structure.total_questions++;
                        }
                        
                        // 添加选项
                        const option = {
                            value: radio.value,
                            text: radio.nextElementSibling?.innerText || radio.value,
                            checked: radio.checked
                        };
                        
                        const group_index = structure.radio_questions.findIndex(g => g.name === groupId);
                        if (group_index !== -1) {
                            structure.radio_questions[group_index].options.push(option);
                            if (radio.checked) {
                                structure.radio_questions[group_index].is_answered = true;
                            }
                        }
                    });
                    
                    // 2. 分析多选题
                    document.querySelectorAll('input[type="checkbox"]').forEach((checkbox, index) => {
                        const group = checkbox.closest('.question-group, .checkbox-group');
                        if (!group) return;
                        
                        const groupId = group.id || `checkbox_group_${index}`;
                        const questionText = group.querySelector('.question-text, .title')?.innerText || '';
                        
                        // 检查是否已有该组
                        const existingGroup = structure.checkbox_questions.find(g => g.name === groupId);
                        if (!existingGroup) {
                            structure.checkbox_questions.push({
                                type: 'checkbox',
                                name: groupId,
                                text: questionText,
                                options: [],
                                is_answered: false
                            });
                            structure.total_questions++;
                        }
                        
                        // 添加选项
                        const option = {
                            value: checkbox.value,
                            text: checkbox.nextElementSibling?.innerText || checkbox.value,
                            checked: checkbox.checked
                        };
                        
                        const group_index = structure.checkbox_questions.findIndex(g => g.name === groupId);
                        if (group_index !== -1) {
                            structure.checkbox_questions[group_index].options.push(option);
                            if (checkbox.checked) {
                                structure.checkbox_questions[group_index].is_answered = true;
                            }
                        }
                    });
                    
                    // 3. 分析文本题
                    document.querySelectorAll('input[type="text"], textarea').forEach((input, index) => {
                        const container = input.closest('.question-group, .text-group');
                        if (!container) return;
                        
                        structure.text_questions.push({
                            type: 'text',
                            name: input.name || `text_${index}`,
                            text: container.querySelector('.question-text, .title')?.innerText || '',
                            value: input.value,
                            is_answered: input.value.trim().length > 0
                        });
                        structure.total_questions++;
                    });
                    
                    // 4. 分析下拉框
                    document.querySelectorAll('select').forEach((select, index) => {
                        const container = select.closest('.question-group, .select-group');
                        if (!container) return;
                        
                        const options = Array.from(select.options).map(option => ({
                            value: option.value,
                            text: option.text,
                            selected: option.selected
                        }));
                        
                        structure.select_questions.push({
                            type: 'select',
                            name: select.name || `select_${index}`,
                            text: container.querySelector('.question-text, .title')?.innerText || '',
                            options: options,
                            is_answered: select.value !== '' && select.selectedIndex > 0
                        });
                        structure.total_questions++;
                    });
                    
                    return structure;
                }
            """)
            
            self.logger.info(f"✅ 页面分析完成: 发现{structure['total_questions']}个题目")
            return structure
            
        except Exception as e:
            self.logger.error(f"❌ 页面分析失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
            
    async def find_submit_button(self) -> Optional[Dict]:
        """查找提交按钮"""
        try:
            submit_info = await self.page.evaluate("""
                () => {
                    // 常见的提交按钮选择器
                    const submitSelectors = [
                        'input[type="submit"]',
                        'button[type="submit"]',
                        'button:contains("提交")',
                        '.submit-button',
                        '#submit',
                        '#submitButton'
                    ];
                    
                    for (const selector of submitSelectors) {
                        const button = document.querySelector(selector);
                        if (button && button.offsetParent !== null) {
                            return {
                                found: true,
                                selector: selector,
                                text: button.innerText || button.value || '提交',
                                disabled: button.disabled
                            };
                        }
                    }
                    
                    return { found: false };
                }
            """)
            
            if submit_info["found"]:
                self.logger.info(f"✅ 发现提交按钮: {submit_info.get('text')}")
            return submit_info
            
        except Exception as e:
            self.logger.error(f"❌ 查找提交按钮失败: {e}")
            return None
            
    async def find_next_page_button(self) -> Optional[Dict]:
        """查找下一页按钮"""
        try:
            next_page_info = await self.page.evaluate("""
                () => {
                    // 常见的下一页按钮选择器
                    const nextPageSelectors = [
                        'button:contains("下一页")',
                        'a:contains("下一页")',
                        '.next-page',
                        '#nextPage',
                        '#next-button'
                    ];
                    
                    for (const selector of nextPageSelectors) {
                        const button = document.querySelector(selector);
                        if (button && button.offsetParent !== null) {
                            return {
                                found: true,
                                selector: selector,
                                text: button.innerText || '下一页',
                                disabled: button.disabled
                            };
                        }
                    }
                    
                    return { found: false };
                }
            """)
            
            if next_page_info["found"]:
                self.logger.info(f"✅ 发现下一页按钮: {next_page_info.get('text')}")
            return next_page_info
            
        except Exception as e:
            self.logger.error(f"❌ 查找下一页按钮失败: {e}")
            return None
            
class IntelligentAnswerEngine:
    """智能答题引擎 - 负责执行具体的答题操作"""
    
    def __init__(self, page, state_manager: QuestionnaireStateManager):
        self.page = page
        self.state_manager = state_manager
        self.logger = logging.getLogger(__name__)
        
    async def answer_current_page(self, structure: Dict, persona_info: Dict) -> Dict:
        """回答当前页面的所有题目"""
        try:
            answered_count = 0
            skipped_count = 0
            error_count = 0
            
            # 1. 处理单选题
            for radio_group in structure.get("radio_questions", []):
                if radio_group.get("is_answered", False):
                    question_id = f"radio_{radio_group['name']}"
                    if not self.state_manager.is_question_answered(question_id):
                        self.state_manager.mark_question_answered(question_id, "已选择")
                    skipped_count += 1
                    continue
                    
                try:
                    answer_result = await self._answer_radio_question(radio_group, persona_info)
                    if answer_result["success"]:
                        answered_count += 1
                    else:
                        error_count += 1
                except Exception as e:
                    self.logger.warning(f"⚠️ 单选题作答失败: {e}")
                    error_count += 1
                    
                # 添加人类化延迟
                await asyncio.sleep(0.5)
                
            # 2. 处理多选题
            for checkbox_group in structure.get("checkbox_questions", []):
                if checkbox_group.get("is_answered", False):
                    question_id = f"checkbox_{checkbox_group['name']}"
                    if not self.state_manager.is_question_answered(question_id):
                        self.state_manager.mark_question_answered(question_id, "已选择")
                    skipped_count += 1
                    continue
                    
                try:
                    answer_result = await self._answer_checkbox_question(checkbox_group, persona_info)
                    if answer_result["success"]:
                        answered_count += 1
                    else:
                        error_count += 1
                except Exception as e:
                    self.logger.warning(f"⚠️ 多选题作答失败: {e}")
                    error_count += 1
                    
                await asyncio.sleep(0.5)
                
            # 3. 处理文本题
            for text_question in structure.get("text_questions", []):
                if text_question.get("is_answered", False):
                    question_id = f"text_{text_question['name']}"
                    if not self.state_manager.is_question_answered(question_id):
                        self.state_manager.mark_question_answered(question_id, text_question["value"])
                    skipped_count += 1
                    continue
                    
                try:
                    answer_result = await self._answer_text_question(text_question, persona_info)
                    if answer_result["success"]:
                        answered_count += 1
                    else:
                        error_count += 1
                except Exception as e:
                    self.logger.warning(f"⚠️ 文本题作答失败: {e}")
                    error_count += 1
                    
                await asyncio.sleep(0.5)
                
            # 4. 处理下拉框
            for select_question in structure.get("select_questions", []):
                if select_question.get("is_answered", False):
                    question_id = f"select_{select_question['name']}"
                    if not self.state_manager.is_question_answered(question_id):
                        self.state_manager.mark_question_answered(question_id, "已选择")
                    skipped_count += 1
                    continue
                    
                try:
                    answer_result = await self._answer_select_question(select_question, persona_info)
                    if answer_result["success"]:
                        answered_count += 1
                    else:
                        error_count += 1
                except Exception as e:
                    self.logger.warning(f"⚠️ 下拉框作答失败: {e}")
                    error_count += 1
                    
                await asyncio.sleep(0.5)
                
            return {
                "success": True,
                "answered": answered_count,
                "skipped": skipped_count,
                "errors": error_count
            }
            
        except Exception as e:
            self.logger.error(f"❌ 页面作答失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
            
    async def _answer_radio_question(self, radio_group: Dict, persona_info: Dict) -> Dict:
        """回答单选题"""
        try:
            # 根据题目内容选择合适的选项
            selected_option = radio_group["options"][0]  # 默认选第一个
            
            # 点击选项
            await self.page.click(f'input[name="{radio_group["name"]}"][value="{selected_option["value"]}"]')
            
            # 标记已答
            self.state_manager.mark_question_answered(
                f"radio_{radio_group['name']}", 
                selected_option["text"]
            )
            
            return {"success": True}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
            
    async def _answer_checkbox_question(self, checkbox_group: Dict, persona_info: Dict) -> Dict:
        """回答多选题"""
        try:
            # 选择1-3个选项
            selected_count = min(len(checkbox_group["options"]), 2)
            selected_options = checkbox_group["options"][:selected_count]
            
            for option in selected_options:
                await self.page.click(
                    f'input[name="{checkbox_group["name"]}"][value="{option["value"]}"]'
                )
            
            # 标记已答
            self.state_manager.mark_question_answered(
                f"checkbox_{checkbox_group['name']}", 
                ",".join(opt["text"] for opt in selected_options)
            )
            
            return {"success": True}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
            
    async def _answer_text_question(self, text_question: Dict, persona_info: Dict) -> Dict:
        """回答文本题"""
        try:
            # 根据题目类型生成答案
            answer = "测试回答"  # TODO: 根据题目内容生成合适的答案
            
            # 输入答案
            await self.page.fill(
                f'input[name="{text_question["name"]}"], textarea[name="{text_question["name"]}"]',
                answer
            )
            
            # 标记已答
            self.state_manager.mark_question_answered(
                f"text_{text_question['name']}", 
                answer
            )
            
            return {"success": True}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
            
    async def _answer_select_question(self, select_question: Dict, persona_info: Dict) -> Dict:
        """回答下拉框题目"""
        try:
            # 选择一个非空选项
            valid_options = [opt for opt in select_question["options"] if opt["value"]]
            if not valid_options:
                return {"success": False, "error": "无有效选项"}
                
            selected_option = valid_options[0]
            
            # 选择选项
            await self.page.select(
                f'select[name="{select_question["name"]}"]',
                selected_option["value"]
            )
            
            # 标记已答
            self.state_manager.mark_question_answered(
                f"select_{select_question['name']}", 
                selected_option["text"]
            )
            
            return {"success": True}
            
        except Exception as e:
            return {"success": False, "error": str(e)}

class IntelligentQuestionnaireController:
    """智能问卷主控制器 - 协调浏览器生命周期和作答流程"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.adspower_manager = AdsPowerLifecycleManager()
        self._playwright: Optional[Playwright] = None
        
    async def execute_questionnaire_task(
        self,
        questionnaire_url: str,
        persona_info: Dict,
        proxy_config: Dict
    ) -> Dict:
        """执行问卷任务的主入口方法"""
        try:
            self.logger.info(f"🎯 开始执行问卷任务: {persona_info.get('name')} -> {questionnaire_url}")
            
            # 1. 创建浏览器环境
            browser_env = await self.adspower_manager.create_complete_browser_environment(
                persona_info=persona_info,
                proxy_config=proxy_config
            )
            
            if not browser_env["success"]:
                raise Exception(f"创建浏览器环境失败: {browser_env.get('error')}")
            
            profile_id = browser_env["profile_id"]
            
            try:
                # 2. 初始化Playwright
                p = await async_playwright().start()
                self._playwright = p
                
                # 3. 访问问卷URL
                self.logger.info(f"🌐 正在访问问卷: {questionnaire_url}")
                page = await self._navigate_to_questionnaire(
                    browser_env["debug_port"],
                    questionnaire_url
                )
                
                if not page:
                    raise Exception("无法访问问卷页面")
                
                # 4. 执行智能作答
                self.logger.info("📝 开始智能作答流程...")
                answer_result = await self._execute_answer_flow(
                    page,
                    persona_info
                )
                
                if not answer_result["success"]:
                    raise Exception(f"智能作答失败: {answer_result.get('error')}")
                
                return {
                    "success": True,
                    "message": "问卷完成",
                    "profile_id": profile_id,
                    "answer_stats": answer_result.get("stats", {})
                }
                
            except Exception as e:
                self.logger.error(f"❌ 问卷执行过程出错: {e}")
                return {
                    "success": False,
                    "error": str(e),
                    "profile_id": profile_id
                }
                
            finally:
                # 5. 清理资源
                self.logger.info("🧹 清理资源...")
                if self._playwright:
                    await self._playwright.stop()
                await self.adspower_manager.cleanup_resources(profile_id)
                
        except Exception as e:
            self.logger.error(f"❌ 问卷任务执行失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
            
    async def _navigate_to_questionnaire(self, debug_port: int, url: str) -> Optional[Page]:
        """导航到问卷页面"""
        try:
            # 1. 连接到浏览器
            browser = await self._connect_browser(debug_port)
            if not browser:
                raise Exception("无法连接到浏览器")
                
            # 2. 创建新页面
            page = await browser.new_page()
            
            # 3. 设置页面视口
            await page.set_viewport_size({"width": 1280, "height": 800})
            
            # 4. 导航到URL
            await page.goto(url, wait_until="networkidle")
            
            # 5. 等待页面加载完成
            await page.wait_for_load_state("domcontentloaded")
            
            # 6. 等待页面稳定
            await asyncio.sleep(2)
            
            return page
            
        except Exception as e:
            self.logger.error(f"❌ 页面导航失败: {e}")
            return None
            
    async def _connect_browser(self, debug_port: int) -> Optional[BrowserContext]:
        """连接到AdsPower浏览器"""
        try:
            if not self._playwright:
                raise Exception("Playwright未初始化")
                
            # 1. 构建CDP URL
            cdp_url = f"http://127.0.0.1:{debug_port}"
            
            # 2. 连接到浏览器
            browser = await self._playwright.chromium.connect_over_cdp(cdp_url)
            
            # 3. 获取默认上下文
            context = browser.contexts[0]
            
            return context
            
        except Exception as e:
            self.logger.error(f"❌ 浏览器连接失败: {e}")
            return None
            
    async def _execute_answer_flow(self, page, persona_info: Dict) -> Dict:
        """执行智能作答流程"""
        try:
            # 1. 初始化问卷状态管理器
            state_manager = QuestionnaireStateManager(
                session_id=f"session_{int(time.time())}",
                persona_name=str(persona_info.get("name", "unknown"))  # 修复类型错误
            )
            
            # 2. 初始化页面分析器
            page_analyzer = QuestionnaireAnalyzer(page)
            
            # 3. 初始化答题引擎
            answer_engine = IntelligentAnswerEngine(
                page=page,
                state_manager=state_manager
            )
            
            total_answered = 0
            total_skipped = 0
            total_errors = 0
            
            # 4. 执行答题循环
            while True:
                # 分析当前页面
                structure = await page_analyzer.analyze_questionnaire_structure()
                
                if not structure["success"]:
                    raise Exception(f"页面分析失败: {structure.get('error')}")
                    
                # 检查是否有题目
                if structure.get("total_questions", 0) == 0:
                    self.logger.info("📭 当前页面无题目,检查是否完成")
                    
                    # 检查提交按钮
                    submit_button = await page_analyzer.find_submit_button()
                    if submit_button and submit_button.get("found"):
                        self.logger.info("🎯 发现提交按钮,尝试提交...")
                        submit_result = await self._attempt_submit(page, submit_button)
                        if submit_result["success"]:
                            break
                            
                    # 检查是否需要翻页
                    next_page = await page_analyzer.find_next_page_button()
                    if next_page and next_page.get("found"):
                        self.logger.info("📄 发现下一页按钮,准备翻页...")
                        next_result = await self._go_to_next_page(page, next_page)
                        if not next_result["success"]:
                            raise Exception(f"翻页失败: {next_result.get('error')}")
                        continue
                        
                    self.logger.info("🎯 未发现更多操作按钮,可能已完成")
                    break
                    
                # 执行答题
                answer_result = await answer_engine.answer_current_page(
                    structure,
                    persona_info
                )
                
                if not answer_result["success"]:
                    raise Exception(f"答题失败: {answer_result.get('error')}")
                    
                total_answered += answer_result.get("answered", 0)
                total_skipped += answer_result.get("skipped", 0)
                total_errors += answer_result.get("errors", 0)
                
                self.logger.info(f"✅ 本页完成: 答题{answer_result.get('answered', 0)}个")
                
                # 检查是否需要翻页
                next_page = await page_analyzer.find_next_page_button()
                if next_page and next_page.get("found"):
                    next_result = await self._go_to_next_page(page, next_page)
                    if not next_result["success"]:
                        raise Exception(f"翻页失败: {next_result.get('error')}")
                else:
                    self.logger.info("📄 未发现下一页按钮,继续检查当前页")
                    
            return {
                "success": True,
                "message": "智能作答完成",
                "stats": {
                    "total_answered": total_answered,
                    "total_skipped": total_skipped,
                    "total_errors": total_errors
                }
            }
            
        except Exception as e:
            self.logger.error(f"❌ 智能作答流程失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
            
    async def _attempt_submit(self, page, submit_info: Dict) -> Dict:
        """尝试提交问卷"""
        try:
            self.logger.info(f"🎯 点击提交按钮: {submit_info.get('text', '提交')}")
            
            # 点击提交按钮
            await page.click(submit_info["selector"])
            
            # 等待提交结果
            await asyncio.sleep(2)
            
            # 检查是否提交成功
            success_text = await page.evaluate("""
                () => {
                    const successTexts = [
                        "提交成功", "已完成", "感谢参与",
                        "问卷提交成功", "答题完成"
                    ];
                    
                    for (const text of successTexts) {
                        if (document.body.innerText.includes(text)) {
                            return text;
                        }
                    }
                    return null;
                }
            """)
            
            if success_text:
                self.logger.info(f"✅ 提交成功: {success_text}")
                return {
                    "success": True,
                    "message": success_text
                }
            else:
                # 检查是否有错误提示
                error_text = await page.evaluate("""
                    () => {
                        const errorTexts = document.querySelectorAll('.error-message, .alert-error');
                        return Array.from(errorTexts).map(el => el.innerText).join(', ');
                    }
                """)
                
                if error_text:
                    raise Exception(f"提交失败: {error_text}")
                else:
                    raise Exception("提交后未发现成功提示")
                    
        except Exception as e:
            self.logger.error(f"❌ 提交失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
            
    async def _go_to_next_page(self, page, next_page_info: Dict) -> Dict:
        """跳转到下一页"""
        try:
            self.logger.info(f"📄 点击下一页按钮: {next_page_info.get('text', '下一页')}")
            
            # 点击下一页按钮
            await page.click(next_page_info["selector"])
            
            # 等待页面加载
            await page.wait_for_load_state("networkidle")
            
            # 等待页面稳定
            await asyncio.sleep(1)
            
            return {
                "success": True,
                "message": "成功跳转到下一页"
            }
            
        except Exception as e:
            self.logger.error(f"❌ 跳转失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

if __name__ == "__main__":
    print("📋 智能问卷系统修正补丁已准备好")
    print("请手动将INTELLIGENT_QUESTIONNAIRE_METHOD_FIX的内容")
    print("替换adspower_browser_use_integration.py中的execute_intelligent_questionnaire_task方法") 