#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
三阶段智能问卷核心系统
基于AdsPower+青果代理+WebUI的正确架构实现

情报收集 → 分析 → 指导作战
"""

import asyncio
import json
import logging
import time
import uuid
import base64
import io
import random
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
import requests
from PIL import Image, ImageDraw

# 初始化日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 导入正确的AdsPower+WebUI集成
try:
    from adspower_browser_use_integration import (
        <PERSON>rowser,
        BrowserConfig,
        BrowserContextConfig,
        BrowserUseAgent
    )
    from enhanced_adspower_lifecycle import EnhancedAdsPowerLifecycle
    ADSPOWER_WEBUI_AVAILABLE = True
    logger.info("✅ AdsPower+WebUI集成模块导入成功")
except ImportError as e:
    logger.warning(f"⚠️ AdsPower+WebUI集成模块导入失败: {e}")
    ADSPOWER_WEBUI_AVAILABLE = False

# 尝试导入Gemini API，如果失败则使用模拟版本
try:
    from google.generativeai.client import configure as genai_configure
    from google.generativeai.generative_models import GenerativeModel
    GEMINI_AVAILABLE = True
    logger.info("✅ Google Generative AI 导入成功")
except ImportError:
    logger.warning("⚠️ Google Generative AI 包未安装，将使用模拟分析")
    GEMINI_AVAILABLE = False
    genai_configure = None
    GenerativeModel = None
except Exception as e:
    logger.warning(f"⚠️ Google Generative AI 导入异常: {e}，将使用模拟分析")
    GEMINI_AVAILABLE = False
    genai_configure = None
    GenerativeModel = None

# 导入现有的数据库和小社会系统组件
try:
    from questionnaire_system import XiaosheSystemClient, QuestionnaireKnowledgeBase, DatabaseManager
    SYSTEM_COMPONENTS_AVAILABLE = True
    logger.info("✅ 系统组件导入成功")
except ImportError as e:
    logger.warning(f"⚠️ 系统组件导入失败: {e}")
    SYSTEM_COMPONENTS_AVAILABLE = False

@dataclass
class ScoutExperience:
    """敢死队经验数据结构"""
    scout_id: str
    scout_name: str
    page_number: int
    page_screenshot: str  # base64编码的截图
    page_content: str     # 页面文字内容
    questions_answered: List[Dict]
    success: bool
    failure_reason: Optional[str]
    timestamp: str
    
    # 🔧 新增：详细的错误分类和答题统计
    error_type: str = "none"  # "none", "code_error", "server_error", "api_error", "trap_termination", "normal_completion"
    questions_count: int = 0  # 实际答题数量
    completion_depth: float = 0.0  # 答题深度（0.0-1.0）
    trap_triggered: bool = False  # 是否触发陷阱题
    browser_error_displayed: bool = False  # 是否在浏览器显示了错误悬浮框
    technical_error_details: Optional[str] = None  # 技术错误详情（用于调试）

@dataclass
class QuestionnaireIntelligence:
    """问卷情报分析结果"""
    target_audience: Dict  # 目标人群特征
    questionnaire_theme: str  # 问卷主题
    trap_questions: List[Dict]  # 陷阱题目
    success_patterns: List[Dict]  # 成功模式
    failure_patterns: List[Dict]  # 失败模式
    recommended_strategies: List[str]  # 推荐策略
    confidence_score: float  # 分析可信度
    guidance_rules: List['GuidanceRule'] = field(default_factory=list)  # 指导规则

@dataclass
class GuidanceRule:
    """指导规则"""
    rule_id: str
    question_pattern: str
    recommended_answer: str
    reasoning: str
    confidence: float
    success_rate: float

class QingguoProxyManager:
    """青果代理管理器"""
    
    def __init__(self, api_key: str = ""):
        from src.config.proxy_config import get_proxy_config
        self.config = get_proxy_config()
        self.api_key = api_key or self.config["auth_key"]
        self.api_base_url = self.config["base_url"]
        self.logger = logging.getLogger(__name__)
        
    async def get_proxy(self) -> Dict:
        """获取代理配置"""
        try:
            # 调用青果API获取代理
            response = requests.get(
                f"{self.api_base_url}/get_ip",
                params={
                    "key": self.api_key,
                    "num": 1,
                    "format": "json",
                    "protocol": 1,  # http/https
                    "lb": 1,  # 负载均衡
                    "regions": "中国"
                },
                timeout=self.config["timeout"]
            )
            
            if response.status_code != 200:
                raise Exception(f"青果API请求失败: {response.status_code}")
                
            data = response.json()
            if not data.get("code") == 200:
                raise Exception(f"青果API返回错误: {data.get('msg')}")
                
            proxy_info = data.get("data", [])[0]
            if not proxy_info:
                raise Exception("未获取到代理信息")
                
            # 解析代理信息
            proxy_config = {
                "proxy_type": "http",
                "proxy_host": proxy_info.get("ip"),
                "proxy_port": str(proxy_info.get("port")),
                "username": proxy_info.get("username", ""),
                "password": proxy_info.get("password", "")
            }
            
            self.logger.info(f"✅ 成功获取青果代理: {proxy_config['proxy_host']}:{proxy_config['proxy_port']}")
            return proxy_config
            
        except Exception as e:
            self.logger.error(f"❌ 获取青果代理失败: {e}")
            return {
                "proxy_type": "http",
                "proxy_host": "",
                "proxy_port": "",
                "username": "",
                "password": ""
            }

class ThreeStageIntelligentCore:
    """三阶段智能核心系统 - 基于AdsPower+WebUI的正确架构"""
    
    def __init__(self):
        self.gemini_api_key = "AIzaSyAfmaTObVEiq6R_c62T4jeEpyf6yp4WCP8"
        self.gemini_model = "gemini-2.0-flash"
        self.knowledge_base_url = "http://localhost:5003"
        self.xiaoshe_api_url = "http://localhost:5001"
        self.qingguo_api_key = "your_api_key_here"  # 替换为实际的API Key
        
        # 初始化青果代理管理器
        self.proxy_manager = QingguoProxyManager(api_key=self.qingguo_api_key)
        
        # 初始化AdsPower生命周期管理器
        self.lifecycle_manager = EnhancedAdsPowerLifecycle()
        
        # 初始化小社会系统客户端
        if SYSTEM_COMPONENTS_AVAILABLE:
            try:
                xiaoshe_config = {
                    "base_url": self.xiaoshe_api_url,
                    "timeout": 30
                }
                self.xiaoshe_client = XiaosheSystemClient(xiaoshe_config)
                logger.info("✅ 小社会系统客户端初始化成功")
            except Exception as e:
                logger.warning(f"⚠️ 小社会系统客户端初始化失败: {e}")
                self.xiaoshe_client = None
        else:
            self.xiaoshe_client = None
        
        # 初始化Gemini（如果可用）
        if GEMINI_AVAILABLE and genai_configure and GenerativeModel:
            try:
                genai_configure(api_key=self.gemini_api_key)
                self.model = GenerativeModel(self.gemini_model)
                logger.info("✅ Gemini API 初始化成功")
            except Exception as e:
                logger.error(f"❌ Gemini API 初始化失败: {e}")
                self.model = None
        else:
            self.model = None
        
        # 🆕 新增：初始化Gemini分析会话数据存储
        self.session_gemini_analysis = {}
        
        logger.info("✅ 三阶段智能核心系统初始化完成")
    
    async def execute_complete_three_stage_workflow(
        self, 
        questionnaire_url: str, 
        scout_count: int = 2, 
        target_count: int = 10
    ) -> Dict[str, Any]:
        """执行完整的三阶段工作流"""
        session_id = str(uuid.uuid4())
        logger.info(f"🚀 开始执行三阶段工作流 (会话ID: {session_id})")
        
        try:
            # 第一阶段：情报收集
            logger.info("🔍 第一阶段：情报收集")
            scout_experiences = await self._execute_scout_phase(
                session_id,
                questionnaire_url,
                scout_count
            )
            
            if not scout_experiences:
                raise Exception("情报收集阶段失败")
            
            # 第二阶段：分析
            logger.info("🧠 第二阶段：分析")
            intelligence = await self._execute_analysis_phase(
                session_id,
                questionnaire_url,
                scout_experiences
            )
            
            if not intelligence:
                raise Exception("分析阶段失败")
            
            # 第三阶段：指导作战
            logger.info("🎯 第三阶段：指导作战")
            target_results = await self._execute_target_phase(
                session_id,
                questionnaire_url,
                intelligence,
                target_count
            )
            
            # 生成最终报告
            final_report = self._generate_final_report(
                session_id,
                scout_experiences,
                intelligence,
                target_results
            )
            
            return {
                "status": "success",
                "session_id": session_id,
                "report": final_report
            }
            
        except Exception as e:
            logger.error(f"❌ 工作流执行失败: {e}")
            return {
                "status": "failed",
                "session_id": session_id,
                "error": str(e)
            }
    
    def _serialize_experience(self, exp: ScoutExperience) -> Dict:
        """序列化敢死队经验"""
        return {
            "scout_id": exp.scout_id,
            "scout_name": exp.scout_name,
            "page_number": exp.page_number,
            "page_content": exp.page_content,
            "questions_answered": exp.questions_answered,
            "success": exp.success,
            "failure_reason": exp.failure_reason,
            "timestamp": exp.timestamp
        }
    
    def _serialize_intelligence(self, intelligence: QuestionnaireIntelligence) -> Dict:
        """序列化智能分析结果"""
        return {
            "target_audience": intelligence.target_audience,
            "questionnaire_theme": intelligence.questionnaire_theme,
            "trap_questions": intelligence.trap_questions,
            "success_patterns": intelligence.success_patterns,
            "failure_patterns": intelligence.failure_patterns,
            "recommended_strategies": intelligence.recommended_strategies,
            "confidence_score": intelligence.confidence_score
        }
    
    def _serialize_rule(self, rule: GuidanceRule) -> Dict:
        """序列化指导规则"""
        return {
            "rule_id": rule.rule_id,
            "question_pattern": rule.question_pattern,
            "recommended_answer": rule.recommended_answer,
            "reasoning": rule.reasoning,
            "confidence": rule.confidence,
            "success_rate": rule.success_rate
        }
    
    async def _execute_scout_phase(
        self, 
        session_id: str, 
        questionnaire_url: str, 
        scout_count: int
    ) -> List[ScoutExperience]:
        """执行侦察阶段"""
        try:
            # 招募多样化的侦察员
            scouts = await self._recruit_diverse_scouts(scout_count)
            if not scouts:
                raise Exception("无法招募侦察员")
            
            # 执行侦察任务
            all_experiences = []
            for scout in scouts:
                experiences = await self._execute_single_scout_mission(
                    session_id,
                    questionnaire_url,
                    scout,
                    len(all_experiences)
                )
                all_experiences.extend(experiences)
            
            return all_experiences
            
        except Exception as e:
            logger.error(f"侦察阶段执行失败: {e}")
            return []
    
    async def _recruit_diverse_scouts(self, scout_count: int) -> List[Dict]:
        """招募多样化的敢死队成员"""
        logger.info(f"👥 正在招募 {scout_count} 个多样化敢死队成员...")
        
        try:
            personas = []
            
            if self.xiaoshe_client:
                # 使用真实的小社会系统
                queries = [
                    "年轻人，学生或刚毕业，喜欢尝试新事物",
                    "中年上班族，有工作经验，谨慎决策",
                    "女性，关注生活品质，注重品牌",
                    "男性，技术相关工作，理性消费",
                    "家庭主妇，负责家庭采购决策",
                    "高收入人群，对价格不敏感",
                    "年轻白领，追求时尚潮流",
                    "退休人员，注重实用性和性价比"
                ]
                
                for i in range(scout_count):
                    query = queries[i % len(queries)]
                    try:
                        # 使用正确的API调用方式
                        result_personas = await self.xiaoshe_client.query_personas(query, 1)
                        if result_personas:
                            persona = result_personas[0]
                            persona["scout_id"] = f"scout_{i+1}_{uuid.uuid4().hex[:6]}"
                            personas.append(persona)
                            logger.info(f"✅ 招募敢死队成员{i+1}: {persona.get('name', '未知')}")
                        else:
                            # 备用数字人
                            personas.append(self._create_backup_persona(i))
                    except Exception as e:
                        logger.warning(f"⚠️ 查询数字人失败: {e}，使用备用数字人")
                        personas.append(self._create_backup_persona(i))
            else:
                # 使用备用数字人
                for i in range(scout_count):
                    personas.append(self._create_backup_persona(i))
            
            logger.info(f"✅ 成功招募 {len(personas)} 个敢死队成员")
            return personas
            
        except Exception as e:
            logger.error(f"❌ 招募敢死队失败: {e}")
            # 返回默认敢死队
            return [self._create_backup_persona(i) for i in range(scout_count)]
    
    def _create_backup_persona(self, index: int) -> Dict:
        """创建备用数字人"""
        personas = [
            {"name": "张三", "age": 25, "gender": "男", "profession": "程序员", "education_level": "本科"},
            {"name": "李四", "age": 32, "gender": "女", "profession": "教师", "education_level": "硕士"},
            {"name": "王五", "age": 28, "gender": "男", "profession": "销售", "education_level": "本科"},
            {"name": "赵六", "age": 35, "gender": "女", "profession": "会计", "education_level": "本科"},
            {"name": "孙七", "age": 30, "gender": "男", "profession": "医生", "education_level": "博士"}
        ]
        
        base_persona = personas[index % len(personas)]
        return {
            "scout_id": f"scout_{index+1}_{uuid.uuid4().hex[:6]}",
            "name": base_persona["name"],
            "age": base_persona["age"],
            "gender": base_persona["gender"],
            "profession": base_persona["profession"],
            "education_level": base_persona["education_level"],
            "income_level": "中等",
            "marital_status": "未婚" if base_persona["age"] < 30 else "已婚"
        }
    
    async def _execute_single_scout_mission(
        self, 
        session_id: str, 
        questionnaire_url: str, 
        persona: Dict, 
        scout_index: int
    ) -> List[ScoutExperience]:
        """执行单个侦察任务"""
        try:
            # 生成浏览器配置文件名
            profile_name = f"scout_{session_id}_{scout_index}_{persona.get('id', uuid.uuid4().hex[:8])}"
            
            # 创建浏览器环境
            browser_env = await self.lifecycle_manager.create_complete_browser_environment(
                profile_name=profile_name
            )
            
            if not browser_env or browser_env.get("status") != "success":
                raise Exception("创建浏览器环境失败")
            
            # 🔥 核心修复：直接调用智能作答流程，不需要额外的结果提取
            experiences = await self._execute_questionnaire_in_adspower(
                browser_env["browser_config"],
                persona,
                questionnaire_url
            )

            # 🔥 修复：无论成功失败都保持浏览器开启
            # 让用户可以查看结果和调试问题
            if experiences and len(experiences) > 0:
                experience = experiences[0]
                if not experience.success:
                    logger.info("❌ 智能作答失败，但保持浏览器窗口开启以便调试")
                else:
                    logger.info("✅ 智能作答成功，保持浏览器窗口开启")
            else:
                logger.info("❌ 未获得有效结果，但保持浏览器窗口开启以便调试")

            return experiences
            
        except Exception as e:
            logger.error(f"侦察任务执行失败: {e}")

            # 🔥 异常情况下也保持浏览器开启以便调试
            logger.info("⚠️ 异常情况下保持浏览器窗口开启以便调试问题")

            # 创建失败经验记录
            experience = ScoutExperience(
                scout_id=str(persona.get('id', 1)),
                scout_name=persona.get('name', '未知'),
                page_number=1,
                page_screenshot='',
                page_content='',
                questions_answered=[],
                success=False,
                failure_reason=str(e),
                timestamp=datetime.now().isoformat(),
                error_type="code_error",
                questions_count=0,
                completion_depth=0.0,
                trap_triggered=False,
                browser_error_displayed=False,
                technical_error_details=str(e)
            )

            return [experience]
            
    async def _execute_questionnaire_in_adspower(
        self,
        browser_config: Dict,
        persona: Dict,
        questionnaire_url: str,
        enhanced_prompt: Optional[str] = ""
    ) -> List[ScoutExperience]:
        """在AdsPower浏览器中执行问卷填写 - 调用智能作答流程"""
        try:
            logger.info(f"🚀 开始执行智能问卷填写")
            logger.info(f"   数字人: {persona.get('name', '未知')}")
            logger.info(f"   问卷URL: {questionnaire_url}")
            
            # 🔥 核心修复：使用标准browser_use.Agent类进行智能作答
            result = await self._run_standard_browser_use_agent(
                persona=persona,
                questionnaire_url=questionnaire_url,
                browser_config=browser_config,
                enhanced_prompt=enhanced_prompt
            )
            
            logger.info(f"✅ 智能作答流程完成: {result.get('success', False)}")
            
            # 🔧 将结果转换为ScoutExperience格式
            experiences = []
            
            if result.get("success"):
                # 成功情况：从结果中提取经验
                experience = ScoutExperience(
                    scout_id=str(persona.get('id', 1)),
                    scout_name=persona.get('name', '未知'),
                    page_number=1,
                    page_screenshot=result.get('screenshot', ''),
                    page_content=result.get('page_content', ''),
                    questions_answered=result.get('questions_answered', []),
                    success=True,
                    failure_reason=None,
                    timestamp=datetime.now().isoformat(),
                    error_type="normal_completion",
                    questions_count=len(result.get('questions_answered', [])),
                    completion_depth=result.get('completion_depth', 1.0),
                    trap_triggered=result.get('trap_triggered', False),
                    browser_error_displayed=False,
                    technical_error_details=None
                )
                experiences.append(experience)
            else:
                # 失败情况：创建失败经验记录
                error_msg = result.get('error', '未知错误')
                error_type = "code_error"
                
                # 根据错误类型分类
                if "API" in error_msg or "api" in error_msg:
                    error_type = "api_error"
                elif "服务器" in error_msg or "server" in error_msg:
                    error_type = "server_error"
                elif "网络" in error_msg or "network" in error_msg:
                    error_type = "api_error"
                
                experience = ScoutExperience(
                    scout_id=str(persona.get('id', 1)),
                    scout_name=persona.get('name', '未知'),
                    page_number=1,
                    page_screenshot='',
                    page_content='',
                    questions_answered=[],
                    success=False,
                    failure_reason=error_msg,
                    timestamp=datetime.now().isoformat(),
                    error_type=error_type,
                    questions_count=0,
                    completion_depth=0.0,
                    trap_triggered=False,
                    browser_error_displayed=False,
                    technical_error_details=error_msg
                )
                experiences.append(experience)
            
            return experiences
            
        except Exception as e:
            logger.error(f"❌ 问卷填写失败: {e}")
            
            # 创建异常经验记录
            experience = ScoutExperience(
                scout_id=str(persona.get('id', 1)),
                scout_name=persona.get('name', '未知'),
                page_number=1,
                page_screenshot='',
                page_content='',
                questions_answered=[],
                success=False,
                failure_reason=str(e),
                timestamp=datetime.now().isoformat(),
                error_type="code_error",
                questions_count=0,
                completion_depth=0.0,
                trap_triggered=False,
                browser_error_displayed=False,
                technical_error_details=str(e)
            )
            
            return [experience]
    
    async def _run_standard_browser_use_agent(
        self,
        persona: Dict,
        questionnaire_url: str,
        browser_config: Dict,
        enhanced_prompt: Optional[str] = None
    ) -> Dict:
        """使用标准browser_use.Agent类运行智能作答"""
        try:
            logger.info(f"🚀 开始使用标准browser_use.Agent执行智能作答")
            logger.info(f"   数字人: {persona.get('name', '未知')}")
            logger.info(f"   问卷URL: {questionnaire_url}")
            
            # 生成增强提示词
            if not enhanced_prompt:
                enhanced_prompt = self._generate_enhanced_scout_prompt(persona, questionnaire_url)
            
            # 🔥 调用现有的智能作答流程
            from adspower_browser_use_integration import run_intelligent_questionnaire_workflow_with_existing_browser
            
            # 调用智能作答流程
            result = await run_intelligent_questionnaire_workflow_with_existing_browser(
                persona_id=persona.get("id", 1),
                persona_name=persona.get("name", "未知"),
                digital_human_info=persona,
                questionnaire_url=questionnaire_url,
                existing_browser_info=browser_config,
                prompt=enhanced_prompt
            )
            
            logger.info(f"✅ 智能作答流程执行完成: {result.get('success', False)}")
            
            # 返回实际的执行结果
            return result
            
        except Exception as e:
            logger.error(f"❌ 标准Agent执行失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "screenshot": "",
                "page_content": "",
                "questions_answered": [],
                "completion_depth": 0.0,
                "trap_triggered": False
            }
    
    def _generate_enhanced_scout_prompt(self, persona: Dict, questionnaire_url: str) -> str:
        """生成增强的敢死队提示词"""
        name = persona.get("name", "张三")
        age = persona.get("age", 30)
        gender = persona.get("gender", "男")
        profession = persona.get("profession", "上班族")
        education = persona.get("education_level", "本科")
        income = persona.get("income_level", "中等")
        
        prompt = f"""你现在是{name}，{age}岁，{gender}性，职业是{profession}，{education}学历，{income}收入水平。

【重要任务说明】
你是敢死队成员，需要探索这个问卷：{questionnaire_url}

【详细任务要求】
1. 仔细阅读每个页面的所有问题
2. 根据你的身份特征进行真实作答
3. 特别注意识别以下内容：
   - 问卷针对什么人群（年龄、性别、职业要求）
   - 问卷主要考察什么产品或服务
   - 是否有陷阱题目或重复题目
   - 哪些答案选择可能导致问卷终止
4. 每页答完后，记住你的选择和理由
5. 持续作答直到完成或被终止

【作答策略】
- 保持身份一致性，所有回答都要符合{name}的身份
- 遇到不确定的问题，选择最符合身份的选项
- 注意观察页面提示和错误信息
- 如果问卷要求特定条件，尽量满足以继续进行

【重要提醒】
- 你要一直作答直到问卷完成或出现"问卷已结束"等提示
- 如果遇到"不符合条件"等提示，记录原因并继续尝试
- 每次选择都要基于你的真实身份背景

开始执行任务！"""

        return prompt
    
    async def _extract_page_experiences_from_adspower_result(
        self, 
        session_id: str, 
        scout_id: str, 
        scout_name: str, 
        questionnaire_url: str, 
        adspower_result: Any
    ) -> List[ScoutExperience]:
        """从AdsPower执行结果中提取页面经验"""
        if not adspower_result.get("success", False):
            error_msg = adspower_result.get("error", "未知错误")
            raise RuntimeError(f"AdsPower执行失败: {error_msg}")
            
        experiences = []
        
        # 提取页面数据
        page_data = adspower_result.get("page_data", {})
        if not page_data:
            raise ValueError("未找到页面数据")
            
        # 处理每个页面的经验
        for page_num, page_info in enumerate(page_data.get("pages", []), 1):
            experience = ScoutExperience(
                scout_id=scout_id,
                scout_name=scout_name,
                page_number=page_num,
                page_screenshot=page_info.get("screenshot", ""),
                page_content=page_info.get("content", ""),
                questions_answered=page_info.get("questions", []),
                success=True,
                failure_reason=None,
                timestamp=datetime.now().isoformat(),
                error_type="normal_completion",
                questions_count=len(page_info.get("questions", [])),
                completion_depth=page_info.get("completion_depth", 1.0),
                trap_triggered=page_info.get("trap_detected", False),
                browser_error_displayed=False,
                technical_error_details=None
            )
            experiences.append(experience)
            
        return experiences
    
    async def _save_experiences_to_knowledge_base(
        self, 
        session_id: str, 
        experiences: List[ScoutExperience],
        questionnaire_url: Optional[str] = None
    ):
        """保存经验到知识库"""
        try:
            for exp in experiences:
                # 保存每个问题的经验
                for i, qa in enumerate(exp.questions_answered):
                    data = {
                        "session_id": session_id,
                        "questionnaire_url": questionnaire_url or "https://www.wjx.cn/vm/ml5AbmN.aspx",  # 使用实际URL
                        "persona_name": exp.scout_name,
                        "persona_role": "scout",
                        "question_content": qa.get("question", ""),
                        "answer_choice": qa.get("answer", ""),
                        "success": 1 if exp.success else 0,
                        "experience_description": f"{exp.scout_name}在第{exp.page_number}页的答题经验：{qa.get('reasoning', '')}"
                    }
                    
                    # 调用知识库API保存
                    response = requests.post(
                        f"{self.knowledge_base_url}/api/save_experience",
                        json=data,
                        timeout=5
                    )
                    
                    if response.status_code == 200:
                        logger.debug(f"✅ 保存经验成功: {qa.get('question', '')}")
                    else:
                        logger.warning(f"⚠️ 保存经验失败: {response.status_code} - {response.text}")
                        
        except Exception as e:
            logger.warning(f"⚠️ 保存经验到知识库失败: {e}")
    
    async def _execute_analysis_phase(
        self, 
        session_id: str, 
        questionnaire_url: str, 
        scout_experiences: List[ScoutExperience]
    ) -> Optional[QuestionnaireIntelligence]:
        """第二阶段：分析敢死队经验并生成指导规则（基于答题数量和错误类型的智能判断）"""
        logger.info("=" * 60)
        logger.info("📍 第二阶段：智能分析（基于答题数量判断成功性）")
        logger.info("=" * 60)
        
        try:
            # 🔧 核心修复：按照用户需求重新分类经验
            code_server_errors = []  # 代码/服务器错误
            normal_completion_experiences = []  # 正常答题经验（包括被陷阱题终止）
            
            for exp in scout_experiences:
                if exp.error_type in ["code_error", "server_error", "api_error"]:
                    code_server_errors.append(exp)
                    logger.warning(f"⚠️ 发现技术错误: {exp.scout_name} - {exp.error_type}: {exp.technical_error_details}")
                else:
                    # 正常答题经验（包括被陷阱题终止的情况）
                    normal_completion_experiences.append(exp)
            
            logger.info(f"📊 经验分类结果:")
            logger.info(f"   技术错误: {len(code_server_errors)} 个")
            logger.info(f"   正常答题: {len(normal_completion_experiences)} 个")
            
            # 🔧 关键修复1：处理技术错误 - 在浏览器显示悬浮框
            if code_server_errors:
                await self._display_technical_errors_in_browser(code_server_errors)
            
            # 🔧 关键修复2：如果没有正常答题经验，无法进行分析
            if len(normal_completion_experiences) == 0:
                logger.error(f"❌ 所有敢死队都遇到技术错误，无法进行有效分析")
                logger.error(f"🔧 建议：检查代码逻辑、API配置、服务器状态")
                return None
            
            # 🔧 关键修复3：按答题数量排序，确定"相对成功"的经验
            normal_completion_experiences.sort(key=lambda x: x.questions_count, reverse=True)
            
            # 选择答题数量最多的经验作为"成功"经验
            max_questions = normal_completion_experiences[0].questions_count
            successful_experiences = [exp for exp in normal_completion_experiences if exp.questions_count == max_questions]
            failed_experiences = [exp for exp in normal_completion_experiences if exp.questions_count < max_questions]
            
            logger.info(f"📊 按答题数量分析结果:")
            logger.info(f"   最多答题数量: {max_questions} 题")
            logger.info(f"   最成功经验: {len(successful_experiences)} 个")
            logger.info(f"   相对失败经验: {len(failed_experiences)} 个")
            
            # 显示详细的答题情况
            for exp in successful_experiences:
                status = "🏆 最成功" if exp.questions_count == max_questions else "📊 次优"
                trap_info = " (触发陷阱题)" if exp.trap_triggered else ""
                logger.info(f"   {status}: {exp.scout_name} - {exp.questions_count}题{trap_info}")
            
            # 🔧 关键修复4：如果最多答题数量为0，说明所有人都无法开始答题
            if max_questions == 0:
                logger.error(f"❌ 所有敢死队答题数量都为0，可能存在页面加载或题目识别问题")
                logger.error(f"🔧 建议：检查问卷URL、页面加载状态、题目识别逻辑")
                return None
            
            # 🔧 关键修复5：基于相对成功的经验进行分析
            logger.info(f"🧠 开始基于{len(successful_experiences)}个最成功经验进行深度分析...")
            
            # 使用最成功的经验进行分析
            if GEMINI_AVAILABLE:
                try:
                    intelligence = await self._gemini_deep_analysis(
                        session_id, questionnaire_url, successful_experiences, failed_experiences
                    )
                except Exception as gemini_error:
                    logger.warning(f"⚠️ Gemini分析失败，使用本地分析: {gemini_error}")
                    intelligence = self._create_mock_analysis(successful_experiences, failed_experiences)
            else:
                intelligence = self._create_mock_analysis(successful_experiences, failed_experiences)
            
            # 生成指导规则
            intelligence.guidance_rules = await self._generate_guidance_rules(intelligence, successful_experiences)
            
            logger.info(f"✅ 智能分析完成")
            logger.info(f"   分析置信度: {intelligence.confidence_score:.1%}")
            logger.info(f"   指导规则数量: {len(intelligence.guidance_rules)}")
            logger.info(f"   推荐策略数量: {len(intelligence.recommended_strategies)}")
            
            return intelligence
            
        except Exception as e:
            logger.error(f"❌ 分析阶段执行失败: {e}")
            return None
    
    async def _gemini_deep_analysis(
        self, 
        session_id: str, 
        questionnaire_url: str, 
        successful_experiences: List[ScoutExperience], 
        failed_experiences: List[ScoutExperience]
    ) -> QuestionnaireIntelligence:
        """使用Gemini进行深度分析"""
        
        # 构建分析提示词
        analysis_prompt = f"""作为专业的问卷分析师，请深度分析以下敢死队数据：

【分析目标】
问卷URL: {questionnaire_url}

【成功经验数据】
{self._format_experiences_for_analysis(successful_experiences)}

【失败经验数据】
{self._format_experiences_for_analysis(failed_experiences)}

【分析要求】
请进行以下专业分析：

1. 目标人群特征：
   - 年龄范围
   - 性别偏好
   - 职业要求
   - 其他特征

2. 问卷主题识别：
   - 主要考察的产品/服务
   - 调研目的
   - 核心关注点

3. 陷阱题目识别：
   - 容易导致失败的题目
   - 重复验证题目
   - 逻辑陷阱

4. 成功模式总结：
   - 有效的答题策略
   - 成功的选择模式
   - 关键成功因素

5. 失败模式分析：
   - 失败的原因
   - 应避免的选择
   - 改进建议

6. 推荐策略：
   - 针对大部队的具体建议
   - 优化答题成功率的方法

请用JSON格式返回分析结果。"""

        try:
            if self.model:
                response = self.model.generate_content(analysis_prompt)
                analysis_text = response.text
                
                # 解析Gemini的分析结果
                intelligence = self._parse_gemini_analysis(analysis_text)
                
                logger.info("✅ Gemini深度分析完成")
                return intelligence
            else:
                logger.info("⚠️ 使用模拟分析模式")
                return self._create_mock_analysis(successful_experiences, failed_experiences)
            
        except Exception as e:
            logger.error(f"❌ Gemini分析失败: {e}")
            # 返回基础分析结果
            return self._create_mock_analysis(successful_experiences, failed_experiences)
    
    def _create_mock_analysis(self, successful_experiences: List[ScoutExperience], failed_experiences: List[ScoutExperience]) -> QuestionnaireIntelligence:
        """创建模拟分析结果"""
        # 基于实际经验数据生成分析
        all_questions = []
        success_patterns = []
        failure_patterns = []
        
        # 分析成功经验
        for exp in successful_experiences:
            for qa in exp.questions_answered:
                all_questions.append(qa.get("question", ""))
                success_patterns.append({
                    "pattern": f"成功选择: {qa.get('answer', '')}",
                    "question": qa.get("question", ""),
                    "success_rate": 0.8
                })
        
        # 分析失败经验
        for exp in failed_experiences:
            for qa in exp.questions_answered:
                failure_patterns.append({
                    "pattern": f"失败选择: {qa.get('answer', '')}",
                    "question": qa.get("question", ""),
                    "failure_rate": 0.7
                })
        
        # 推断目标人群
        target_audience = {
            "age_range": "25-40",
            "gender": "不限",
            "occupation": "上班族",
            "education": "大学本科以上",
            "income_level": "中等以上"
        }
        
        # 识别陷阱题目
        trap_questions = []
        question_freq = {}
        for q in all_questions:
            question_freq[q] = question_freq.get(q, 0) + 1
        
        for question, freq in question_freq.items():
            if freq > 1:
                trap_questions.append({
                    "question": question,
                    "trap_type": "重复验证题",
                    "frequency": freq
                })
        
        return QuestionnaireIntelligence(
            target_audience=target_audience,
            questionnaire_theme="消费习惯与偏好调研",
            trap_questions=trap_questions,
            success_patterns=success_patterns[:5],  # 取前5个
            failure_patterns=failure_patterns[:3],  # 取前3个
            recommended_strategies=[
                "选择中等收入相关选项",
                "避免极端年龄选择",
                "保持职业与教育背景一致性",
                "选择主流消费习惯",
                "避免过于特殊的选择"
            ],
            confidence_score=0.8
        )
    
    def _format_experiences_for_analysis(self, experiences: List[ScoutExperience]) -> str:
        """格式化经验数据用于分析"""
        if not experiences:
            return "无数据"
        
        formatted = []
        for exp in experiences:
            formatted.append(f"""
敢死队员: {exp.scout_name}
页面: 第{exp.page_number}页
结果: {'成功' if exp.success else '失败'}
问题答案: {exp.questions_answered}
失败原因: {exp.failure_reason or '无'}
""")
        
        return "\n".join(formatted)
    
    def _parse_gemini_analysis(self, analysis_text: str) -> QuestionnaireIntelligence:
        """解析Gemini分析结果"""
        try:
            # 尝试解析JSON格式的分析结果
            # 这里需要更复杂的解析逻辑
            
            # 简化实现，返回基本结构
            return QuestionnaireIntelligence(
                target_audience={
                    "age_range": "25-40",
                    "gender": "不限",
                    "occupation": "上班族",
                    "education": "大学本科"
                },
                questionnaire_theme="消费习惯调研",
                trap_questions=[
                    {"question": "重复验证题", "trap_type": "一致性检查"}
                ],
                success_patterns=[
                    {"pattern": "保守选择", "success_rate": 0.8},
                    {"pattern": "符合身份", "success_rate": 0.9}
                ],
                failure_patterns=[
                    {"pattern": "极端选择", "failure_rate": 0.7}
                ],
                recommended_strategies=[
                    "选择中等收入相关选项",
                    "避免极端年龄选择",
                    "保持职业一致性"
                ],
                confidence_score=0.8
            )
            
        except Exception as e:
            logger.error(f"❌ 解析Gemini分析结果失败: {e}")
            return self._create_mock_analysis([], [])
    
    async def _generate_guidance_rules(
        self, 
        intelligence: QuestionnaireIntelligence, 
        successful_experiences: List[ScoutExperience]
    ) -> List[GuidanceRule]:
        """生成指导规则"""
        rules = []
        
        # 基于成功模式生成规则
        for i, pattern in enumerate(intelligence.success_patterns):
            rule = GuidanceRule(
                rule_id=f"rule_{i+1}",
                question_pattern=pattern.get("pattern", "通用"),
                recommended_answer="基于成功经验的选择",
                reasoning=f"敢死队在此模式下成功率 {pattern.get('success_rate', 0.5):.0%}",
                confidence=pattern.get("success_rate", 0.5),
                success_rate=pattern.get("success_rate", 0.5)
            )
            rules.append(rule)
        
        # 基于推荐策略生成规则
        for i, strategy in enumerate(intelligence.recommended_strategies):
            rule = GuidanceRule(
                rule_id=f"strategy_{i+1}",
                question_pattern="通用策略",
                recommended_answer=strategy,
                reasoning="基于敢死队成功经验总结",
                confidence=intelligence.confidence_score,
                success_rate=intelligence.confidence_score
            )
            rules.append(rule)
        
        # 基于实际成功经验生成具体规则
        question_success_map = {}
        for exp in successful_experiences:
            for qa in exp.questions_answered:
                question = qa.get("question", "")
                answer = qa.get("answer", "")
                if question:
                    if question not in question_success_map:
                        question_success_map[question] = {}
                    if answer not in question_success_map[question]:
                        question_success_map[question][answer] = 0
                    question_success_map[question][answer] += 1
        
        # 为每个问题生成最优答案规则
        for question, answer_counts in question_success_map.items():
            if answer_counts:
                best_answer = max(answer_counts, key=answer_counts.get)
                success_count = answer_counts[best_answer]
                total_count = sum(answer_counts.values())
                success_rate = success_count / total_count
                
                rule = GuidanceRule(
                    rule_id=f"question_rule_{len(rules)+1}",
                    question_pattern=question,
                    recommended_answer=best_answer,
                    reasoning=f"敢死队在此问题上选择{best_answer}的成功率最高",
                    confidence=success_rate,
                    success_rate=success_rate
                )
                rules.append(rule)
        
        return rules
    
    async def _execute_target_phase(
        self, 
        session_id: str, 
        questionnaire_url: str, 
        intelligence: QuestionnaireIntelligence, 
        target_count: int
    ) -> List[Dict]:
        """执行目标阶段 - 大部队智能作答"""
        try:
            # 招募目标人群
            personas = await self._recruit_guided_targets(intelligence, target_count)
            if not personas:
                raise Exception("无法招募目标人群")
            
            logger.info(f"🎯 开始执行大部队任务，共 {len(personas)} 个成员")
            
            # 执行目标任务
            results = []
            for i, persona in enumerate(personas):
                logger.info(f"🚀 执行大部队成员 {i+1}/{len(personas)}: {persona.get('name', '未知')}")
                
                result = await self._execute_target_mission(
                    session_id,
                    questionnaire_url,
                    persona,
                    self.lifecycle_manager,
                    intelligence  # 🔥 传递智能分析结果
                )
                results.append(result)
                
                # 记录执行状态
                status = "✅ 成功" if result.get("status") == "success" else "❌ 失败"
                logger.info(f"   {status}: {persona.get('name', '未知')}")
            
            # 统计结果
            successful_count = sum(1 for r in results if r.get("status") == "success")
            logger.info(f"🎉 大部队任务完成: {successful_count}/{len(results)} 成功")
            
            return results
            
        except Exception as e:
            logger.error(f"目标阶段执行失败: {e}")
            return []
            
    async def _execute_target_mission(
        self,
        session_id: str,
        questionnaire_url: str,
        persona: Dict,
        lifecycle_manager: EnhancedAdsPowerLifecycle,
        intelligence: Optional[QuestionnaireIntelligence] = None
    ) -> Dict:
        """执行目标任务 - 大部队智能作答"""
        try:
            # 生成浏览器配置文件名
            profile_name = f"target_{session_id}_{persona.get('id', uuid.uuid4().hex[:8])}"
            
            # 创建浏览器环境
            browser_env = await lifecycle_manager.create_complete_browser_environment(
                profile_name=profile_name
            )
            
            if not browser_env or browser_env.get("status") != "success":
                raise Exception("创建浏览器环境失败")
            
            # 🔥 核心修复：为大部队生成包含智能指导的提示词
            enhanced_prompt = ""
            if intelligence:
                enhanced_prompt = self._generate_guided_prompt(persona, intelligence, questionnaire_url)
            else:
                enhanced_prompt = self._generate_enhanced_scout_prompt(persona, questionnaire_url)
            
            # 执行智能问卷填写
            experiences = await self._execute_questionnaire_in_adspower(
                browser_env["browser_config"],
                persona,
                questionnaire_url,
                enhanced_prompt
            )
            
            # 🔥 修复：无论成功失败都保持浏览器开启
            # 分析执行结果
            if experiences and len(experiences) > 0:
                experience = experiences[0]  # 取第一个经验

                if not experience.success:
                    logger.info("❌ 大部队智能作答失败，但保持浏览器窗口开启以便调试")
                else:
                    logger.info("✅ 大部队智能作答成功，保持浏览器窗口开启")

                return {
                    "status": "success" if experience.success else "failed",
                    "persona": persona,
                    "experience": experience,
                    "questions_answered": experience.questions_answered,
                    "questions_count": experience.questions_count,
                    "completion_depth": experience.completion_depth,
                    "error_type": experience.error_type,
                    "failure_reason": experience.failure_reason
                }
            else:
                return {
                    "status": "failed",
                    "error": "未获得有效执行结果",
                    "persona": persona
                }
            
        except Exception as e:
            logger.error(f"目标任务执行失败: {e}")
            return {
                "status": "failed",
                "error": str(e),
                "persona": persona
            }
    
    async def _recruit_guided_targets(
        self, 
        intelligence: QuestionnaireIntelligence, 
        target_count: int
    ) -> List[Dict]:
        """基于智能分析招募大部队成员"""
        logger.info(f"👥 基于智能分析招募 {target_count} 个大部队成员...")
        
        target_audience = intelligence.target_audience
        age_range = target_audience.get("age_range", "25-40")
        occupation = target_audience.get("occupation", "上班族")
        
        personas = []
        
        # 70%与成功者相似，30%其他可能成功的
        similar_count = int(target_count * 0.7)
        diverse_count = target_count - similar_count
        
        # 招募相似的数字人
        if self.xiaoshe_client:
            for i in range(similar_count):
                query = f"{age_range}岁，{occupation}，符合目标人群特征"
                persona = await self._query_single_persona(query, f"target_similar_{i+1}")
                personas.append(persona)
            
            # 招募多样化的数字人
            diverse_queries = [
                "有消费能力的年轻人",
                "注重品质的消费者", 
                "理性决策的购买者",
                "追求性价比的用户",
                "品牌忠诚度高的客户"
            ]
            
            for i in range(diverse_count):
                query = diverse_queries[i % len(diverse_queries)]
                persona = await self._query_single_persona(query, f"target_diverse_{i+1}")
                personas.append(persona)
        else:
            # 使用备用数字人
            for i in range(target_count):
                persona = self._create_backup_persona(i)
                persona["target_id"] = f"target_{i+1}"
                personas.append(persona)
        
        logger.info(f"✅ 招募完成：{similar_count}个相似成员，{diverse_count}个多样化成员")
        return personas
    
    async def _query_single_persona(self, query: str, persona_id: str) -> Dict:
        """查询单个数字人"""
        try:
            if self.xiaoshe_client:
                result_personas = await self.xiaoshe_client.query_personas(query, 1)
                if result_personas:
                    persona = result_personas[0]
                    persona["target_id"] = persona_id
                    return persona
        except Exception as e:
            logger.warning(f"⚠️ 查询数字人失败: {e}")
        
        # 返回备用数字人
        backup = self._create_backup_persona(0)
        backup["target_id"] = persona_id
        backup["name"] = f"大部队成员{persona_id}"
        return backup
    
    def _generate_guided_prompt(
        self, 
        persona: Dict, 
        intelligence: QuestionnaireIntelligence, 
        questionnaire_url: str
    ) -> str:
        """生成包含经验指导的提示词"""
        name = persona.get("name", "李四")
        age = persona.get("age", 30)
        gender = persona.get("gender", "男")
        profession = persona.get("profession", "上班族")
        
        # 构建经验指导部分
        guidance_text = "\n【敢死队经验指导】\n"
        for rule in intelligence.guidance_rules:
            guidance_text += f"- {rule.question_pattern}: {rule.recommended_answer} (成功率: {rule.success_rate:.0%})\n"
        
        guidance_text += "\n【成功策略】\n"
        for strategy in intelligence.recommended_strategies:
            guidance_text += f"- {strategy}\n"
        
        guidance_text += "\n【避免陷阱】\n"
        for trap in intelligence.trap_questions:
            guidance_text += f"- 注意: {trap.get('question', '未知陷阱')}\n"
        
        prompt = f"""你现在是{name}，{age}岁，{gender}性，职业是{profession}。

{guidance_text}

【任务说明】
基于以上敢死队探索的宝贵经验，请访问问卷：{questionnaire_url}

【详细要求】
1. 严格按照你的身份({name}, {age}岁, {gender}性, {profession})进行作答
2. 参考上述敢死队的成功经验和策略
3. 避免已知的陷阱题目
4. 选择与成功案例相似的答案模式
5. 保持逻辑一致性，不要自相矛盾
6. 持续作答直到问卷完成

【重要提醒】
- 这是基于 {len(intelligence.guidance_rules)} 条敢死队经验的指导
- 问卷主题: {intelligence.questionnaire_theme}
- 目标人群: {intelligence.target_audience}
- 请充分利用这些经验提高成功率

开始执行任务！"""

        return prompt
    
    def _generate_final_report(
        self, 
        session_id: str, 
        scout_experiences: List[ScoutExperience], 
        intelligence: QuestionnaireIntelligence, 
        target_results: List[Dict]
    ) -> Dict:
        """生成最终报告"""
        
        scout_success_count = sum(1 for exp in scout_experiences if exp.success)
        # 🔧 修复：适配新的结果格式
        target_success_count = sum(1 for result in target_results if result.get('status') == 'success')
        
        total_participants = len(scout_experiences) + len(target_results)
        overall_success_count = scout_success_count + target_success_count
        overall_success_rate = overall_success_count / total_participants if total_participants > 0 else 0
        
        # 计算改进率
        scout_success_rate = scout_success_count / len(scout_experiences) if scout_experiences else 0
        target_success_rate = target_success_count / len(target_results) if target_results else 0
        improvement_rate = target_success_rate - scout_success_rate
        
        report = {
            "session_id": session_id,
            "execution_time": datetime.now().isoformat(),
            "summary": {
                "total_participants": total_participants,
                "total_successful": overall_success_count,
                "overall_success_rate": overall_success_rate,
                "scout_phase": {
                    "total": len(scout_experiences),
                    "successful": scout_success_count,
                    "success_rate": scout_success_rate
                },
                "target_phase": {
                    "total": len(target_results),
                    "successful": target_success_count,
                    "success_rate": target_success_rate
                }
            },
            "intelligence_analysis": {
                "questionnaire_theme": intelligence.questionnaire_theme,
                "target_audience": intelligence.target_audience,
                "confidence_score": intelligence.confidence_score,
                "guidance_rules_count": len(intelligence.guidance_rules),
                "trap_questions_identified": len(intelligence.trap_questions),
                "success_patterns_found": len(intelligence.success_patterns)
            },
            "improvements": {
                "success_rate_improvement": improvement_rate,
                "strategy_effectiveness": "高" if improvement_rate > 0.2 else "中" if improvement_rate > 0.1 else "低",
                "guidance_rules_applied": len(intelligence.guidance_rules),
                "ai_analysis_confidence": intelligence.confidence_score
            },
            "recommendations": [
                f"问卷主要面向{intelligence.target_audience.get('age_range', '未知')}岁的{intelligence.target_audience.get('occupation', '用户')}",
                f"识别到{len(intelligence.trap_questions)}个陷阱题目，需要特别注意",
                f"生成了{len(intelligence.guidance_rules)}条指导规则，成功率提升{improvement_rate:.1%}",
                f"建议继续使用智能三阶段策略，AI分析可信度达{intelligence.confidence_score:.0%}"
            ]
        }
        
        return report

    def _extract_screenshot_from_result(self, adspower_result: Dict) -> str:
        """从AdsPower结果中提取截图"""
        try:
            if "page_data" in adspower_result:
                page_data = adspower_result["page_data"]
                if isinstance(page_data, dict):
                    return page_data.get("page_screenshot", "")
            return self._generate_mock_screenshot()
        except Exception:
            return self._generate_mock_screenshot()
    
    def _extract_content_from_result(self, adspower_result: Dict) -> str:
        """从AdsPower结果中提取页面内容"""
        try:
            if "page_data" in adspower_result:
                page_data = adspower_result["page_data"]
                if isinstance(page_data, dict):
                    return page_data.get("page_html", "")
            
            # 尝试从其他字段提取
            if "result" in adspower_result:
                result_data = adspower_result["result"]
                if isinstance(result_data, dict):
                    return str(result_data)
            
            return f"AdsPower执行结果: {adspower_result.get('success', False)}"
        except Exception:
            return "内容提取失败"
    
    def _extract_questions_from_result(self, adspower_result: Dict) -> List[Dict]:
        """从AdsPower结果中提取答题信息"""
        try:
            if "page_data" in adspower_result:
                page_data = adspower_result["page_data"]
                if isinstance(page_data, dict):
                    return page_data.get("answered_questions", [])
            
            # 如果没有具体的答题信息，返回基础信息
            return [{
                "question": "AdsPower执行状态",
                "answer": "成功" if adspower_result.get("success") else "失败",
                "reasoning": "基于AdsPower执行结果推断"
            }]
        except Exception:
            return []

    async def _display_technical_errors_in_browser(self, code_server_errors: List[ScoutExperience]):
        """显示技术错误信息，主要在控制台输出详细调试信息"""
        try:
            logger.error(f"🚨 发现 {len(code_server_errors)} 个技术错误，需要调试:")
            
            # 在控制台输出详细错误信息
            for i, exp in enumerate(code_server_errors, 1):
                logger.error(f"🔧 错误 #{i}: {exp.scout_name}")
                logger.error(f"   错误类型: {exp.error_type}")
                logger.error(f"   错误详情: {exp.technical_error_details}")
                logger.error(f"   时间戳: {exp.timestamp}")
                logger.error(f"   答题数量: {exp.questions_count}")
                logger.error(f"   失败原因: {exp.failure_reason}")
                logger.error(f"   浏览器显示: {exp.browser_error_displayed}")
                logger.error("-" * 50)
            
            # 汇总错误信息用于后续处理
            error_summary = {
                "total_errors": len(code_server_errors),
                "error_types": list(set([exp.error_type for exp in code_server_errors])),
                "affected_scouts": [exp.scout_name for exp in code_server_errors],
                "detailed_errors": [
                    {
                        "scout": exp.scout_name,
                        "type": exp.error_type,
                        "details": exp.technical_error_details,
                        "timestamp": exp.timestamp
                    }
                    for exp in code_server_errors
                ]
            }
            
            logger.error(f"🚨 技术错误汇总:")
            logger.error(f"   总错误数: {error_summary['total_errors']}")
            logger.error(f"   错误类型: {', '.join(error_summary['error_types'])}")
            logger.error(f"   受影响的敢死队: {', '.join(error_summary['affected_scouts'])}")
            
            # 建议调试措施
            if "code_error" in error_summary['error_types']:
                logger.error(f"🔧 建议: 检查代码逻辑、模块导入、变量定义")
            if "api_error" in error_summary['error_types']:
                logger.error(f"🌐 建议: 检查API密钥、配额、网络连接")
            if "server_error" in error_summary['error_types']:
                logger.error(f"🖥️ 建议: 检查服务器状态、端口配置、防火墙设置")
                
        except Exception as e:
            logger.error(f"❌ 显示技术错误信息失败: {e}")


    async def _execute_gemini_screenshot_analysis(
        self, 
        session_id: str, 
        questionnaire_url: str, 
        successful_experiences: List[ScoutExperience]
    ) -> str:
        """
        执行Gemini截图分析，生成大部队作答经验指导
        """
        try:
            if not ADSPOWER_WEBUI_AVAILABLE:
                logger.warning("⚠️ AdsPowerWebUI不可用，跳过Gemini截图分析")
                return ""
            
            from adspower_browser_use_integration import GeminiScreenshotAnalyzer
            gemini_analyzer = GeminiScreenshotAnalyzer(self.gemini_api_key)
            
            best_experience = successful_experiences[0] if successful_experiences else None
            if not best_experience or not best_experience.page_screenshot:
                logger.warning("⚠️ 没有可用的成功截图，跳过Gemini分析")
                return ""
            
            logger.info(f"🖼️ 分析最成功敢死队 {best_experience.scout_name} 的截图")
            
            digital_human_info = {
                "name": best_experience.scout_name,
                "gender": "未知",
                "age": "未知", 
                "profession": "未知",
                "income": "未知"
            }
            
            optimized_screenshot, size_kb, saved_filepath = await gemini_analyzer.optimize_screenshot_for_gemini(
                best_experience.page_screenshot, best_experience.scout_name, session_id
            )
            
            logger.info(f"📸 截图已优化: {size_kb}KB, 保存至: {saved_filepath}")
            
            analysis_result = await gemini_analyzer.analyze_questionnaire_screenshot(
                optimized_screenshot, digital_human_info, questionnaire_url
            )
            
            guidance_text = analysis_result.get("guidance_for_troops", "")
            
            if guidance_text:
                logger.info(f"✅ Gemini截图分析成功，生成经验指导")
                
                if not hasattr(self, 'session_gemini_analysis'):
                    self.session_gemini_analysis = {}
                    
                self.session_gemini_analysis[session_id] = {
                    "analysis_result": analysis_result,
                    "best_scout": best_experience.scout_name,
                    "screenshot_filepath": saved_filepath,
                    "analysis_time": datetime.now().isoformat(),
                    "guidance_preview": guidance_text[:200] + "..." if len(guidance_text) > 200 else guidance_text
                }
                
                return guidance_text
            else:
                logger.warning("⚠️ Gemini分析未生成有效的经验指导")
                return ""
                
        except Exception as e:
            logger.error(f"❌ Gemini截图分析失败: {e}")
            return ""
    
    def get_session_gemini_analysis(self, session_id: str) -> Optional[Dict]:
        """获取会话的Gemini分析结果"""
        if hasattr(self, 'session_gemini_analysis'):
            return self.session_gemini_analysis.get(session_id)
        return None

    def _generate_mock_screenshot(self, page_content: Optional[str] = "") -> str:
        """生成模拟截图（用于测试和错误处理）"""
        try:
            # 创建一个空白图像
            width, height = 800, 600
            image = Image.new('RGB', (width, height), color='white')
            
            # 如果有页面内容，添加到图像中
            if page_content:
                draw = ImageDraw.Draw(image)
                draw.text((10, 10), page_content[:200], fill='black')
            
            # 转换为base64
            buffered = io.BytesIO()
            image.save(buffered, format="PNG")
            img_str = base64.b64encode(buffered.getvalue()).decode()
            
            return img_str
            
        except Exception as e:
            logger.error(f"❌ 生成模拟截图失败: {e}")
            return ""

# 导出核心类供app.py使用
__all__ = ['ThreeStageIntelligentCore'] 