#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
🔧 智能引擎集成补丁 - 解决系统架构问题
修复CustomController缺失的方法和组件
"""

import logging
import asyncio
import types
from typing import Dict, Any
from browser_use.browser.context import BrowserContext
from browser_use.agent.views import ActionResult

logger = logging.getLogger(__name__)

class IntelligentEngineIntegrationPatch:
    """智能引擎集成补丁"""
    
    def __init__(self):
        self.patch_applied = False
    
    def apply_patch(self, controller):
        """应用补丁到CustomController"""
        try:
            logger.info("🔧 开始应用智能引擎集成补丁...")
            
            # 修复1: 添加缺失的register_intelligent_dropdown_engine方法
            self._add_dropdown_engine_method(controller)
            
            # 修复2: 增强错误处理
            self._enhance_error_handling(controller)
            
            # 修复3: 添加DOM操作保护
            self._add_dom_protection(controller)
            
            # 修复4: 确保数字人信息正确设置
            self._ensure_digital_human_info(controller)
            
            self.patch_applied = True
            logger.info("✅ 智能引擎集成补丁应用成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 智能引擎集成补丁应用失败: {e}")
            return False
    
    def _add_dropdown_engine_method(self, controller):
        """添加缺失的register_intelligent_dropdown_engine方法"""
        
        def register_intelligent_dropdown_engine(self):
            """🎯 注册智能下拉框引擎"""
            try:
                logger.info("🎯 注册智能下拉框引擎...")
                
                # 初始化智能下拉框引擎
                if not hasattr(self, 'dropdown_engine'):
                    try:
                        # 尝试导入外部引擎
                        from intelligent_dropdown_engine import IntelligentDropdownEngine
                        self.dropdown_engine = IntelligentDropdownEngine()
                        if hasattr(self, 'digital_human_info') and self.digital_human_info:
                            self.dropdown_engine.set_digital_human_info(self.digital_human_info)
                        logger.info("✅ 外部智能下拉框引擎初始化成功")
                    except ImportError:
                        # 使用内置引擎
                        self.dropdown_engine = self._create_builtin_dropdown_engine()
                        logger.info("✅ 内置智能下拉框引擎初始化成功")
                
                # 注册下拉框处理动作
                @self.registry.action(
                    'Intelligent dropdown selection with digital human matching',
                )
                async def intelligent_dropdown_selection(
                    index: int, 
                    browser: BrowserContext,
                    target_value: str = ""
                ) -> ActionResult:
                    """🎯 智能下拉框选择"""
                    try:
                        logger.info(f"🎯 启动智能下拉框选择: index={index}, target='{target_value}'")
                        
                        # 获取数字人信息
                        digital_human_info = self._get_digital_human_info_safely()
                        
                        # 获取页面和元素
                        page = await browser.get_current_page()
                        selector_map = await browser.get_selector_map()
                        
                        if index >= len(selector_map):
                            return ActionResult(error=f"Element index {index} out of range")
                        
                        target_element = selector_map[index]
                        
                        # 智能选项匹配
                        if hasattr(self.dropdown_engine, 'intelligent_option_matching'):
                            matching_result = self.dropdown_engine.intelligent_option_matching(target_value)
                            if matching_result.get('matches'):
                                for match_value in matching_result['matches']:
                                    try:
                                        # 尝试选择匹配的选项
                                        await page.select_option(f"xpath={target_element.xpath}", value=match_value)
                                        logger.info(f"✅ 智能下拉选择成功: {match_value}")
                                        return ActionResult(
                                            extracted_content=f"Successfully selected: {match_value}",
                                            include_in_memory=True
                                        )
                                    except Exception as select_error:
                                        logger.debug(f"⚠️ 选择 {match_value} 失败: {select_error}")
                                        continue
                        
                        # 回退到基础选择
                        if target_value:
                            await page.select_option(f"xpath={target_element.xpath}", value=target_value)
                            return ActionResult(
                                extracted_content=f"Selected: {target_value}",
                                include_in_memory=True
                            )
                        else:
                            return ActionResult(error="No target value provided for dropdown selection")
                            
                    except Exception as e:
                        logger.error(f"❌ 智能下拉框选择失败: {e}")
                        return ActionResult(error=f"Dropdown selection failed: {str(e)}")
                
                logger.info("✅ 智能下拉框引擎注册完成")
                
            except Exception as e:
                logger.error(f"❌ 智能下拉框引擎注册失败: {e}")
        
        def _create_builtin_dropdown_engine(self):
            """创建内置下拉框引擎"""
            class BuiltinDropdownEngine:
                def __init__(self):
                    self.digital_human_info = {}
                
                def set_digital_human_info(self, info):
                    self.digital_human_info = info
                
                def intelligent_option_matching(self, target_value):
                    """简单的选项匹配"""
                    if not target_value:
                        return {'matches': []}
                    
                    # 职业匹配
                    if '会计' in target_value or '财务' in target_value:
                        return {
                            'matches': ['会计/财务', '财务会计', '会计师', '财务经理'],
                            'type': 'profession'
                        }
                    
                    # 国家匹配
                    if any(keyword in target_value for keyword in ['中国', 'China', '国家']):
                        return {
                            'matches': ['中国', '中国大陆', '中华人民共和国', 'China'],
                            'type': 'country'
                        }
                    
                    return {'matches': [target_value]}
            
            return BuiltinDropdownEngine()
        
        # 绑定方法到controller
        controller.register_intelligent_dropdown_engine = types.MethodType(register_intelligent_dropdown_engine, controller)
        controller._create_builtin_dropdown_engine = types.MethodType(_create_builtin_dropdown_engine, controller)
        
        logger.info("✅ 下拉框引擎方法已添加")
    
    def _enhance_error_handling(self, controller):
        """增强错误处理"""
        
        def safe_method_call(self, method_name, *args, **kwargs):
            """安全方法调用包装器"""
            try:
                if hasattr(self, method_name):
                    method = getattr(self, method_name)
                    return method(*args, **kwargs)
                else:
                    logger.warning(f"⚠️ 方法 {method_name} 不存在，跳过调用")
                    return None
            except Exception as e:
                logger.error(f"❌ 方法 {method_name} 调用失败: {e}")
                return None
        
        controller.safe_method_call = types.MethodType(safe_method_call, controller)
        logger.info("✅ 错误处理增强已添加")
    
    def _add_dom_protection(self, controller):
        """添加DOM操作保护"""
        
        async def safe_dom_operation(self, operation_func, max_retries=3, delay=1.0):
            """安全DOM操作包装器"""
            for attempt in range(max_retries):
                try:
                    return await operation_func()
                except Exception as e:
                    if "Execution context was destroyed" in str(e):
                        logger.warning(f"⚠️ DOM操作失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                        if attempt < max_retries - 1:
                            await asyncio.sleep(delay)
                            continue
                    raise e
            return None
        
        controller.safe_dom_operation = types.MethodType(safe_dom_operation, controller)
        logger.info("✅ DOM保护已添加")
    
    def _ensure_digital_human_info(self, controller):
        """确保数字人信息正确设置"""
        
        def _get_digital_human_info_safely(self):
            """安全获取数字人信息"""
            if hasattr(self, 'digital_human_info') and self.digital_human_info:
                return self.digital_human_info
            elif hasattr(self, '_digital_human_info') and self._digital_human_info:
                return self._digital_human_info
            else:
                logger.warning("⚠️ 数字人信息未设置，使用默认信息")
                return {
                    "name": "默认用户",
                    "age": 30,
                    "gender": "女",
                    "profession": "白领",
                    "location": "北京",
                    "residence": "中国"
                }
        
        # 如果方法不存在，添加它
        if not hasattr(controller, '_get_digital_human_info_safely'):
            controller._get_digital_human_info_safely = types.MethodType(_get_digital_human_info_safely, controller)
        
        logger.info("✅ 数字人信息保护已添加")

# 全局补丁实例
intelligent_engine_patch = IntelligentEngineIntegrationPatch()

def apply_intelligent_engine_patch(controller):
    """应用智能引擎补丁的便捷函数"""
    return intelligent_engine_patch.apply_patch(controller) 