#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试BrowserConfig问题
"""

import traceback

def test_browser_config():
    """测试BrowserConfig的问题"""
    try:
        print("🔧 测试BrowserConfig...")
        
        # 导入browser-use组件
        from browser_use.browser.browser import <PERSON>rowser, BrowserConfig
        from browser_use.browser.context import BrowserContextConfig
        
        print("✅ 成功导入browser-use组件")
        
        # 测试创建BrowserConfig
        debug_port = "61857"
        
        config_dict = {
            "headless": False,
            "disable_security": True,
            "browser_binary_path": None,
            "cdp_url": f"http://127.0.0.1:{debug_port}",
            "extra_chromium_args": [
                "--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36",
                "--disable-mobile-emulation", 
                "--disable-touch-events",
                "--no-sandbox",
            ],
            "new_context_config": {
                "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36",
                "is_mobile": False,
                "has_touch": False,
                "viewport_width": 1280,
                "viewport_height": 800,
                "device_scale_factor": 1.0,
                "locale": "zh-CN",
                "timezone_id": "Asia/Shanghai"
            }
        }
        
        print("🚀 创建BrowserConfig...")
        browser_config = BrowserConfig(config_dict)
        print("✅ BrowserConfig创建成功")
        
        print("🔍 测试BrowserConfig属性访问...")
        print(f"   headless: {browser_config.headless}")
        print(f"   可用方法: {[m for m in dir(browser_config) if not m.startswith('_')]}")
        
        # 测试是否有get方法
        if hasattr(browser_config, 'get'):
            print("✅ BrowserConfig有get方法")
        else:
            print("❌ BrowserConfig没有get方法")
        
        # 测试创建Browser
        print("🚀 创建Browser...")
        browser = Browser(config=browser_config)
        print("✅ Browser创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print("完整错误堆栈:")
        traceback.print_exc()
        return False

def test_adspower_integration():
    """测试AdsPower集成"""
    try:
        print("\n🔧 测试AdsPower集成...")
        
        # 导入集成模块
        from adspower_browser_use_integration import execute_questionnaire_task_with_data_extraction
        
        print("✅ 成功导入集成模块")
        
        # 测试参数
        debug_port = "127.0.0.1:61857"
        persona_info = {
            "name": "张三",
            "age": 25,
            "gender": "男",
            "profession": "程序员"
        }
        questionnaire_url = "https://wjx.cn/vm/w4e8hc9.aspx"
        
        print("🚀 调用智能作答函数...")
        
        # 这里会触发错误
        result = execute_questionnaire_task_with_data_extraction(
            debug_port=debug_port,
            persona_info=persona_info,
            questionnaire_url=questionnaire_url
        )
        
        print(f"✅ 智能作答完成: {result}")
        return True
        
    except Exception as e:
        print(f"❌ AdsPower集成测试失败: {e}")
        print("完整错误堆栈:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 BrowserConfig调试测试")
    print("=" * 50)
    
    # 测试1：基础BrowserConfig
    success1 = test_browser_config()
    
    # 测试2：AdsPower集成
    success2 = test_adspower_integration()
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果:")
    print(f"   基础BrowserConfig: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"   AdsPower集成: {'✅ 成功' if success2 else '❌ 失败'}")
