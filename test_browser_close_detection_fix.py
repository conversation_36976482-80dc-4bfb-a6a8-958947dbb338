#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
浏览器关闭检测和强制清理功能测试
测试AdsPower资源是否能在浏览器手动关闭后正确释放
"""

import asyncio
import logging
import time
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from adspower_browser_use_integration import AdsPowerWebUIIntegration, AdsPowerResourceManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('browser_close_test.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

class BrowserCloseDetectionTest:
    """浏览器关闭检测测试类"""
    
    def __init__(self):
        self.integration = AdsPowerWebUIIntegration()
        self.resource_manager = AdsPowerResourceManager(logger)
        self.test_profile_id = None
        self.test_persona_name = "测试数字人_浏览器关闭检测"
        
    async def run_comprehensive_test(self):
        """运行完整的浏览器关闭检测测试"""
        logger.info("🚀 开始浏览器关闭检测和强制清理测试")
        logger.info("=" * 60)
        
        try:
            # 步骤1：创建测试配置文件
            logger.info("📍 步骤1：创建测试AdsPower配置文件...")
            profile_result = await self._create_test_profile()
            
            if not profile_result["success"]:
                logger.error("❌ 测试配置文件创建失败")
                return False
            
            self.test_profile_id = profile_result["profile_id"]
            logger.info(f"✅ 测试配置文件创建成功: {self.test_profile_id}")
            
            # 步骤2：启动浏览器
            logger.info("📍 步骤2：启动AdsPower浏览器...")
            browser_result = await self._start_test_browser()
            
            if not browser_result["success"]:
                logger.error("❌ 浏览器启动失败")
                return False
            
            logger.info("✅ 浏览器启动成功")
            
            # 步骤3：验证浏览器在AdsPower列表中
            logger.info("📍 步骤3：验证浏览器在AdsPower列表中...")
            before_close = await self._check_profile_in_list()
            logger.info(f"浏览器关闭前配置文件存在: {before_close}")
            
            # 步骤4：模拟浏览器手动关闭检测
            logger.info("📍 步骤4：模拟检测浏览器手动关闭...")
            logger.info("⏳ 请在5秒内手动关闭AdsPower浏览器窗口...")
            
            # 等待用户手动关闭浏览器
            await asyncio.sleep(5)
            
            # 步骤5：检测浏览器状态
            logger.info("📍 步骤5：检测浏览器状态...")
            browser_status = await self._check_browser_status()
            logger.info(f"检测到浏览器状态: {browser_status}")
            
            # 步骤6：如果检测到关闭，执行强制清理
            if browser_status == "Inactive":
                logger.info("📍 步骤6：执行强制资源清理...")
                cleanup_result = await self._test_force_cleanup()
                
                # 步骤7：验证清理结果
                logger.info("📍 步骤7：验证清理结果...")
                after_cleanup = await self._check_profile_in_list()
                logger.info(f"清理后配置文件存在: {after_cleanup}")
                
                # 评估测试结果
                success = (
                    before_close and  # 清理前存在
                    not after_cleanup and  # 清理后不存在
                    cleanup_result.get("full_cleanup", False)  # 清理成功
                )
                
                logger.info("📊 测试结果总结:")
                logger.info(f"   创建配置文件: ✅")
                logger.info(f"   启动浏览器: ✅")
                logger.info(f"   清理前存在: {'✅' if before_close else '❌'}")
                logger.info(f"   检测到关闭: ✅")
                logger.info(f"   强制清理执行: {'✅' if cleanup_result.get('cleanup_performed') else '❌'}")
                logger.info(f"   配置文件删除: {'✅' if cleanup_result.get('profile_deleted') else '❌'}")
                logger.info(f"   清理后消失: {'✅' if not after_cleanup else '❌'}")
                logger.info(f"   整体成功: {'✅' if success else '❌'}")
                
                return success
            else:
                logger.warning("⚠️ 未检测到浏览器关闭，可能需要手动关闭浏览器")
                logger.info("🔄 清理测试配置文件...")
                await self._cleanup_test_profile()
                return False
                
        except Exception as e:
            logger.error(f"❌ 测试过程出现异常: {e}")
            return False
        
        finally:
            # 确保清理测试资源
            if self.test_profile_id:
                try:
                    await self._cleanup_test_profile()
                except Exception as cleanup_error:
                    logger.warning(f"⚠️ 清理测试资源失败: {cleanup_error}")
    
    async def _create_test_profile(self):
        """创建测试配置文件"""
        try:
            # 使用集成类的方法创建配置文件
            digital_human_info = {
                "name": self.test_persona_name,
                "age": 25,
                "profession": "软件测试工程师",
                "location": "北京",
                "nationality": "中国"
            }
            
            # 创建浏览器环境
            result = await self.integration.create_browser_environment(
                persona_id=999999,  # 测试用ID
                persona_name=self.test_persona_name,
                digital_human_info=digital_human_info,
                proxy_enabled=False
            )
            
            return {
                "success": result.get("success", False),
                "profile_id": result.get("browser_info", {}).get("profile_id"),
                "details": result
            }
            
        except Exception as e:
            logger.error(f"❌ 创建测试配置文件失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def _start_test_browser(self):
        """启动测试浏览器"""
        try:
            if not self.test_profile_id:
                return {"success": False, "error": "配置文件ID不存在"}
            
            # 使用AdsPower API启动浏览器
            import aiohttp
            
            url = "http://local.adspower.net:50325/api/v1/browser/start"
            data = {"user_id": self.test_profile_id}
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get("code") == 0:
                            return {"success": True, "data": result.get("data")}
                        else:
                            return {"success": False, "error": result.get("msg")}
                    else:
                        return {"success": False, "error": f"HTTP {response.status}"}
                        
        except Exception as e:
            logger.error(f"❌ 启动测试浏览器失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def _check_browser_status(self):
        """检查浏览器状态"""
        try:
            import aiohttp
            
            url = "http://local.adspower.net:50325/api/v1/browser/active"
            params = {"user_id": self.test_profile_id}
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get("code") == 0:
                            return result.get("data", {}).get("status", "Unknown")
                    return "Unknown"
                    
        except Exception as e:
            logger.error(f"❌ 检查浏览器状态失败: {e}")
            return "Error"
    
    async def _test_force_cleanup(self):
        """测试强制清理功能"""
        try:
            logger.info("🔥 开始测试强制清理功能...")
            
            # 调用强制清理方法
            cleanup_result = await self.resource_manager.force_cleanup_browser_closed_resources(
                self.test_profile_id, self.test_persona_name
            )
            
            logger.info(f"强制清理结果: {cleanup_result}")
            return cleanup_result
            
        except Exception as e:
            logger.error(f"❌ 强制清理测试失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def _check_profile_in_list(self):
        """检查配置文件是否在AdsPower列表中"""
        try:
            import aiohttp
            
            url = "http://local.adspower.net:50325/api/v1/user/list"
            params = {"page": 1, "page_size": 100}
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get("code") == 0:
                            profiles = result.get("data", {}).get("list", [])
                            for profile in profiles:
                                if profile.get("user_id") == self.test_profile_id:
                                    return True
                            return False
                    return False
                    
        except Exception as e:
            logger.error(f"❌ 检查配置文件列表失败: {e}")
            return False
    
    async def _cleanup_test_profile(self):
        """清理测试配置文件"""
        try:
            if not self.test_profile_id:
                return
            
            logger.info(f"🧹 清理测试配置文件: {self.test_profile_id}")
            
            # 强制清理
            cleanup_result = await self.resource_manager.force_cleanup_browser_closed_resources(
                self.test_profile_id, self.test_persona_name
            )
            
            if cleanup_result.get("full_cleanup"):
                logger.info("✅ 测试配置文件清理成功")
            else:
                logger.warning("⚠️ 测试配置文件清理部分失败")
                
        except Exception as e:
            logger.error(f"❌ 清理测试配置文件失败: {e}")

async def main():
    """主测试函数"""
    print("🧪 AdsPower浏览器关闭检测和强制清理测试")
    print("=" * 60)
    print("📋 测试流程:")
    print("1. 创建测试AdsPower配置文件")
    print("2. 启动浏览器")
    print("3. 验证配置文件在AdsPower列表中")
    print("4. 等待用户手动关闭浏览器")
    print("5. 检测浏览器关闭状态")
    print("6. 执行强制资源清理")
    print("7. 验证配置文件从AdsPower列表中消失")
    print("=" * 60)
    
    # 确认开始测试
    input("按回车键开始测试...")
    
    test = BrowserCloseDetectionTest()
    success = await test.run_comprehensive_test()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试通过！浏览器关闭检测和强制清理功能正常工作")
        print("✅ AdsPower资源可以在浏览器手动关闭后正确释放")
    else:
        print("❌ 测试失败！需要检查浏览器关闭检测和强制清理功能")
        print("⚠️ 建议检查AdsPower API连接和权限设置")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main()) 