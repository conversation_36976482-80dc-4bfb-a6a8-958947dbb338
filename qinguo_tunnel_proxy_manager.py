#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
青果隧道代理管理器
使用青果隧道代理服务，为每个浏览器分配独立的代理通道
"""

import requests
import time
import random
import logging
from typing import Dict, List, Optional
from src.config.proxy_config import (
    get_proxy_config,
    format_auth_string,
    get_proxy_url,
    get_api_params,
    validate_proxy_config
)

logger = logging.getLogger(__name__)

class QinguoTunnelProxyManager:
    """青果隧道代理管理器"""
    
    def __init__(self):
        # 从配置系统获取青果代理配置
        self.config = get_proxy_config()
        
        # 验证配置
        error = validate_proxy_config()
        if error:
            raise ValueError(f"代理配置错误: {error}")
        
        # 代理管理
        self.allocated_proxies = []  # 已分配的代理通道
        self.session_counter = 0     # 会话计数器
        
    def generate_session_id(self) -> str:
        """生成唯一的会话ID"""
        self.session_counter += 1
        timestamp = int(time.time())
        return f"session_{timestamp}_{self.session_counter}_{random.randint(1000, 9999)}"
    
    def create_tunnel_proxy_config(self, session_id: str) -> Dict:
        """创建隧道代理配置"""
        # 获取API参数
        params = get_api_params("allocate")
        params["session"] = session_id
        
        try:
            # 调用API创建隧道
            url = f"{self.config['base_url']}/allocate"
            response = requests.get(url, params=params, timeout=self.config["timeout"])
            response.raise_for_status()
            
            # 解析响应
            if response.text.startswith("ERROR"):
                raise Exception(f"API错误: {response.text}")
            
            # 创建代理配置
            return {
                "proxy_type": "http",
                "proxy_host": self.config["tunnel_host"],
                "proxy_port": self.config["tunnel_port"],
                "proxy_user": format_auth_string("tunnel"),
                "proxy_password": self.config["auth_pwd"],
                "session_id": session_id,
                "full_address": f"{self.config['tunnel_host']}:{self.config['tunnel_port']}"
            }
            
        except Exception as e:
            logger.error(f"创建隧道代理失败: {e}")
            return {}
    
    def allocate_tunnel_proxy_for_browser(self, browser_name: str) -> Dict:
        """为浏览器分配隧道代理"""
        try:
            # 生成唯一会话ID
            session_id = self.generate_session_id()
            
            # 创建隧道代理配置
            proxy_config = self.create_tunnel_proxy_config(session_id)
            if not proxy_config:
                raise Exception("无法创建隧道代理配置")
            
            proxy_config.update({
                "browser_name": browser_name,
                "allocated_at": time.time(),
                "type": "tunnel"
            })
            
            # 测试代理连接
            if not self.test_tunnel_proxy(proxy_config):
                raise Exception("代理连接测试失败")
            
            self.allocated_proxies.append(proxy_config)
            logger.info(f"为浏览器 {browser_name} 分配隧道代理: {session_id}")
            return proxy_config
            
        except Exception as e:
            logger.error(f"分配隧道代理失败: {e}")
            return {}
    
    def generate_adspower_proxy_config(self, proxy: Dict) -> Dict:
        """生成AdsPower代理配置"""
        if not proxy or proxy.get("type") != "tunnel":
            return {
                "proxy_soft": "no_proxy",
                "proxy_type": "noproxy"
            }
        
        return {
            "proxy_soft": "other",
            "proxy_type": "http",
            "proxy_host": proxy["proxy_host"],
            "proxy_port": proxy["proxy_port"],
            "proxy_user": proxy["proxy_user"],
            "proxy_password": proxy["proxy_password"],
            
            # 附加配置
            "proxy_check": True,  # 启用代理检查
            "proxy_connection_timeout": 10,  # 连接超时时间
            "proxy_read_timeout": 30,  # 读取超时时间
            "proxy_retry_count": 3,  # 重试次数
            
            # DNS配置
            "proxy_dns_type": "default",  # 使用系统DNS
            
            # 高级选项
            "proxy_force_local_dns": False,  # 不强制本地DNS
            "proxy_force_http_tunnel": True,  # 强制HTTP隧道
            "proxy_disable_cache": True  # 禁用缓存
        }
    
    def test_tunnel_proxy(self, proxy: Dict) -> bool:
        """测试隧道代理是否可用"""
        try:
            if not proxy or proxy.get("type") != "tunnel":
                return False
            
            # 获取代理URL
            proxy_url = get_proxy_url("http", "tunnel")
            
            proxies = {
                "http": proxy_url,
                "https": proxy_url
            }
            
            # 添加重试机制
            for attempt in range(self.config["retry_attempts"]):
                try:
                    # 测试请求
                    response = requests.get(
                        "http://httpbin.org/ip",
                        proxies=proxies,
                        timeout=self.config["timeout"]
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        current_ip = result.get('origin', 'unknown')
                        logger.info(f"隧道代理测试成功: {proxy['session_id']} -> IP: {current_ip}")
                        proxy["current_ip"] = current_ip
                        return True
                    
                except Exception as e:
                    if attempt == self.config["retry_attempts"] - 1:
                        logger.error(f"隧道代理测试失败 {proxy.get('session_id', 'unknown')}: {e}")
                        return False
                    time.sleep(self.config["retry_delay"])
            
            return False
            
        except Exception as e:
            logger.error(f"隧道代理测试失败 {proxy.get('session_id', 'unknown')}: {e}")
            return False
    
    def release_proxy(self, browser_name: str) -> bool:
        """释放浏览器的代理"""
        try:
            for i, proxy in enumerate(self.allocated_proxies):
                if proxy.get("browser_name") == browser_name:
                    released_proxy = self.allocated_proxies.pop(i)
                    logger.info(f"释放浏览器 {browser_name} 的隧道代理: {released_proxy['session_id']}")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"释放代理失败: {e}")
            return False
    
    def get_proxy_status(self) -> Dict:
        """获取代理状态"""
        return {
            "tunnel_server": f"{self.config['tunnel_host']}:{self.config['tunnel_port']}",
            "allocated_count": len(self.allocated_proxies),
            "allocated_proxies": [
                {
                    "browser": p["browser_name"],
                    "session_id": p["session_id"],
                    "current_ip": p.get("current_ip", "未检测"),
                    "allocated_time": time.strftime("%H:%M:%S", time.localtime(p["allocated_at"]))
                }
                for p in self.allocated_proxies
            ]
        }
    
    async def cleanup_all_proxies(self):
        """清理所有代理"""
        try:
            logger.info("清理所有隧道代理资源...")
            
            # 释放所有代理
            for proxy in self.allocated_proxies:
                if proxy.get("browser_name"):
                    self.release_proxy(proxy["browser_name"])
            
            # 清理列表
            self.allocated_proxies.clear()
            
            logger.info("✅ 隧道代理资源清理完成")
            
        except Exception as e:
            logger.error(f"❌ 隧道代理资源清理失败: {e}")

# 测试函数
async def test_qinguo_tunnel_proxy():
    """测试青果隧道代理功能"""
    print("🧪 测试青果隧道代理功能...")
    
    manager = QinguoTunnelProxyManager()
    
    try:
        # 1. 为测试浏览器分配隧道代理
        proxy1 = manager.allocate_tunnel_proxy_for_browser("测试浏览器1")
        proxy2 = manager.allocate_tunnel_proxy_for_browser("测试浏览器2")
        
        print(f"✅ 分配隧道代理:")
        print(f"   浏览器1: {proxy1['session_id']}")
        print(f"   浏览器2: {proxy2['session_id']}")
        
        # 2. 测试代理连接
        print(f"\n🔍 测试隧道代理连接...")
        
        if manager.test_tunnel_proxy(proxy1):
            print(f"✅ 浏览器1代理测试成功: IP = {proxy1.get('current_ip')}")
        else:
            print(f"❌ 浏览器1代理测试失败")
        
        if manager.test_tunnel_proxy(proxy2):
            print(f"✅ 浏览器2代理测试成功: IP = {proxy2.get('current_ip')}")
        else:
            print(f"❌ 浏览器2代理测试失败")
        
        # 3. 生成AdsPower配置
        adspower_config1 = manager.generate_adspower_proxy_config(proxy1)
        adspower_config2 = manager.generate_adspower_proxy_config(proxy2)
        
        print(f"\n📋 AdsPower配置:")
        print(f"   浏览器1: {adspower_config1}")
        print(f"   浏览器2: {adspower_config2}")
        
        # 4. 显示状态
        status = manager.get_proxy_status()
        print(f"\n📊 隧道代理状态:")
        print(f"   隧道服务器: {status['tunnel_server']}")
        print(f"   已分配数量: {status['allocated_count']}")
        for proxy_info in status['allocated_proxies']:
            print(f"   - {proxy_info['browser']}: {proxy_info['session_id']} (IP: {proxy_info['current_ip']})")
        
        # 5. 清理资源
        await manager.cleanup_all_proxies()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    import asyncio
    asyncio.run(test_qinguo_tunnel_proxy()) 