#!/usr/bin/env python3
"""
测试增强的拖拽手柄检测功能
专门测试基于视觉特征的拖拽手柄识别
"""

import asyncio
import sys
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('enhanced_drag_detection_test.log')
    ]
)

logger = logging.getLogger(__name__)

async def test_enhanced_drag_detection():
    """测试增强的拖拽手柄检测"""
    try:
        # 导入必要的模块
        from src.controller.custom_controller import CustomController
        from src.browser.custom_browser import CustomBrowser
        from browser_use.browser.browser import BrowserConfig
        from browser_use.browser.context import BrowserContextConfig
        
        logger.info("🧪 开始增强拖拽手柄检测测试...")
        
        # 创建控制器
        controller = CustomController()
        logger.info("✅ CustomController初始化成功")
        
        # 创建浏览器（无头模式用于测试）
        browser = CustomBrowser(
            config=BrowserConfig(
                headless=True,  # 测试时使用无头模式
                new_context_config=BrowserContextConfig(
                    window_width=1920,
                    window_height=1080,
                )
            )
        )
        
        context_config = BrowserContextConfig(
            window_height=1080,
            window_width=1920,
        )
        browser_context = await browser.new_context(config=context_config)
        logger.info("✅ 浏览器上下文创建成功")
        
        # 获取页面
        page = await browser_context.get_current_page()
        
        # 测试HTML内容 - 模拟带有拖拽手柄的页面
        test_html = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>拖拽测试页面</title>
            <style>
                .sortable-item {
                    display: flex;
                    align-items: center;
                    padding: 10px;
                    margin: 5px 0;
                    background: #f5f5f5;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                }
                .drag-handle {
                    cursor: grab;
                    margin-right: 10px;
                    color: #666;
                    font-size: 18px;
                }
                .content {
                    flex: 1;
                }
                .question-title {
                    font-size: 18px;
                    font-weight: bold;
                    margin-bottom: 20px;
                }
            </style>
        </head>
        <body>
            <div class="question-title">你对手机最看重的性能有哪些呢？请对以下选项进行排序。</div>
            
            <div class="sortable-container">
                <!-- 带点点手柄的拖拽项 -->
                <div class="sortable-item">
                    <span class="drag-handle">⋮⋮</span>
                    <div class="content">颜色外观</div>
                </div>
                
                <div class="sortable-item">
                    <span class="drag-handle">⋮⋮</span>
                    <div class="content">游戏流畅度</div>
                </div>
                
                <div class="sortable-item">
                    <span class="drag-handle">⋮⋮</span>
                    <div class="content">摄像头性能</div>
                </div>
                
                <!-- 带横线手柄的拖拽项 -->
                <div class="sortable-item">
                    <span class="drag-handle">≡</span>
                    <div class="content">拍照速度</div>
                </div>
                
                <div class="sortable-item">
                    <span class="drag-handle">☰</span>
                    <div class="content">电池续航</div>
                </div>
                
                <!-- 带CSS类名的拖拽项 -->
                <div class="sortable-item">
                    <div class="drag-handle sortable-handle">⋯</div>
                    <div class="content">系统流畅度</div>
                </div>
                
                <!-- 带data属性的拖拽项 -->
                <div class="sortable-item" data-sortable="true">
                    <div class="drag-handle" data-handle="true">•••</div>
                    <div class="content">存储空间</div>
                </div>
            </div>
            
            <!-- 模拟其他类型的元素 -->
            <button>提交</button>
            <input type="text" placeholder="其他意见">
            <select>
                <option>选择选项</option>
                <option>选项1</option>
            </select>
        </body>
        </html>
        """
        
        # 加载测试页面
        await page.set_content(test_html)
        logger.info("✅ 测试页面加载完成")
        
        # 等待页面渲染
        await asyncio.sleep(1)
        
        # 测试增强拖拽手柄检测
        logger.info("🔍 测试增强的拖拽手柄检测...")
        enhanced_detection = await controller._enhanced_drag_handle_detection(page)
        
        # 输出检测结果
        logger.info("📊 增强检测结果汇总:")
        logger.info(f"   成功: {enhanced_detection.get('success', False)}")
        logger.info(f"   拖拽手柄数量: {enhanced_detection.get('totalHandles', 0)}")
        logger.info(f"   拖拽项数量: {enhanced_detection.get('totalItems', 0)}")
        
        return enhanced_detection.get('success', False)
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生异常: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False
    
    finally:
        # 清理资源
        try:
            if 'browser_context' in locals():
                await browser_context.close()
            if 'browser' in locals():
                await browser.close()
            logger.info("✅ 资源清理完成")
        except:
            pass

async def main():
    """主函数"""
    logger.info("🚀 启动增强拖拽手柄检测测试...")
    
    success = await test_enhanced_drag_detection()
    
    if success:
        logger.info("🎉 所有测试通过!")
        sys.exit(0)
    else:
        logger.error("❌ 测试失败!")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
