"""
数据库配置
集中管理所有数据库相关的配置信息
"""

import os
from typing import Dict, Any

# 数据库配置
DATABASE_CONFIG = {
    # 主数据库配置
    "default": {
        "host": os.getenv("DB_HOST", "**************"),
        "port": int(os.getenv("DB_PORT", "3306")),
        "user": os.getenv("DB_USER", "root"),
        "password": os.getenv("DB_PASSWORD", "123456"),
        "database": os.getenv("DB_NAME", "wenjuan"),
        "charset": os.getenv("DB_CHARSET", "utf8mb4")
    },
    
    # 连接池配置
    "pool": {
        "min_connections": int(os.getenv("DB_POOL_MIN", "1")),
        "max_connections": int(os.getenv("DB_POOL_MAX", "10")),
        "connection_timeout": int(os.getenv("DB_POOL_TIMEOUT", "30")),
        "connection_lifetime": int(os.getenv("DB_POOL_LIFETIME", "1800"))
    },
    
    # 重试配置
    "retry": {
        "max_attempts": int(os.getenv("DB_RETRY_MAX", "3")),
        "retry_delay": float(os.getenv("DB_RETRY_DELAY", "1.0")),
        "retry_on_timeout": True
    },
    
    # 日志配置
    "logging": {
        "slow_query_threshold": float(os.getenv("DB_SLOW_QUERY", "1.0")),
        "log_queries": os.getenv("DB_LOG_QUERIES", "false").lower() == "true",
        "log_slow_queries": True
    }
}

def get_database_config(db_name: str = "default") -> Dict[str, Any]:
    """
    获取数据库配置
    
    Args:
        db_name: 数据库配置名称，默认为"default"
        
    Returns:
        Dict[str, Any]: 数据库配置字典
    """
    if db_name not in DATABASE_CONFIG:
        raise ValueError(f"未知的数据库配置: {db_name}")
    return DATABASE_CONFIG[db_name]

def get_connection_string(db_name: str = "default") -> str:
    """
    获取数据库连接字符串
    
    Args:
        db_name: 数据库配置名称，默认为"default"
        
    Returns:
        str: 数据库连接字符串
    """
    config = get_database_config(db_name)
    return (
        f"mysql+pymysql://{config['user']}:{config['password']}@"
        f"{config['host']}:{config['port']}/{config['database']}"
        f"?charset={config['charset']}"
    )

def get_pool_config() -> Dict[str, Any]:
    """获取连接池配置"""
    return DATABASE_CONFIG["pool"] 