#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
浏览器配置
"""

import os
from typing import Dict, Any

# 浏览器基础配置
BROWSER_BASE_CONFIG = {
    'headless': os.getenv('BROWSER_HEADLESS', 'false').lower() == 'true',
    'viewport': {
        'width': int(os.getenv('BROWSER_WIDTH', '1920')),
        'height': int(os.getenv('BROWSER_HEIGHT', '1080')),
    },
    'timeout': int(os.getenv('BROWSER_TIMEOUT', '30000')),
}

# 浏览器上下文配置
BROWSER_CONTEXT_CONFIG = {
    'ignore_https_errors': True,
    'bypass_csp': True,
    'viewport': BROWSER_BASE_CONFIG['viewport'],
    'user_agent': os.getenv('BROWSER_USER_AGENT', ''),
    'locale': os.getenv('BROWSER_LOCALE', 'zh-CN'),
    'timezone_id': os.getenv('BROWSER_TIMEZONE', 'Asia/Shanghai'),
    'permissions': ['geolocation'],
}

# 浏览器行为配置
BROWSER_BEHAVIOR_CONFIG = {
    'navigation': {
        'timeout': int(os.getenv('BROWSER_NAVIGATION_TIMEOUT', '30000')),
        'wait_until': os.getenv('BROWSER_WAIT_UNTIL', 'networkidle'),
    },
    'screenshot': {
        'path': os.getenv('BROWSER_SCREENSHOT_PATH', 'screenshots'),
        'full_page': True,
        'quality': int(os.getenv('BROWSER_SCREENSHOT_QUALITY', '90')),
    },
    'input': {
        'delay': float(os.getenv('BROWSER_INPUT_DELAY', '0.1')),
        'click_delay': float(os.getenv('BROWSER_CLICK_DELAY', '0.2')),
    },
}

def get_config() -> Dict[str, Any]:
    """获取配置
    
    Returns:
        Dict[str, Any]: 配置字典
    """
    return {
        'base': BROWSER_BASE_CONFIG,
        'context': BROWSER_CONTEXT_CONFIG,
        'behavior': BROWSER_BEHAVIOR_CONFIG,
    } 