"""
系统配置
集中管理所有系统级别的配置信息
"""

import os
from typing import Dict, Any

# 系统配置
SYSTEM_CONFIG = {
    # 问卷系统配置
    "questionnaire": {
        "default_scout_count": int(os.getenv("SCOUT_COUNT", "2")),      # 默认敢死队数量
        "default_target_count": int(os.getenv("TARGET_COUNT", "10")),   # 默认目标团队数量
        "max_concurrent_browsers": int(os.getenv("MAX_BROWSERS", "5")), # 最大并发浏览器数量
        "task_timeout_minutes": int(os.getenv("TASK_TIMEOUT", "60")),   # 任务超时时间（分钟）
        "retry_attempts": int(os.getenv("RETRY_ATTEMPTS", "3")),        # 重试次数
        "screenshot_enabled": os.getenv("SCREENSHOT_ENABLED", "true").lower() == "true",  # 是否启用截图
        "knowledge_base_ttl_hours": int(os.getenv("KB_TTL", "24"))      # 知识库生存时间（小时）
    },
    
    # 日志配置
    "logging": {
        "level": os.getenv("LOG_LEVEL", "INFO"),
        "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        "file_enabled": True,
        "file_path": "logs/questionnaire_system.log",
        "max_file_size": "10MB",
        "backup_count": 5
    },
    
    # 安全配置
    "security": {
        "api_key_required": False,     # 是否需要API密钥
        "rate_limit_enabled": True,    # 是否启用速率限制
        "max_requests_per_minute": 60, # 每分钟最大请求数
        "allowed_domains": [           # 允许的问卷域名
            "wjx.cn",
            "jinshuju.net", 
            "tencent.com",
            "example.com"  # 测试用
        ]
    },
    
    # 环境信息显示配置
    "environment_display": {
        "enabled": True,  # 是否启用环境信息显示
        "display_location": "scout_monitor",  # 显示位置：侦察监控区域
        "refresh_interval": 30,  # 刷新间隔（秒）
        "components": {
            "adspower_browser": True,  # 显示AdsPower浏览器详情
            "digital_human": True,     # 显示数字人信息
            "proxy_ip": True,         # 显示青果代理IP地址
            "system_status": True     # 显示系统状态
        }
    },
    
    # 人类化操作配置
    "human_like": {
        "anti_detection_enabled": True,
        "random_delays": {
            "thinking_time": (0.2, 1.2),      # 思考时间范围
            "typing_speed": (0.05, 0.20),     # 打字速度范围
            "click_delay": (0.1, 0.8),        # 点击延迟范围
            "inter_action_pause": (0.3, 2.0)  # 操作间隔范围
        },
        "input_strategies": [
            "natural_click_and_type",         # 自然点击输入
            "hesitation_and_retry",           # 犹豫重试
            "progressive_verification"         # 渐进验证
        ],
        "mouse_behavior": {
            "subtle_movements": True,
            "trajectory_randomization": True,
            "micro_adjustments": True
        }
    }
}

def get_system_config() -> Dict[str, Any]:
    """获取系统配置"""
    return SYSTEM_CONFIG

def get_questionnaire_config() -> Dict[str, Any]:
    """获取问卷系统配置"""
    return SYSTEM_CONFIG["questionnaire"]

def get_logging_config() -> Dict[str, Any]:
    """获取日志配置"""
    return SYSTEM_CONFIG["logging"]

def get_security_config() -> Dict[str, Any]:
    """获取安全配置"""
    return SYSTEM_CONFIG["security"]

def get_environment_display_config() -> Dict[str, Any]:
    """获取环境信息显示配置"""
    return SYSTEM_CONFIG["environment_display"]

def get_human_like_config() -> Dict[str, Any]:
    """获取人类化操作配置"""
    return SYSTEM_CONFIG["human_like"] 