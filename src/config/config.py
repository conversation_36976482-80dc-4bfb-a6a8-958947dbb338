"""统一配置管理模块"""

import os
from typing import Dict, Any, Optional
import json
import logging

logger = logging.getLogger(__name__)

class Config:
    """统一配置管理类"""
    
    def __init__(self):
        """初始化配置"""
        self.config = {
            'llm': {
                'model': 'gemini-2.0-flash',
                'api_key': os.getenv('GOOGLE_API_KEY', 'AIzaSyAfmaTObVEiq6R_c62T4jeEpyf6yp4WCP8'),
                'temperature': 0.1,
                'max_tokens': 4000,
                'max_retries': 3
            },
            'browser': {
                'use_vision': True,
                'max_actions_per_step': 15,
                'max_failures': 25,
                'questionnaire_mode': True,
                'never_give_up_mode': True
            },
            'adspower': {
                'api_base': 'http://localhost:50325',
                'group_id': '1',
                'user_data_path': os.path.expanduser('~/.adspower'),
                'timeout': 30000
            },
            'qinguo': {
                'api_key': os.getenv('QINGUO_API_KEY', ''),
                'proxy_type': 'http',
                'region': 'cn',
                'city': 'random',
                'isp': 'random'
            },
            'questionnaire': {
                'max_retries': 3,
                'timeout': 60000,
                'auto_submit': True,
                'validate_before_submit': True
            }
        }
        
        # 加载环境变量
        self._load_env()
        
        # 加载配置文件
        self._load_config_file()
        
        logger.info("✅ 配置加载完成")
        
    def _load_env(self):
        """加载环境变量"""
        # 加载.env文件
        try:
            from dotenv import load_dotenv
            load_dotenv()
        except ImportError:
            logger.warning("⚠️ python-dotenv未安装，跳过.env文件加载")
            
        # 更新配置
        for key, value in os.environ.items():
            if key.startswith('CONFIG_'):
                path = key[7:].lower().split('_')
                current = self.config
                for part in path[:-1]:
                    if part not in current:
                        current[part] = {}
                    current = current[part]
                current[path[-1]] = value
                
    def _load_config_file(self):
        """加载配置文件"""
        config_file = os.getenv('CONFIG_FILE', 'config.json')
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                self._merge_config(self.config, file_config)
                logger.info(f"✅ 成功加载配置文件: {config_file}")
            except Exception as e:
                logger.error(f"❌ 加载配置文件失败: {e}")
                
    def _merge_config(self, base: Dict[str, Any], update: Dict[str, Any]):
        """合并配置
        
        Args:
            base: 基础配置
            update: 更新配置
        """
        for key, value in update.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._merge_config(base[key], value)
            else:
                base[key] = value
                
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值
        
        Args:
            key: 配置键
            default: 默认值
            
        Returns:
            Any: 配置值
        """
        try:
            current = self.config
            for part in key.split('.'):
                current = current[part]
            return current
        except (KeyError, TypeError):
            return default
            
    def set(self, key: str, value: Any):
        """设置配置值
        
        Args:
            key: 配置键
            value: 配置值
        """
        parts = key.split('.')
        current = self.config
        for part in parts[:-1]:
            if part not in current:
                current[part] = {}
            current = current[part]
        current[parts[-1]] = value
        
    def update(self, config: Dict[str, Any]):
        """更新配置
        
        Args:
            config: 新配置
        """
        self._merge_config(self.config, config) 