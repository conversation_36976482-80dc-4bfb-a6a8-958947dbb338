#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
AdsPower配置
"""

import os
from typing import Dict, Any

# AdsPower API配置
ADSPOWER_API = {
    'host': os.getenv('ADSPOWER_API_HOST', 'http://localhost:50325'),
    'timeout': int(os.getenv('ADSPOWER_API_TIMEOUT', '30')),
    'retry_count': int(os.getenv('ADSPOWER_API_RETRY', '3')),
}

# 浏览器配置
BROWSER_CONFIG = {
    'headless': os.getenv('BROWSER_HEADLESS', 'false').lower() == 'true',
    'viewport': {
        'width': int(os.getenv('BROWSER_WIDTH', '1920')),
        'height': int(os.getenv('BROWSER_HEIGHT', '1080')),
    },
    'timeout': int(os.getenv('BROWSER_TIMEOUT', '30000')),
}

# 代理配置
PROXY_CONFIG = {
    'qinguo': {
        'api_key': os.getenv('QINGUO_API_KEY', ''),
        'api_secret': os.getenv('QINGUO_API_SECRET', ''),
        'tunnel_port': int(os.getenv('QINGUO_TUNNEL_PORT', '8080')),
    },
}

# 数字人配置
DIGITAL_HUMAN_CONFIG = {
    'default': {
        'personality': '专业、认真、细心',
        'behavior_pattern': '思考后再行动',
        'response_style': '客观、理性',
    },
}

def get_config() -> Dict[str, Any]:
    """获取配置
    
    Returns:
        Dict[str, Any]: 配置字典
    """
    return {
        'adspower': ADSPOWER_API,
        'browser': BROWSER_CONFIG,
        'proxy': PROXY_CONFIG,
        'digital_human': DIGITAL_HUMAN_CONFIG,
    } 