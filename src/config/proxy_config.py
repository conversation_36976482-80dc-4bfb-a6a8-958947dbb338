"""
青果代理配置
集中管理所有青果代理相关的配置信息
"""

import os
from typing import Dict, Any, Optional

# 青果代理配置
QINGUO_PROXY_CONFIG = {
    # 基础配置
    "business_id": os.getenv("QINGUO_BUSINESS_ID", "xnxmcc4a"),  # 业务标识
    "auth_key": os.getenv("QINGUO_AUTH_KEY", "A942CE1E"),       # Authkey
    "auth_pwd": os.getenv("QINGUO_AUTH_PWD", "B9FCD013057A"),   # Authpwd
    
    # API配置
    "base_url": os.getenv("QINGUO_BASE_URL", "https://proxy.qg.net"),  # API基础URL
    "api_version": "v1",
    "default_format": "json",
    "default_protocol": "http",
    
    # 隧道配置
    "tunnel_host": os.getenv("QINGUO_TUNNEL_HOST", "tun-szbhry.qg.net"),  # 隧道服务器
    "tunnel_port": os.getenv("QINGUO_TUNNEL_PORT", "17790"),              # 隧道端口
    
    # 请求配置
    "timeout": int(os.getenv("QINGUO_TIMEOUT", "30")),          # API超时时间
    "retry_attempts": int(os.getenv("QINGUO_RETRY", "3")),      # 重试次数
    "retry_delay": float(os.getenv("QINGUO_DELAY", "1.0")),     # 重试延迟
    
    # 认证格式模板
    "auth_formats": {
        "default": "{business_id}:{auth_key}",           # 默认格式
        "adspower": "{auth_key}:{auth_pwd}",            # AdsPower格式
        "tunnel": "{business_id}-{auth_key}:{auth_pwd}" # 隧道格式
    },
    
    # 代理池配置
    "pool_size": int(os.getenv("QINGUO_POOL_SIZE", "10")),      # 代理池大小
    "pool_refresh_interval": int(os.getenv("QINGUO_REFRESH", "300")),  # 刷新间隔（秒）
    
    # 计费配置
    "tunnel_cost": float(os.getenv("QINGUO_TUNNEL_COST", "0.02")),  # 隧道代理成本
    "short_cost": float(os.getenv("QINGUO_SHORT_COST", "0.01")),    # 短效代理成本
    "long_cost": float(os.getenv("QINGUO_LONG_COST", "0.05")),      # 长效代理成本
    
    # 高级特性
    "features": {
        "auto_rotation": True,        # 自动轮换
        "health_check": True,         # 健康检查
        "load_balancing": True,       # 负载均衡
        "session_persistence": True   # 会话保持
    }
}

def get_proxy_config() -> Dict[str, Any]:
    """获取代理配置"""
    return QINGUO_PROXY_CONFIG

def format_auth_string(format_type: str = "default") -> str:
    """
    根据不同场景格式化认证字符串
    
    Args:
        format_type: 格式类型，可选值：default, adspower, tunnel
        
    Returns:
        str: 格式化后的认证字符串
    """
    auth_format = QINGUO_PROXY_CONFIG["auth_formats"].get(format_type)
    if not auth_format:
        auth_format = QINGUO_PROXY_CONFIG["auth_formats"]["default"]
    
    return auth_format.format(
        business_id=QINGUO_PROXY_CONFIG["business_id"],
        auth_key=QINGUO_PROXY_CONFIG["auth_key"],
        auth_pwd=QINGUO_PROXY_CONFIG["auth_pwd"]
    )

def get_proxy_url(proxy_type: str = "http", format_type: str = "default") -> str:
    """
    获取完整的代理URL
    
    Args:
        proxy_type: 代理类型，可选值：http, https, socks5
        format_type: 认证格式类型，可选值：default, adspower, tunnel
        
    Returns:
        str: 完整的代理URL
    """
    auth = format_auth_string(format_type)
    return f"{proxy_type}://{auth}@{QINGUO_PROXY_CONFIG['tunnel_host']}:{QINGUO_PROXY_CONFIG['tunnel_port']}"

def validate_proxy_config() -> Optional[str]:
    """
    验证代理配置的完整性和正确性
    
    Returns:
        Optional[str]: 如果配置有误，返回错误信息；如果配置正确，返回None
    """
    required_fields = [
        "business_id", "auth_key", "auth_pwd",
        "base_url", "tunnel_host", "tunnel_port"
    ]
    
    for field in required_fields:
        if not QINGUO_PROXY_CONFIG.get(field):
            return f"缺少必要的配置项: {field}"
    
    # 验证URL格式
    if not QINGUO_PROXY_CONFIG["base_url"].startswith(("http://", "https://")):
        return "base_url 必须以 http:// 或 https:// 开头"
    
    # 验证端口格式
    try:
        port = int(QINGUO_PROXY_CONFIG["tunnel_port"])
        if not 1 <= port <= 65535:
            return "tunnel_port 必须在 1-65535 范围内"
    except ValueError:
        return "tunnel_port 必须是有效的端口号"
    
    return None

def get_api_params(endpoint: str) -> Dict[str, Any]:
    """
    获取API调用的标准参数
    
    Args:
        endpoint: API端点名称
        
    Returns:
        Dict[str, Any]: API参数字典
    """
    params = {
        "Key": QINGUO_PROXY_CONFIG["auth_key"],
        "Pwd": QINGUO_PROXY_CONFIG["auth_pwd"],
        "format": QINGUO_PROXY_CONFIG["default_format"]
    }
    
    # 根据不同端点添加特定参数
    if endpoint == "allocate":
        params.update({
            "protocol": QINGUO_PROXY_CONFIG["default_protocol"],
            "lb": 1  # 启用负载均衡
        })
    
    return params 