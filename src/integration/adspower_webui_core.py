#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
AdsPower + WebUI 核心集成模块
专注于数字人信息、AdsPower浏览器和青果代理的集成
"""

import asyncio
import logging
import time
import random
import json
import hashlib
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

# 导入必要组件
from enhanced_adspower_lifecycle import EnhancedAdsPowerLifecycle
from qinguo_proxy_manager import QinguoProxyManager
from qinguo_tunnel_proxy_manager import QinguoTunnelProxyManager

class AdsPowerWebUICore:
    """AdsPower + WebUI 核心集成类"""
    
    def __init__(self, digital_human_info: Optional[Dict[str, Any]] = None):
        """初始化集成
        
        Args:
            digital_human_info: 数字人信息，包含性格特征、行为模式等
        """
        self.logger = logging.getLogger(f"{__name__}.AdsPowerWebUICore")
        self.digital_human_info = digital_human_info or {}
        
        # 初始化组件
        self.adspower = EnhancedAdsPowerLifecycle()
        self.qinguo = QinguoProxyManager()
        self.tunnel_proxy = QinguoTunnelProxyManager()
        
        # 初始化状态
        self.state = {
            'browser_started': False,
            'proxy_configured': False,
            'browser_page': None,
            'last_action_time': time.time(),
            'current_url': None,
            'digital_human_ready': bool(digital_human_info)
        }
        
        self.logger.info("✅ AdsPower + WebUI 核心集成初始化完成")
        
    async def prepare_digital_human(self) -> bool:
        """准备数字人信息
        
        Returns:
            bool: 是否成功准备数字人信息
        """
        try:
            if not self.digital_human_info:
                self.logger.warning("⚠️ 未提供数字人信息")
                return False
                
            # 验证必要字段
            required_fields = ['personality', 'behavior_pattern', 'response_style']
            missing_fields = [f for f in required_fields if f not in self.digital_human_info]
            
            if missing_fields:
                self.logger.warning(f"⚠️ 数字人信息缺失字段: {missing_fields}")
                return False
                
            # 生成数字人指纹
            fingerprint = hashlib.sha256(
                json.dumps(self.digital_human_info, sort_keys=True).encode()
            ).hexdigest()
            
            self.digital_human_info['fingerprint'] = fingerprint
            self.state['digital_human_ready'] = True
            
            self.logger.info(f"✅ 数字人信息准备完成: {fingerprint[:8]}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 数字人信息准备失败: {e}")
            return False
            
    async def configure_proxy(self) -> bool:
        """配置青果代理
        
        Returns:
            bool: 是否成功配置代理
        """
        try:
            # 获取代理配置
            proxy_config = await self.qinguo.get_proxy()
            if not proxy_config:
                self.logger.error("❌ 获取代理配置失败")
                return False
                
            # 配置隧道代理
            tunnel_config = await self.tunnel_proxy.setup_tunnel(proxy_config)
            if not tunnel_config:
                self.logger.error("❌ 配置隧道代理失败")
                return False
                
            # 应用到AdsPower
            success = await self.adspower.set_proxy(tunnel_config)
            if not success:
                self.logger.error("❌ 应用代理到AdsPower失败")
                return False
                
            self.state['proxy_configured'] = True
            self.logger.info(f"✅ 代理配置成功: {proxy_config.get('ip')}:{proxy_config.get('port')}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 代理配置失败: {e}")
            return False
            
    async def start_browser(self) -> bool:
        """启动浏览器
        
        Returns:
            bool: 是否成功启动浏览器
        """
        try:
            # 确保代理已配置
            if not self.state['proxy_configured']:
                proxy_success = await self.configure_proxy()
                if not proxy_success:
                    return False
                    
            # 启动浏览器
            browser = await self.adspower.launch_browser()
            if not browser:
                self.logger.error("❌ 浏览器启动失败")
                return False
                
            # 创建新页面
            page = await browser.new_page()
            if not page:
                self.logger.error("❌ 创建页面失败")
                await browser.close()
                return False
                
            self.state['browser_started'] = True
            self.state['browser_page'] = page
            
            self.logger.info("✅ 浏览器启动成功")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 浏览器启动失败: {e}")
            return False
            
    async def navigate_to_url(self, url: str) -> bool:
        """导航到指定URL
        
        Args:
            url: 目标URL
            
        Returns:
            bool: 是否成功导航
        """
        try:
            if not self.state['browser_started'] or not self.state['browser_page']:
                self.logger.error("❌ 浏览器未启动")
                return False
                
            page = self.state['browser_page']
            
            # 执行导航
            await page.goto(url, timeout=30000)
            
            self.state['current_url'] = url
            self.logger.info(f"✅ 成功导航到: {url}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 导航失败: {e}")
            return False
            
    async def cleanup(self):
        """清理资源"""
        try:
            # 关闭浏览器
            if self.state['browser_started'] and self.state['browser_page']:
                await self.state['browser_page'].close()
                self.state['browser_started'] = False
                self.state['browser_page'] = None
                
            # 释放代理
            if self.state['proxy_configured']:
                await self.qinguo.release_proxy()
                await self.tunnel_proxy.cleanup()
                self.state['proxy_configured'] = False
                
            self.logger.info("✅ 资源清理完成")
            
        except Exception as e:
            self.logger.error(f"❌ 资源清理失败: {e}")
            raise 