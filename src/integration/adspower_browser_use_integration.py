"""AdsPower浏览器集成模块"""
import logging
import asyncio
from typing import Dict, Any, Optional

from browser_use.browser import <PERSON><PERSON>er, BrowserConfig
from browser_use.dom import DOMElement, DOMOperations
from browser_use.human_input import HumanLikeInput
from src.agent.browser_use.browser_use_agent import BrowserUseAgent
from src.browser.controller import CustomController
from src.config import Config

logger = logging.getLogger(__name__)

class AdsPowerBrowserUseIntegration:
    """AdsPower浏览器集成类"""
    
    def __init__(self, config: Config):
        """初始化AdsPower浏览器集成
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.browser = None
        self.controller = None
        self.human_input = None
        
    async def setup(self):
        """设置浏览器环境"""
        try:
            # 创建人类式输入代理
            self.human_input = HumanLikeInput()
            logger.info("✅ 人类式输入代理创建成功")
            
            # 创建智能控制器
            try:
                self.controller = CustomController(
                    human_input=self.human_input,
                    config=self.config
                )
                logger.info("✅ 智能控制器创建成功")
            except Exception as e:
                logger.error(f"❌ 智能控制器创建失败: {e}")
                # 强制重新创建
                logger.info("🔧 强制重新创建CustomController（确保WebUI智能性）...")
                try:
                    self.controller = CustomController(
                        human_input=self.human_input,
                        config=self.config,
                        force_create=True
                    )
                except Exception as e:
                    logger.error(f"❌ 强制创建CustomController也失败: {e}")
                    logger.warning("⚠️ 使用紧急CustomController替代方案")
                    self.controller = CustomController(
                        human_input=self.human_input,
                        config=self.config,
                        emergency_mode=True
                    )
            
        except Exception as e:
            logger.error(f"❌ 设置浏览器环境失败: {e}")
            raise
            
    async def create_browser_use_agent(self, task: str, digital_human_info: Optional[Dict] = None) -> BrowserUseAgent:
        """创建浏览器使用代理
        
        Args:
            task: 任务描述
            digital_human_info: 数字人信息
            
        Returns:
            BrowserUseAgent: 浏览器使用代理实例
        """
        try:
            logger.info("🎯 强制使用CustomController创建BrowserUseAgent...")
            agent = BrowserUseAgent(
                task=task,
                controller=self.controller,
                digital_human_info=digital_human_info,
                questionnaire_mode=True,
                never_give_up_mode=True
            )
            return agent
            
        except Exception as e:
            logger.error(f"❌ 创建BrowserUseAgent失败: {e}")
            raise
            
    async def execute_questionnaire(self, url: str, digital_human_info: Dict[str, Any]):
        """执行问卷任务
        
        Args:
            url: 问卷URL
            digital_human_info: 数字人信息
            
        Returns:
            Dict: 执行结果
        """
        try:
            # 设置浏览器环境
            await self.setup()
            
            # 创建任务描述
            task = f"填写问卷调查: {url}"
            
            # 创建浏览器使用代理
            agent = await self.create_browser_use_agent(task, digital_human_info)
            
            # 执行问卷任务
            logger.info(f"🚀 开始执行问卷任务（基于testWenjuan.py成功模式）...")
            try:
                result = await agent.execute_questionnaire(url)
                if result.get("success"):
                    logger.info(f"✅ 问卷任务执行成功: {digital_human_info.get('name')}")
                else:
                    logger.error(f"❌ 问卷任务执行失败: {result.get('error')}")
                return result
                
            except Exception as e:
                logger.error(f"❌ testWenjuan.py模式执行失败: {e}")
                logger.error(f"⚠️ 任务执行遇到问题，但浏览器将保持运行: {e}")
                return {"success": False, "error": str(e)}
                
        except Exception as e:
            logger.error(f"❌ 执行问卷任务失败: {e}")
            return {"success": False, "error": str(e)}
        finally:
            # 记录完成状态
            name = digital_human_info.get("name", "未知用户")
            status = "成功" if result.get("success") else "失败"
            logger.info(f"🎉 智能问卷工作流完成: {name}, 状态: {status}") 