"""统一配置管理模块"""
import os
from typing import Dict, Any, Optional
import json
import logging

logger = logging.getLogger(__name__)

class Config:
    """统一配置管理类"""
    
    def __init__(self):
        self.config = {
            'llm': {
                'model': 'gemini-2.0-flash',
                'api_key': os.getenv('GOOGLE_API_KEY', 'AIzaSyAfmaTObVEiq6R_c62T4jeEpyf6yp4WCP8'),
                'temperature': 0.1,
                'max_tokens': 4000,
                'max_retries': 3
            },
            'browser': {
                'use_vision': True,
                'max_actions_per_step': 15,
                'max_failures': 25,
                'questionnaire_mode': True,
                'never_give_up_mode': True
            },
            'adspower': {
                'api_base': 'http://localhost:50325',
                'group_id': '1',
                'user_data_path': './user_data'
            },
            'qinguo': {
                'api_base': 'http://api.qingguo.com',
                'username': os.getenv('QINGUO_USERNAME', ''),
                'password': os.getenv('QINGUO_PASSWORD', '')
            }
        }
        
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        return self.config.get(key, default)
        
    def set(self, key: str, value: Any):
        """设置配置值"""
        self.config[key] = value
        
    def update(self, updates: Dict[str, Any]):
        """批量更新配置"""
        self.config.update(updates)
        
    def load_from_file(self, filepath: str):
        """从文件加载配置"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                updates = json.load(f)
                self.update(updates)
            logger.info(f"✅ 从{filepath}加载配置成功")
        except Exception as e:
            logger.error(f"❌ 加载配置失败: {e}")
            
    def save_to_file(self, filepath: str):
        """保存配置到文件"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            logger.info(f"✅ 配置已保存到{filepath}")
        except Exception as e:
            logger.error(f"❌ 保存配置失败: {e}")

# 创建全局配置实例
config = Config() 