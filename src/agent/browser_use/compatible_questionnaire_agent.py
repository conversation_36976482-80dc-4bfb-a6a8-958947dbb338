"""
兼容版智能问卷Agent - 基于browser-use原生Agent的包装器
完全兼容browser-use webui的执行流程，专门优化问卷填写
"""

import logging
import asyncio
import time
from typing import Optional, Dict, Any, List, Callable

# 导入真正的browser_use组件
try:
    # 导入官方browser-use的Agent
    from browser_use import Agent
    from browser_use.agent.views import AgentHistoryList, AgentStepInfo, AgentOutput
    from browser_use.browser.context import BrowserContext
    from browser_use.browser.views import BrowserState
    from browser_use.browser.browser import Browser
    browser_use_available = True
    logging.info("✅ 官方browser_use组件导入成功")
except ImportError as e:
    logging.warning(f"⚠️ 官方browser_use组件导入失败: {e}")
    try:
        # 备用：使用我们的本地版本
        from browser_use.agent.service import Agent
        from browser_use.agent.views import AgentHistoryList, AgentStepInfo, AgentOutput
        from browser_use.browser.context import BrowserContext
        from browser_use.browser.views import BrowserState
        from browser_use.browser.browser import Browser
        browser_use_available = True
        logging.info("✅ 本地browser_use组件导入成功")
    except ImportError as e2:
        logging.error(f"❌ 所有browser_use组件导入都失败: {e2}")
        browser_use_available = False

        # 创建占位符类
        class AgentHistoryList:
            def __init__(self): self.history = []
            def total_duration_seconds(self): return 0
            def total_input_tokens(self): return 0
            def final_result(self): return None
            def errors(self): return []

        class AgentStepInfo: pass
        class AgentOutput: pass
        class BrowserState: pass
        class BrowserContext: pass
        class Browser: pass
        class Agent:
            def __init__(self, **kwargs): pass
            async def run(self, max_steps=100): return {"success": False}


class CompatibleQuestionnaireAgent:
    """兼容版智能问卷Agent - 基于browser-use原生Agent的包装器"""

    def __init__(self, task: str, llm, browser_context: BrowserContext = None,
                 browser: Browser = None, controller=None, digital_human_info: Dict = None,
                 register_new_step_callback: Optional[Callable] = None,
                 register_done_callback: Optional[Callable] = None,
                 use_vision: bool = True, max_actions_per_step: int = 10,
                 override_system_message: Optional[str] = None,
                 extend_system_message: Optional[str] = None,
                 max_input_tokens: int = 128000,
                 tool_calling_method: Optional[str] = None,
                 planner_llm = None,
                 use_vision_for_planner: bool = False,
                 source: str = "intelligent_questionnaire",
                 **kwargs):
        """初始化兼容版智能问卷Agent"""
        self.logger = logging.getLogger(f"{__name__}.CompatibleQuestionnaireAgent")

        if not browser_use_available:
            self.logger.error("❌ browser_use不可用")
            raise ImportError("browser_use组件不可用")

        # 保存参数
        self.task = task
        self.llm = llm
        self.browser_context = browser_context
        self.browser = browser
        self.controller = controller  # 保存controller但不传递给原生Agent
        self.digital_human_info = digital_human_info or {}

        self.logger.info(f"✅ 参数保存完成，controller类型: {type(controller) if controller else 'None'}")

        # 构建问卷专用系统消息
        questionnaire_system_message = self._build_questionnaire_system_message()

        # 合并系统消息
        final_system_message = override_system_message or questionnaire_system_message
        if extend_system_message:
            final_system_message += f"\n\n{extend_system_message}"

        # 创建官方browser_use Agent（使用正确的参数）
        self.logger.info(f"🔧 开始创建Agent，参数详情:")
        self.logger.info(f"   task: {task[:100]}...")
        self.logger.info(f"   llm: {type(llm)}")
        self.logger.info(f"   browser_context: {type(browser_context)}")
        self.logger.info(f"   use_vision: {use_vision}")
        self.logger.info(f"   max_actions_per_step: {max_actions_per_step}")

        try:
            self.logger.info("🔧 尝试使用官方browser_use Agent...")
            self.agent = Agent(
                task=task,
                llm=llm,
                browser_context=browser_context,
                max_actions_per_step=max_actions_per_step,
                system_message=final_system_message,
                max_input_tokens=max_input_tokens,
                tool_calling_method=tool_calling_method,
                planner_llm=planner_llm
            )
            self.logger.info("✅ 使用官方browser_use Agent创建成功")
        except TypeError as e:
            self.logger.error(f"❌ 官方Agent创建失败(参数错误): {e}")
            self.logger.info("🔧 尝试使用简化参数创建Agent...")
            try:
                # 备用方案：使用简化参数
                self.agent = Agent(
                    task=task,
                    llm=llm,
                    browser_context=browser_context
                )
                self.logger.info("✅ 使用简化参数创建Agent成功")
            except Exception as e2:
                self.logger.error(f"❌ 简化参数Agent创建也失败: {e2}")
                raise e2
        except Exception as e:
            self.logger.error(f"❌ 官方Agent创建失败(其他错误): {e}")
            self.logger.info("🔧 尝试使用简化参数创建Agent...")
            try:
                # 备用方案：使用简化参数
                self.agent = Agent(
                    task=task,
                    llm=llm,
                    browser_context=browser_context
                )
                self.logger.info("✅ 使用简化参数创建Agent成功")
            except Exception as e2:
                self.logger.error(f"❌ 简化参数Agent创建也失败: {e2}")
                raise e2

        # 注册回调函数
        if register_new_step_callback and hasattr(self.agent, 'register_new_step_callback'):
            self.agent.register_new_step_callback = register_new_step_callback
            self.logger.info("✅ 新步骤回调函数已注册")

        if register_done_callback and hasattr(self.agent, 'register_done_callback'):
            self.agent.register_done_callback = register_done_callback
            self.logger.info("✅ 完成回调函数已注册")

        # 代理属性到原生Agent
        self.state = self.agent.state if hasattr(self.agent, 'state') else type('AgentState', (), {
            'agent_id': None,
            'paused': False,
            'stopped': False
        })()

        self.settings = self.agent.settings if hasattr(self.agent, 'settings') else type('AgentSettings', (), {
            'generate_gif': None
        })()

        # 历史记录
        self.history = AgentHistoryList()
        self._step_count = 0
        self._start_time = None

        self.logger.info("✅ 兼容版智能问卷Agent初始化完成")

    def _build_questionnaire_system_message(self) -> str:
        """构建问卷专用系统消息"""
        digital_human = self.digital_human_info

        system_message = f"""你是一个专业的智能问卷填写助手，具备以下能力：

🎯 核心任务：
- 自动识别和填写各种类型的问卷表单
- 根据数字人特征选择合适的答案
- 确保所有必填项都被正确填写

👤 数字人档案：
- 姓名：{digital_human.get('name', '未知')}
- 年龄：{digital_human.get('age', '未知')}
- 性别：{digital_human.get('gender', '未知')}
- 职业：{digital_human.get('occupation', '未知')}
- 收入：{digital_human.get('income', '未知')}
- 居住地：{digital_human.get('location', '未知')}
- 品牌偏好：{digital_human.get('brand_preferences', '未知')}

🔧 操作规则：
1. 仔细分析页面上的所有表单元素
2. 根据数字人特征选择最合适的答案
3. 优先填写页面顶部的元素
4. 对于单选题，选择最符合数字人特征的选项
5. 对于多选题，可以选择多个相关选项
6. 对于文本输入，提供简洁但相关的回答
7. 确保所有必填项都被填写
8. 填写完成后提交表单

💡 智能策略：
- 如果遇到不确定的问题，选择中性或常见的答案
- 保持答案的一致性和逻辑性
- 避免选择极端或不合理的选项
- 注意问题之间的关联性

请开始智能问卷填写任务。"""

        return system_message

    async def run(self, max_steps: int = 100) -> AgentHistoryList:
        """执行问卷填写任务 - 直接调用browser_use原生Agent"""
        self.logger.info(f"🚀 开始智能问卷填写，最大步数: {max_steps}")
        self.logger.info(f"🔍 Agent类型: {type(self.agent)}")
        self.logger.info(f"🔍 Agent模块: {self.agent.__class__.__module__}")
        self.logger.info(f"🔍 Agent类名: {self.agent.__class__.__name__}")

        # 检查Agent是否有run方法
        if hasattr(self.agent, 'run'):
            self.logger.info("✅ Agent有run方法")
            self.logger.info(f"🔍 run方法类型: {type(self.agent.run)}")
        else:
            self.logger.error("❌ Agent没有run方法")
            self.logger.error(f"❌ Agent可用方法: {[m for m in dir(self.agent) if not m.startswith('_')]}")
            return AgentHistoryList()

        self._start_time = time.time()

        try:
            self.logger.info("🔧 开始调用Agent.run()...")
            # 直接调用browser_use原生Agent的run方法
            result = await self.agent.run(max_steps=max_steps)
            self.logger.info(f"✅ Agent.run()执行完成")
            self.logger.info(f"🔍 结果类型: {type(result)}")
            self.logger.info(f"🔍 结果内容: {result}")

            # 返回结果
            return result

        except Exception as e:
            self.logger.error(f"❌ 问卷填写过程中发生错误: {e}")
            self.logger.error(f"❌ 错误类型: {type(e).__name__}")
            import traceback
            self.logger.error(f"❌ 错误堆栈: {traceback.format_exc()}")
            # 返回空的历史记录
            return AgentHistoryList()

    def pause(self):
        """暂停Agent"""
        if hasattr(self.agent, 'pause'):
            self.agent.pause()
        elif hasattr(self.state, 'paused'):
            self.state.paused = True

    def resume(self):
        """恢复Agent"""
        if hasattr(self.agent, 'resume'):
            self.agent.resume()
        elif hasattr(self.state, 'paused'):
            self.state.paused = False

    def stop(self):
        """停止Agent"""
        if hasattr(self.agent, 'stop'):
            self.agent.stop()
        elif hasattr(self.state, 'stopped'):
            self.state.stopped = True

    def add_new_task(self, task: str):
        """添加新任务"""
        if hasattr(self.agent, 'add_new_task'):
            self.agent.add_new_task(task)
        else:
            self.task = task

    def save_history(self, file_path: str):
        """保存历史记录"""
        if hasattr(self.agent, 'save_history'):
            self.agent.save_history(file_path)
        else:
            self.logger.warning("⚠️ 原生Agent不支持save_history方法")

    def get_browser_status(self) -> Optional[str]:
        """获取浏览器状态"""
        try:
            if hasattr(self, 'browser_context') and self.browser_context:
                return "running"
            return "unknown"
        except Exception as e:
            self.logger.warning(f"⚠️ 无法获取浏览器状态: {e}")
            return None