"""
兼容版智能问卷Agent - 完全兼容browser-use原版API
基于原版browser-use webui的Agent实现，专门优化问卷填写
"""

import logging
import asyncio
import json
import time
from typing import Optional, Dict, Any, List, Callable, Awaitable
from datetime import datetime

# 导入browser_use组件
try:
    from browser_use.agent.views import Agent<PERSON><PERSON>oryList, AgentStepInfo, AgentOutput
    from browser_use.browser.context import BrowserContext
    from browser_use.browser.views import BrowserState
    from browser_use.browser.browser import Browser
    from browser_use.agent.service import Agent
    browser_use_available = True
    logging.info("✅ browser_use组件导入成功")
except ImportError as e:
    logging.warning(f"⚠️ browser_use组件导入失败: {e}")
    browser_use_available = False

    # 创建占位符类
    class AgentHistoryList:
        def __init__(self): self.history = []
        def total_duration_seconds(self): return 0
        def total_input_tokens(self): return 0
        def final_result(self): return None
        def errors(self): return []

    class AgentStepInfo: pass
    class AgentOutput: pass
    class BrowserState: pass
    class BrowserContext: pass
    class Browser: pass
    class Agent:
        def __init__(self, **kwargs): pass
        async def run(self, max_steps=100): return {"success": False}


class CompatibleQuestionnaireAgent(Agent):
    """兼容版智能问卷Agent - 完全兼容browser-use API"""
    
    def __init__(self, task: str, llm, browser_context: BrowserContext = None,
                 browser: Browser = None, controller=None, digital_human_info: Dict = None,
                 register_new_step_callback: Optional[Callable] = None,
                 register_done_callback: Optional[Callable] = None,
                 use_vision: bool = True, max_actions_per_step: int = 10,
                 override_system_message: Optional[str] = None,
                 extend_system_message: Optional[str] = None,
                 max_input_tokens: int = 128000,
                 tool_calling_method: Optional[str] = None,
                 planner_llm = None,
                 use_vision_for_planner: bool = False,
                 source: str = "intelligent_questionnaire",
                 **kwargs):
        """初始化兼容版智能问卷Agent"""
        self.logger = logging.getLogger(f"{__name__}.CompatibleQuestionnaireAgent")
        
        if not browser_use_available:
            self.logger.error("❌ browser_use不可用")
            raise ImportError("browser_use组件不可用")
        
        # 调用父类初始化（如果可用）
        if hasattr(Agent, '__init__'):
            try:
                super().__init__(
                    task=task,
                    llm=llm,
                    browser_context=browser_context
                )
            except Exception as e:
                self.logger.warning(f"⚠️ 父类初始化失败，使用自定义初始化: {e}")
        
        # 核心属性
        self.task = task
        self.llm = llm
        self.browser_context = browser_context
        self.browser = browser
        self.controller = controller
        self.digital_human_info = digital_human_info or {}
        
        # Agent配置
        self.use_vision = use_vision
        self.max_actions_per_step = max_actions_per_step
        self.override_system_message = override_system_message
        self.extend_system_message = extend_system_message
        self.max_input_tokens = max_input_tokens
        self.tool_calling_method = tool_calling_method
        self.planner_llm = planner_llm
        self.use_vision_for_planner = use_vision_for_planner
        self.source = source
        
        # 回调函数
        self.register_new_step_callback = register_new_step_callback
        self.register_done_callback = register_done_callback
        
        # 状态管理（兼容原版API）
        self.state = type('AgentState', (), {
            'agent_id': None,
            'paused': False,
            'stopped': False
        })()
        
        # 设置管理（兼容原版API）
        self.settings = type('AgentSettings', (), {
            'generate_gif': None
        })()
        
        # 问卷专用状态
        self.questionnaire_state = {
            "current_question": 0,
            "total_questions": 0,
            "answered_questions": [],
            "errors": []
        }
        
        # 历史记录
        self.history = AgentHistoryList()
        self._step_count = 0
        self._start_time = None
        
        self.logger.info("✅ 兼容版智能问卷Agent初始化完成")
    
    async def run(self, max_steps: int = 100) -> Dict[str, Any]:
        """执行问卷填写任务 - 兼容原版API"""
        self.logger.info(f"🚀 开始智能问卷填写，最大步数: {max_steps}")
        self._start_time = time.time()
        
        try:
            # 检查状态
            if self.state.stopped:
                return {"success": False, "error": "Agent已停止"}
            
            # 分析页面结构
            page_analysis = await self.analyze_questionnaire_page()
            if not page_analysis["success"]:
                return {"success": False, "error": "页面分析失败"}
            
            # 执行问卷填写
            result = await self.execute_questionnaire_filling(max_steps)
            
            # 调用完成回调
            if self.register_done_callback:
                try:
                    self.register_done_callback(self.history)
                except Exception as e:
                    self.logger.error(f"❌ 完成回调失败: {e}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ 问卷填写过程中发生错误: {e}")
            return {
                "success": False,
                "error": str(e),
                "steps": self._step_count,
                "questionnaire_state": self.questionnaire_state
            }
    
    async def analyze_questionnaire_page(self) -> Dict[str, Any]:
        """分析问卷页面结构"""
        try:
            self.logger.info("🔍 开始分析问卷页面结构...")
            
            page = await self.browser_context.get_current_page()
            
            # 获取页面基本信息
            title = await page.title()
            url = page.url
            
            # 等待页面完全加载
            await page.wait_for_load_state('networkidle')
            
            # 查找问卷元素
            questions = await page.query_selector_all('[class*="field"], [class*="question"], .div_question, .field')
            inputs = await page.query_selector_all('input, select, textarea')
            
            self.questionnaire_state["total_questions"] = len(questions)
            
            self.logger.info(f"✅ 页面分析完成: 标题={title}, 问题数={len(questions)}, 输入元素={len(inputs)}")
            
            return {
                "success": True,
                "title": title,
                "url": url,
                "questions_count": len(questions),
                "inputs_count": len(inputs)
            }
            
        except Exception as e:
            self.logger.error(f"❌ 页面分析失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def execute_questionnaire_filling(self, max_steps: int) -> Dict[str, Any]:
        """执行问卷填写逻辑"""
        self.logger.info("🎯 开始执行智能问卷填写...")
        
        filled_count = 0
        
        for step in range(max_steps):
            if self.state.stopped:
                self.logger.info("🛑 Agent已停止")
                break
                
            if self.state.paused:
                self.logger.info("⏸️ Agent已暂停，等待恢复...")
                while self.state.paused and not self.state.stopped:
                    await asyncio.sleep(0.5)
                if self.state.stopped:
                    break
            
            self._step_count = step + 1
            
            try:
                # 观察当前状态
                observation = await self.observe_questionnaire_state()
                
                # 使用LLM分析并决定动作
                action_decision = await self.llm_analyze_and_decide(observation)
                
                if action_decision.get("completed", False):
                    self.logger.info("✅ 问卷填写完成")
                    break
                
                # 执行动作
                action_result = await self.execute_questionnaire_action(action_decision)
                
                if action_result.get("success", False):
                    filled_count += 1
                    self.questionnaire_state["answered_questions"].append({
                        "step": self._step_count,
                        "action": action_decision,
                        "result": action_result
                    })
                
                # 调用步骤回调
                if self.register_new_step_callback:
                    try:
                        # 创建模拟的BrowserState和AgentOutput
                        browser_state = type('BrowserState', (), {
                            'screenshot': None,
                            'url': observation.get('url', ''),
                            'title': observation.get('title', '')
                        })()
                        
                        agent_output = type('AgentOutput', (), {
                            'action': [action_decision],
                            'current_state': browser_state
                        })()
                        
                        await self.register_new_step_callback(browser_state, agent_output, self._step_count)
                    except Exception as e:
                        self.logger.error(f"❌ 步骤回调失败: {e}")
                
                # 短暂等待
                await asyncio.sleep(1)
                
            except Exception as e:
                self.logger.error(f"❌ 步骤 {self._step_count} 执行失败: {e}")
                self.questionnaire_state["errors"].append({
                    "step": self._step_count,
                    "error": str(e)
                })
        
        return {
            "success": filled_count > 0,
            "steps": self._step_count,
            "filled_questions": filled_count,
            "questionnaire_state": self.questionnaire_state,
            "message": f"完成 {filled_count} 个问题的填写"
        }
    
    # 兼容原版API的方法
    def add_new_task(self, task: str):
        """添加新任务"""
        self.task = task
        self.logger.info(f"✅ 添加新任务: {task}")
    
    def pause(self):
        """暂停Agent"""
        self.state.paused = True
        self.logger.info("⏸️ Agent已暂停")
    
    def resume(self):
        """恢复Agent"""
        self.state.paused = False
        self.logger.info("▶️ Agent已恢复")
    
    def stop(self):
        """停止Agent"""
        self.state.stopped = True
        self.state.paused = False
        self.logger.info("🛑 Agent已停止")
    
    def save_history(self, file_path: str):
        """保存历史记录"""
        try:
            history_data = {
                "agent_id": self.state.agent_id,
                "task": self.task,
                "steps": self._step_count,
                "questionnaire_state": self.questionnaire_state,
                "duration": time.time() - self._start_time if self._start_time else 0
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(history_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"✅ 历史记录已保存: {file_path}")
        except Exception as e:
            self.logger.error(f"❌ 保存历史记录失败: {e}")

    async def observe_questionnaire_state(self) -> Dict[str, Any]:
        """观察当前问卷状态"""
        try:
            page = await self.browser_context.get_current_page()

            # 获取当前可交互的元素
            current_interactive = await self.get_current_interactive_elements(page)

            return {
                "url": page.url,
                "title": await page.title(),
                "step": self._step_count,
                "digital_human": self.digital_human_info,
                "current_interactive": current_interactive,
                "remaining_elements": len(current_interactive)
            }

        except Exception as e:
            self.logger.error(f"❌ 状态观察失败: {e}")
            return {"error": str(e), "step": self._step_count}

    async def get_current_interactive_elements(self, page) -> List[Dict[str, Any]]:
        """获取当前可交互的元素"""
        try:
            interactive_elements = []

            # 查找可见的未填写单选按钮
            radios = await page.query_selector_all('input[type="radio"]:visible')
            for radio in radios[:3]:  # 只取前3个
                try:
                    name = await radio.get_attribute('name')
                    value = await radio.get_attribute('value')
                    is_checked = await radio.is_checked()
                    if name and not is_checked:
                        interactive_elements.append({
                            "type": "radio",
                            "name": name,
                            "value": value,
                            "selector": f'input[name="{name}"][value="{value}"]'
                        })
                except:
                    continue

            # 查找可见的未选择下拉框
            selects = await page.query_selector_all('select:visible')
            for select in selects[:2]:  # 只取前2个
                try:
                    name = await select.get_attribute('name') or await select.get_attribute('id')
                    if name:
                        interactive_elements.append({
                            "type": "select",
                            "name": name,
                            "selector": f'select[name="{name}"]' if await select.get_attribute('name') else f'#{name}'
                        })
                except:
                    continue

            # 查找可见的空文本框
            text_inputs = await page.query_selector_all('input[type="text"]:visible, textarea:visible')
            for text_input in text_inputs[:2]:  # 只取前2个
                try:
                    name = await text_input.get_attribute('name') or await text_input.get_attribute('id')
                    value = await text_input.input_value()
                    if name and (not value or value.strip() == ""):
                        interactive_elements.append({
                            "type": "text",
                            "name": name,
                            "selector": f'input[name="{name}"]' if await text_input.get_attribute('name') else f'#{name}'
                        })
                except:
                    continue

            return interactive_elements

        except Exception as e:
            self.logger.error(f"❌ 获取交互元素失败: {e}")
            return []

    async def llm_analyze_and_decide(self, observation: Dict[str, Any]) -> Dict[str, Any]:
        """使用LLM分析当前状态并决定下一步动作"""
        try:
            # 构建提示词
            prompt = self.build_questionnaire_prompt(observation)

            # 调用LLM
            response = await self.llm.ainvoke(prompt)

            # 解析LLM响应
            action_decision = self.parse_llm_response(response.content)

            return action_decision

        except Exception as e:
            self.logger.error(f"❌ LLM分析失败: {e}")
            return {"action": "wait", "completed": False, "error": str(e)}

    def build_questionnaire_prompt(self, observation: Dict[str, Any]) -> str:
        """构建问卷填写的提示词"""
        digital_human = self.digital_human_info
        current_interactive = observation.get('current_interactive', [])

        # 构建当前可交互元素的描述
        interactive_desc = ""
        if current_interactive:
            interactive_desc = "当前可交互元素：\n"
            for i, element in enumerate(current_interactive):
                interactive_desc += f"{i+1}. {element['type']} - {element['name']} (选择器: {element['selector']})\n"

        prompt = f"""
你是一个专业的问卷填写AI助手。请根据数字人信息智能填写问卷。

数字人档案：
- 姓名：{digital_human.get('name', '未知')}
- 年龄：{digital_human.get('age', '未知')}
- 性别：{digital_human.get('gender', '未知')}
- 职业：{digital_human.get('occupation', '未知')}
- 品牌偏好：{digital_human.get('brand_preferences', '未知')}

当前状态：
- 页面：{observation.get('title', '')}
- 剩余元素：{observation.get('remaining_elements', 0)}

{interactive_desc}

请选择最合适的下一步动作。返回JSON格式：
{{
    "action": "click|fill|select|wait|completed",
    "target": "精确的CSS选择器",
    "value": "要填入的值（仅对fill/select动作）",
    "reasoning": "选择理由",
    "completed": false
}}

填写规则：
1. 根据数字人特征选择合适的答案
2. 优先填写最上方的未填写元素
3. 如果没有可交互元素，设置 "completed": true
4. 使用提供的精确选择器

现在请选择下一步动作：
"""
        return prompt

    def parse_llm_response(self, response: str) -> Dict[str, Any]:
        """解析LLM响应"""
        try:
            # 尝试解析JSON
            if "{" in response and "}" in response:
                json_start = response.find("{")
                json_end = response.rfind("}") + 1
                json_str = response[json_start:json_end]
                return json.loads(json_str)
            else:
                # 如果不是JSON，返回默认动作
                return {
                    "action": "wait",
                    "reasoning": "无法解析LLM响应",
                    "completed": False
                }
        except Exception as e:
            self.logger.error(f"❌ LLM响应解析失败: {e}")
            return {
                "action": "wait",
                "error": str(e),
                "completed": False
            }

    async def execute_questionnaire_action(self, action_decision: Dict[str, Any]) -> Dict[str, Any]:
        """执行具体的问卷动作"""
        try:
            page = await self.browser_context.get_current_page()
            action = action_decision.get("action", "wait")
            target = action_decision.get("target", "")
            value = action_decision.get("value", "")

            self.logger.info(f"🎯 执行动作: {action} -> {target}")

            if action == "completed":
                return {"success": True, "action": "completed", "completed": True}

            if action == "click":
                return await self.smart_click_element(page, target)

            elif action == "fill":
                if not value:
                    value = self.generate_appropriate_value(target)
                return await self.smart_fill_element(page, target, value)

            elif action == "select":
                if not value:
                    value = await self.generate_appropriate_select_value(page, target)
                return await self.smart_select_element(page, target, value)

            elif action == "wait":
                await asyncio.sleep(2)
                return {"success": True, "action": "wait"}

            return {"success": False, "error": f"未知动作: {action}"}

        except Exception as e:
            self.logger.error(f"❌ 动作执行失败: {e}")
            return {"success": False, "error": str(e)}

    async def smart_click_element(self, page, target: str) -> Dict[str, Any]:
        """智能点击元素"""
        try:
            # 尝试多种定位策略
            selectors_to_try = [target, f"[name='{target}']", f"#{target}"]

            for selector in selectors_to_try:
                try:
                    await page.wait_for_selector(selector, timeout=5000)
                    element = await page.query_selector(selector)

                    if element and await element.is_visible():
                        await element.scroll_into_view_if_needed()
                        await asyncio.sleep(0.5)
                        await element.click()

                        self.logger.info(f"✅ 成功点击元素: {selector}")
                        return {"success": True, "action": "click", "target": selector}

                except Exception:
                    continue

            return {"success": False, "error": f"无法找到可点击的元素: {target}"}

        except Exception as e:
            return {"success": False, "error": f"点击操作失败: {str(e)}"}

    async def smart_fill_element(self, page, target: str, value: str) -> Dict[str, Any]:
        """智能填写元素"""
        try:
            selectors_to_try = [target, f"[name='{target}']", f"#{target}"]

            for selector in selectors_to_try:
                try:
                    await page.wait_for_selector(selector, timeout=5000)
                    element = await page.query_selector(selector)

                    if element and await element.is_visible():
                        await element.clear()
                        await element.fill(value)

                        self.logger.info(f"✅ 成功填写元素: {selector} = {value}")
                        return {"success": True, "action": "fill", "target": selector, "value": value}

                except Exception:
                    continue

            return {"success": False, "error": f"无法找到可填写的元素: {target}"}

        except Exception as e:
            return {"success": False, "error": f"填写操作失败: {str(e)}"}

    async def smart_select_element(self, page, target: str, value: str) -> Dict[str, Any]:
        """智能选择下拉框"""
        try:
            selectors_to_try = [target, f"[name='{target}']", f"#{target}"]

            for selector in selectors_to_try:
                try:
                    await page.wait_for_selector(selector, timeout=5000)
                    element = await page.query_selector(selector)

                    if element and await element.is_visible():
                        try:
                            await element.select_option(value=value)
                        except:
                            await element.select_option(label=value)

                        self.logger.info(f"✅ 成功选择元素: {selector} = {value}")
                        return {"success": True, "action": "select", "target": selector, "value": value}

                except Exception:
                    continue

            return {"success": False, "error": f"无法找到可选择的元素: {target}"}

        except Exception as e:
            return {"success": False, "error": f"选择操作失败: {str(e)}"}

    def generate_appropriate_value(self, field_name: str) -> str:
        """根据字段名和数字人信息生成合适的值"""
        digital_human = self.digital_human_info
        field_lower = field_name.lower()

        if "name" in field_lower or "姓名" in field_lower:
            return digital_human.get('name', '张三')
        elif "age" in field_lower or "年龄" in field_lower:
            return str(digital_human.get('age', 25))
        elif "phone" in field_lower or "电话" in field_lower:
            return "13800138000"
        elif "email" in field_lower or "邮箱" in field_lower:
            return "<EMAIL>"
        else:
            return "测试内容"

    async def generate_appropriate_select_value(self, page, target: str) -> str:
        """为下拉框生成合适的选择值"""
        try:
            element = await page.query_selector(target)
            if element:
                options = await element.query_selector_all('option')
                if len(options) > 1:  # 跳过第一个空选项
                    second_option = options[1]
                    return await second_option.get_attribute('value') or await second_option.inner_text()
            return ""
        except:
            return ""
