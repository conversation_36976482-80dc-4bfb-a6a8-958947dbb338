"""
BrowserUseAgent - 智能问卷填写代理
基于browser_use库的智能浏览器自动化代理，专门用于问卷填写任务
"""

import logging
import asyncio
from typing import Optional, Dict, Any, List

# 尝试导入browser_use组件
try:
    from browser_use.agent.service import Agent
    from browser_use.agent.views import AgentH<PERSON><PERSON>List, AgentStepInfo
    from browser_use.browser.context import BrowserContext
    from browser_use.controller.service import Controller
    browser_use_available = True
except ImportError as e:
    logging.warning(f"⚠️ browser_use组件导入失败: {e}")
    browser_use_available = False

    # 创建占位符类
    class Agent: pass
    class AgentStepInfo: pass
    class AgentHistoryList: pass
    class BrowserContext: pass
    class Controller: pass


class BrowserUseAgent(Agent):
    """智能问卷填写代理 - 基于browser_use.Agent的简化实现"""
    
    def __init__(self, task: str, llm, browser_context = None,
                 controller = None, digital_human_info: Dict = None,
                 max_actions_per_step: int = 10, use_vision: bool = True, **kwargs):
        """初始化BrowserUseAgent"""
        self.logger = logging.getLogger(f"{__name__}.BrowserUseAgent")

        if not browser_use_available:
            self.logger.error("❌ browser_use不可用")
            raise ImportError("browser_use组件不可用")

        # 确保controller是正确的类型
        if controller is None:
            controller = Controller()
            self.logger.info("🔧 使用默认Controller")

        # 调用父类初始化，使用渐进式兼容性处理
        init_success = False

        # 尝试1: 完整参数
        try:
            super().__init__(
                task=task,
                llm=llm,
                browser_context=browser_context,
                controller=controller,
                use_vision=use_vision,
                max_actions_per_step=max_actions_per_step,
                **kwargs
            )
            self.logger.info("✅ 使用完整参数初始化Agent成功")
            init_success = True
        except TypeError as e:
            self.logger.warning(f"⚠️ 完整参数初始化失败: {e}")

        # 尝试2: 不传递controller参数
        if not init_success:
            try:
                super().__init__(
                    task=task,
                    llm=llm,
                    browser_context=browser_context,
                    use_vision=use_vision,
                    max_actions_per_step=max_actions_per_step,
                    **kwargs
                )
                # 手动设置controller
                self.controller = controller
                self.logger.info("✅ 不传递controller参数初始化Agent成功")
                init_success = True
            except TypeError as e:
                self.logger.warning(f"⚠️ 不传递controller参数初始化失败: {e}")

        # 尝试3: 不传递use_vision参数
        if not init_success:
            try:
                super().__init__(
                    task=task,
                    llm=llm,
                    browser_context=browser_context,
                    max_actions_per_step=max_actions_per_step,
                    **kwargs
                )
                # 手动设置属性
                self.controller = controller
                self.use_vision = use_vision
                self.logger.info("✅ 不传递use_vision参数初始化Agent成功")
                init_success = True
            except TypeError as e:
                self.logger.warning(f"⚠️ 不传递use_vision参数初始化失败: {e}")

        # 尝试4: 最基本参数
        if not init_success:
            try:
                super().__init__(
                    task=task,
                    llm=llm,
                    browser_context=browser_context,
                    **kwargs
                )
                # 手动设置所有属性
                self.controller = controller
                self.use_vision = use_vision
                self.max_actions_per_step = max_actions_per_step
                self.logger.info("✅ 使用最基本参数初始化Agent成功")
                init_success = True
            except TypeError as e:
                self.logger.error(f"❌ 最基本参数初始化也失败: {e}")

        if not init_success:
            raise RuntimeError("无法使用任何参数组合初始化Agent")

        # 设置自定义属性
        self.digital_human_info = digital_human_info or {}
        
        # 同步浏览器上下文
        if controller and hasattr(controller, 'browser_context'):
            controller.browser_context = browser_context
            self.logger.info("✅ 已同步controller的browser_context")
        
        self.logger.info("✅ BrowserUseAgent初始化完成")

    async def _execute_questionnaire_action(self, page) -> Dict[str, Any]:
        """执行智能问卷填写动作"""
        try:
            self.logger.info("🎯 开始智能问卷分析...")

            # 获取页面中的所有可交互元素
            elements = await self._get_interactive_elements(page)

            if not elements:
                self.logger.info("✅ 没有找到可交互元素，可能已完成")
                return {
                    'action': 'done',
                    'completed': True,
                    'message': '没有找到可交互元素，任务完成'
                }

            self.logger.info(f"🔍 找到 {len(elements)} 个可交互元素")

            # 选择第一个未填写的元素进行操作
            for i, element in enumerate(elements):
                element_type = await element.get_attribute('type') or await element.tag_name
                element_text = await element.text_content() or ""

                self.logger.info(f"🎯 处理元素 {i}: {element_type} - {element_text[:50]}")

                # 根据元素类型执行相应操作
                if element_type in ['radio', 'checkbox']:
                    await self._handle_choice_element(element, i)
                    return {
                        'action': 'click',
                        'completed': False,
                        'message': f'点击了选择项 {i}'
                    }
                elif element_type in ['text', 'textarea', 'input']:
                    await self._handle_input_element(element, i)
                    return {
                        'action': 'input',
                        'completed': False,
                        'message': f'填写了输入框 {i}'
                    }
                elif element_type == 'select':
                    await self._handle_select_element(element, i)
                    return {
                        'action': 'select',
                        'completed': False,
                        'message': f'选择了下拉框 {i}'
                    }

            # 查找提交按钮
            submit_button = await self._find_submit_button(page)
            if submit_button:
                self.logger.info("🎯 找到提交按钮，准备提交")
                await submit_button.click()
                await page.wait_for_timeout(2000)  # 等待提交完成
                return {
                    'action': 'submit',
                    'completed': True,
                    'message': '已提交问卷'
                }

            # 如果没有更多操作，标记完成
            return {
                'action': 'done',
                'completed': True,
                'message': '所有元素已处理完成'
            }

        except Exception as e:
            self.logger.error(f"❌ 执行问卷动作失败: {e}")
            return {
                'action': 'error',
                'completed': False,
                'message': f'执行问卷动作失败: {str(e)}'
            }

    async def _get_interactive_elements(self, page):
        """获取页面中的可交互元素"""
        try:
            # 查找各种类型的可交互元素
            selectors = [
                'input[type="radio"]:not(:checked)',  # 未选中的单选框
                'input[type="checkbox"]:not(:checked)',  # 未选中的复选框
                'input[type="text"]:not([value])',  # 空的文本框
                'textarea:empty',  # 空的文本域
                'select:not([value])',  # 未选择的下拉框
            ]

            elements = []
            for selector in selectors:
                found_elements = await page.query_selector_all(selector)
                elements.extend(found_elements)

            return elements
        except Exception as e:
            self.logger.error(f"❌ 获取交互元素失败: {e}")
            return []

    async def _handle_choice_element(self, element, index):
        """处理选择类元素（单选框、复选框）"""
        try:
            self.logger.info(f"🎯 点击选择元素 {index}")
            await element.click()
            await element.page.wait_for_timeout(500)  # 短暂等待
        except Exception as e:
            self.logger.error(f"❌ 点击选择元素失败: {e}")

    async def _handle_input_element(self, element, index):
        """处理输入类元素（文本框、文本域）"""
        try:
            # 根据数字人信息生成合适的输入内容
            input_text = self._generate_input_text(element, index)
            self.logger.info(f"🎯 输入文本到元素 {index}: {input_text}")

            await element.click()
            await element.fill(input_text)
            await element.page.wait_for_timeout(500)  # 短暂等待
        except Exception as e:
            self.logger.error(f"❌ 输入文本失败: {e}")

    async def _handle_select_element(self, element, index):
        """处理下拉选择元素"""
        try:
            # 获取选项并选择第一个非空选项
            options = await element.query_selector_all('option')
            if len(options) > 1:  # 跳过第一个通常是空选项
                await element.select_option(index=1)
                self.logger.info(f"🎯 选择下拉框选项 {index}")
            await element.page.wait_for_timeout(500)  # 短暂等待
        except Exception as e:
            self.logger.error(f"❌ 选择下拉框失败: {e}")

    async def _find_submit_button(self, page):
        """查找提交按钮"""
        try:
            # 常见的提交按钮选择器
            submit_selectors = [
                'input[type="submit"]',
                'button[type="submit"]',
                'button:contains("提交")',
                'button:contains("完成")',
                'button:contains("下一页")',
                '.submit-btn',
                '#submit',
            ]

            for selector in submit_selectors:
                button = await page.query_selector(selector)
                if button:
                    return button

            return None
        except Exception as e:
            self.logger.error(f"❌ 查找提交按钮失败: {e}")
            return None

    def _generate_input_text(self, element, index):
        """根据数字人信息生成输入文本"""
        try:
            # 获取元素的标签或占位符文本来判断应该输入什么
            digital_info = self.digital_human_info or {}

            # 简单的文本生成逻辑
            if "姓名" in str(element) or "name" in str(element).lower():
                return digital_info.get("name", "张三")
            elif "年龄" in str(element) or "age" in str(element).lower():
                return digital_info.get("age", "25")
            elif "电话" in str(element) or "phone" in str(element).lower():
                return digital_info.get("phone", "13800138000")
            elif "邮箱" in str(element) or "email" in str(element).lower():
                return digital_info.get("email", "<EMAIL>")
            else:
                return "测试内容"
        except Exception as e:
            self.logger.error(f"❌ 生成输入文本失败: {e}")
            return "测试内容"
    
    async def run(self, max_steps: int = 100) -> Dict:
        """执行任务 - 智能问卷填写的核心实现"""
        if not browser_use_available:
            self.logger.error("❌ browser_use不可用")
            return {"success": False, "error": "browser_use不可用"}

        self.logger.info(f"🚀 开始运行智能作答流程，最大步数: {max_steps}")

        try:
            # 调用父类的run方法，这会循环调用我们重写的act方法
            result = await super().run(max_steps=max_steps)
            return result
        except Exception as e:
            self.logger.error(f"❌ 运行过程中发生错误: {e}")
            return {"success": False, "error": str(e)}

    async def act(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """执行智能动作 - 这是核心的智能操作方法"""
        try:
            self.logger.info(f"🎯 开始执行智能动作，当前步骤: {self._step_count}")

            # 检查是否有controller
            if not hasattr(self, 'controller') or self.controller is None:
                self.logger.error("❌ Controller未设置")
                return {
                    'action': 'error',
                    'completed': True,
                    'message': 'Controller未设置'
                }

            # 获取当前页面状态
            if not self.browser_context:
                self.logger.error("❌ BrowserContext未设置")
                return {
                    'action': 'error',
                    'completed': True,
                    'message': 'BrowserContext未设置'
                }

            # 获取页面信息
            page = await self.browser_context.get_current_page()
            current_url = page.url
            self.logger.info(f"🔍 当前页面: {current_url}")

            # 检查是否在问卷页面
            if "wjx.cn" not in current_url and "questionnaire" not in current_url.lower():
                self.logger.info("✅ 不在问卷页面，任务完成")
                return {
                    'action': 'done',
                    'completed': True,
                    'message': '已离开问卷页面，任务完成'
                }

            # 执行智能问卷填写
            return await self._execute_questionnaire_action(page)

        except Exception as e:
            self.logger.error(f"❌ 执行动作失败: {e}")
            return {
                'action': 'error',
                'completed': False,
                'message': f'执行动作失败: {str(e)}'
            }
    
    def get_browser_status(self) -> Optional[str]:
        """获取浏览器状态"""
        try:
            if hasattr(self, 'browser_context') and self.browser_context:
                return "running"
            return "unknown"
        except Exception as e:
            self.logger.warning(f"⚠️ 无法获取浏览器状态: {e}")
            return None
