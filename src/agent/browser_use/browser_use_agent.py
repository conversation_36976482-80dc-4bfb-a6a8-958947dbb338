# 🌍 国籍选择优先级声明：国籍选择页面必须成功处理，不允许回退
from __future__ import annotations

import asyncio
import logging
import os
import json
import random
import hashlib
import re
import time
import aiohttp
from typing import Dict, Any, Optional, List, Tuple, Callable, Awaitable, Union, cast
from types import SimpleNamespace

# 导入配置
from src.config import config

# 导入必要的browser_use组件
try:
    from browser_use.browser import Browser, BrowserConfig
    from browser_use.dom import DOMElement, DOMOperations
    from browser_use.human_input import HumanLikeInput
    from browser_use.agent.service import Agent
    from browser_use.agent.views import AgentStepInfo, AgentHistoryList
    from browser_use.utils import time_execution_async
    from browser_use.browser.context import BrowserContext
    browser_use_available = True
except ImportError as e:
    logger = logging.getLogger(__name__)
    logger.warning(f"⚠️ browser_use组件导入失败: {e}")
    browser_use_available = False
    # 创建模拟类
    class Browser: pass
    class BrowserConfig: pass
    class DOMElement: pass
    class DOMOperations: pass
    class HumanLikeInput: pass
    class Agent: pass
    class AgentStepInfo: pass
    class AgentHistoryList: pass
    class BrowserContext: pass
    def time_execution_async(func): return func

# 定义AgentHookFunc类型
AgentHookFunc = Callable[['BrowserUseAgent'], Awaitable[None]]

logger = logging.getLogger(__name__)

SKIP_LLM_API_KEY_VERIFICATION = (
    os.environ.get("SKIP_LLM_API_KEY_VERIFICATION", "false").lower()[0] in "ty1"
)

# 🧠 第二层：答题一致性保障系统
class QuestionAnswerMemoryBank:
    """🧠 问题-答案记忆库：确保前后答题一致性"""
    
    def __init__(self, digital_human_info: Dict[str, Any]):
        self.digital_human_info = digital_human_info
        self.question_memory = {}  # 问题指纹 -> 答案
        self.logical_rules = self._build_logical_consistency_rules()
        self.session_start_time = time.time()
        
    def _generate_question_fingerprint(self, question_text: str, options: Optional[List[str]] = None) -> str:
        """生成问题指纹：忽略细微差异，识别本质相同的问题"""
        try:
            # 标准化问题文本
            normalized_question = re.sub(r'\s+', ' ', question_text.lower().strip())
            
            # 提取关键词
            keywords = self._extract_key_concepts(normalized_question)
            
            # 标准化选项
            if options:
                normalized_options = sorted([opt.lower().strip() for opt in options if opt])
                options_hash = hashlib.md5('|'.join(normalized_options).encode()).hexdigest()
            else:
                options_hash = "no_options"
            
            # 生成唯一指纹
            fingerprint_data = {
                'keywords': sorted(keywords),
                'options_hash': options_hash
            }
            
            return hashlib.md5(json.dumps(fingerprint_data, sort_keys=True).encode()).hexdigest()
        except Exception as e:
            logger.warning(f"⚠️ 生成问题指纹失败: {e}")
            return hashlib.md5(question_text.encode()).hexdigest()
    
    def _extract_key_concepts(self, text: str) -> List[str]:
        """提取关键概念"""
        # 移除常见停用词
        stop_words = {'的', '是', '在', '有', '和', '或', '但', '如果', '因为', '所以', 
                     'the', 'is', 'are', 'and', 'or', 'but', 'if', 'because', 'so', 'what', 'how'}
        
        # 简单分词和关键词提取
        words = re.findall(r'\b\w+\b', text.lower())
        keywords = [w for w in words if len(w) > 2 and w not in stop_words]
        
        return keywords[:10]  # 取前10个关键词
    
    async def get_consistent_answer(self, question_text: str, options: Optional[List[str]] = None) -> Optional[str]:
        """获取一致的答案：如果之前回答过相同问题，返回相同答案"""
        try:
            fingerprint = self._generate_question_fingerprint(question_text, options or [])
            
            if fingerprint in self.question_memory:
                previous_answer = self.question_memory[fingerprint]
                logger.info(f"🧠 发现重复问题，使用一致答案: {previous_answer}")
                return previous_answer
            
            # 新问题：根据数字人信息和逻辑规则生成答案
            answer = await self._generate_logical_answer(question_text, options or [])
            
            # 记录答案
            if answer:
                self.question_memory[fingerprint] = answer
                logger.info(f"🧠 新问题记录答案: {answer}")
            
            return answer
        except Exception as e:
            logger.error(f"❌ 获取一致答案失败: {e}")
            return None
    
    async def _generate_logical_answer(self, question_text: str, options: List[str]) -> Optional[str]:
        """根据数字人信息生成逻辑一致的答案"""
        try:
            # 基于数字人信息的智能匹配
            question_lower = question_text.lower()
            
            # 年龄相关问题
            if any(keyword in question_lower for keyword in ['age', 'old', '年龄', '岁']):
                age = self.digital_human_info.get('age', 25)
                return self._select_age_appropriate_option(options, age)
            
            # 收入相关问题
            if any(keyword in question_lower for keyword in ['income', 'salary', 'money', '收入', '工资', '薪水']):
                income = self.digital_human_info.get('income', '')
                return self._select_income_appropriate_option(options, income)
            
            # 职业相关问题
            if any(keyword in question_lower for keyword in ['job', 'work', 'profession', '工作', '职业']):
                profession = self.digital_human_info.get('profession', '')
                return self._select_profession_appropriate_option(options, profession)
            
            # 地区相关问题
            if any(keyword in question_lower for keyword in ['location', 'city', 'country', '地区', '城市', '国家']):
                location = self.digital_human_info.get('location', '')
                return self._select_location_appropriate_option(options, location)
            
            # 默认选择第一个选项
            return options[0] if options else None
            
        except Exception as e:
            logger.error(f"❌ 生成逻辑答案失败: {e}")
            return options[0] if options else None
    
    def _select_age_appropriate_option(self, options: List[str], age: int) -> str:
        """选择年龄相关的合适选项"""
        for option in options:
            option_lower = option.lower()
            if age < 25 and any(keyword in option_lower for keyword in ['18-24', '20-25', 'young']):
                return option
            elif 25 <= age < 35 and any(keyword in option_lower for keyword in ['25-34', '25-35', 'adult']):
                return option
            elif 35 <= age < 45 and any(keyword in option_lower for keyword in ['35-44', '35-45', 'middle']):
                return option
        return options[0] if options else ""
    
    def _select_income_appropriate_option(self, options: List[str], income: str) -> str:
        """选择收入相关的合适选项"""
        income_lower = income.lower()
        for option in options:
            option_lower = option.lower()
            if '5000' in income_lower and any(keyword in option_lower for keyword in ['5000', '5k', 'low']):
                return option
            elif '10000' in income_lower and any(keyword in option_lower for keyword in ['10000', '10k', 'medium']):
                return option
            elif '15000' in income_lower and any(keyword in option_lower for keyword in ['15000', '15k', 'high']):
                return option
        return options[0] if options else ""
    
    def _select_profession_appropriate_option(self, options: List[str], profession: str) -> str:
        """选择职业相关的合适选项"""
        profession_lower = profession.lower()
        for option in options:
            option_lower = option.lower()
            if 'engineer' in profession_lower and any(keyword in option_lower for keyword in ['engineer', 'tech', 'it']):
                return option
            elif 'teacher' in profession_lower and any(keyword in option_lower for keyword in ['teacher', 'education']):
                return option
            elif 'doctor' in profession_lower and any(keyword in option_lower for keyword in ['doctor', 'medical']):
                return option
        return options[0] if options else ""
    
    def _select_location_appropriate_option(self, options: List[str], location: str) -> str:
        """选择地区相关的合适选项"""
        location_lower = location.lower()
        for option in options:
            option_lower = option.lower()
            if any(keyword in location_lower for keyword in ['china', '中国', 'beijing', '北京']) and \
               any(keyword in option_lower for keyword in ['china', '中国', 'beijing', '北京', 'chinese']):
                return option
        return options[0] if options else ""
    
    def _build_logical_consistency_rules(self) -> Dict[str, Any]:
        """构建逻辑一致性规则"""
        return {
            'age_based_preferences': self._get_age_based_rules(),
            'income_based_choices': self._get_income_based_rules(),
            'profession_based_answers': self._get_profession_based_rules(),
            'location_based_preferences': self._get_location_based_rules()
        }
    
    def _get_age_based_rules(self) -> Dict[str, Any]:
        """获取基于年龄的规则"""
        age = self.digital_human_info.get('age', 25)
        if age < 25:
            return {'preferences': ['technology', 'social_media', 'gaming'], 'lifestyle': 'active'}
        elif age < 35:
            return {'preferences': ['career', 'family', 'travel'], 'lifestyle': 'balanced'}
        else:
            return {'preferences': ['stability', 'health', 'family'], 'lifestyle': 'conservative'}
    
    def _get_income_based_rules(self) -> Dict[str, Any]:
        """获取基于收入的规则"""
        income = self.digital_human_info.get('income', '')
        if '5000' in income or 'low' in income.lower():
            return {'spending': 'conservative', 'priorities': ['basic_needs', 'savings']}
        elif '15000' in income or 'high' in income.lower():
            return {'spending': 'liberal', 'priorities': ['luxury', 'investment']}
        else:
            return {'spending': 'moderate', 'priorities': ['comfort', 'security']}
    
    def _get_profession_based_rules(self) -> Dict[str, Any]:
        """获取基于职业的规则"""
        profession = self.digital_human_info.get('profession', '').lower()
        if 'engineer' in profession or 'tech' in profession:
            return {'interests': ['technology', 'innovation'], 'work_style': 'analytical'}
        elif 'teacher' in profession:
            return {'interests': ['education', 'books'], 'work_style': 'nurturing'}
        else:
            return {'interests': ['general'], 'work_style': 'balanced'}
    
    def _get_location_based_rules(self) -> Dict[str, Any]:
        """获取基于地区的规则"""
        location = self.digital_human_info.get('location', '').lower()
        if any(keyword in location for keyword in ['china', '中国', 'beijing', '北京']):
            return {'culture': 'chinese', 'language': 'chinese', 'preferences': ['chinese_food', 'local_brands']}
        else:
            return {'culture': 'international', 'language': 'english', 'preferences': ['international']}
    
    def get_answered_questions_summary(self) -> str:
        """获取已回答问题的摘要"""
        if not self.question_memory:
            return "尚未回答任何问题"
        
        summary = f"已回答 {len(self.question_memory)} 个问题："
        for i, (fingerprint, answer) in enumerate(list(self.question_memory.items())[:5]):
            summary += f"\n{i+1}. 答案: {answer}"
        
        if len(self.question_memory) > 5:
            summary += f"\n... 还有 {len(self.question_memory) - 5} 个问题"
        
        return summary
    
    async def save_session_summary(self):
        """保存会话摘要"""
        try:
            summary = {
                'digital_human': self.digital_human_info,
                'questions_answered': len(self.question_memory),
                'session_duration': time.time() - self.session_start_time,
                'answers': list(self.question_memory.values())[:10]  # 保存前10个答案
            }
            
            with open(f'questionnaire_session_{int(self.session_start_time)}.json', 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
                
            logger.info(f"✅ 会话摘要已保存: {len(self.question_memory)} 个问题")
        except Exception as e:
            logger.error(f"❌ 保存会话摘要失败: {e}")

# 🔧 第三层：AdsPower资源智能管理
class AdsPowerResourceManager:
    """🔧 AdsPower资源智能管理：基于官方API文档实现"""
    
    def __init__(self, profile_id: str, adspower_host: str = "http://local.adspower.net:50325"):
        self.profile_id = profile_id
        self.adspower_host = adspower_host
        self.browser_status = "unknown"
        self.monitoring_active = True
        self.agent_reference = None
        
    def set_agent_reference(self, agent):
        """设置Agent引用"""
        self.agent_reference = agent
        
    async def start_monitoring(self):
        """🔧 【修复】启动浏览器状态监控 - 使用增强智能判断"""
        logger.info("🔧 启动增强型浏览器状态监控...")
        asyncio.create_task(self._monitor_browser_lifecycle())
    
    async def _monitor_browser_lifecycle(self):
        """持续监控浏览器生命周期"""
        while self.monitoring_active:
            try:
                # 检查浏览器状态
                current_status = await self._check_browser_status()
                
                if current_status != self.browser_status:
                    logger.info(f"🔍 浏览器状态变化: {self.browser_status} -> {current_status}")
                    self.browser_status = current_status
                    
                    # 如果检测到浏览器被关闭
                    if current_status == "Inactive":
                        logger.warning("🚨 检测到浏览器被手动关闭，开始资源清理...")
                        await self._handle_browser_closed()
                        break
                
                await asyncio.sleep(5)  # 每5秒检查一次
                
            except Exception as e:
                logger.error(f"❌ 浏览器状态监控异常: {e}")
                await asyncio.sleep(10)
    
    async def _check_browser_status(self) -> str:
        """🔧 【关键修复】检查浏览器启动状态 - 智能判断真正关闭vs临时连接问题"""
        try:
            url = f"{self.adspower_host}/api/v1/browser/active"
            params = {"user_id": self.profile_id}
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=5)) as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("code") == 0:
                            status = data.get("data", {}).get("status", "Unknown")
                            
                            # 🔍 【新增】：智能状态验证
                            if status == "Inactive":
                                # 进行二次确认，避免误判
                                verified_status = await self._verify_browser_truly_closed()
                                if verified_status == "actually_active":
                                    logger.info("✅ 二次验证：浏览器实际仍在运行，忽略Inactive状态")
                                    return "Active"  # 修正状态
                                else:
                                    logger.warning(f"🚨 确认浏览器已关闭: {verified_status}")
                                    return "Inactive"
                            
                            return status
                    return "Unknown"
                    
        except Exception as e:
            logger.warning(f"⚠️ 无法获取浏览器状态: {e}")
            # 🔧 【修复】：API失败时不应判断为关闭
            return "Unknown"
    
    async def _verify_browser_truly_closed(self) -> str:
        """🔍 【新增】二次验证浏览器是否真正关闭"""
        try:
            # 方法1：尝试通过WebSocket连接测试
            if hasattr(self, 'agent_reference') and self.agent_reference:
                try:
                    # 尝试获取当前页面，如果成功说明浏览器还在
                    browser_context = getattr(self.agent_reference, 'browser_context', None)
                    if browser_context:
                        page = await browser_context.get_current_page()
                        current_url = page.url
                        logger.info(f"🔍 验证成功：浏览器仍在运行，当前页面: {current_url[:50]}...")
                        return "actually_active"
                except Exception as browser_test_error:
                    logger.debug(f"🔍 浏览器连接测试失败: {browser_test_error}")
            
            # 方法2：再次调用AdsPower API确认
            await asyncio.sleep(2)  # 短暂等待
            try:
                url = f"{self.adspower_host}/api/v1/browser/active"
                params = {"user_id": self.profile_id}
                
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=3)) as session:
                    async with session.get(url, params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            if data.get("code") == 0:
                                status = data.get("data", {}).get("status", "Unknown")
                                if status == "Active":
                                    return "actually_active"
            except Exception as api_retry_error:
                logger.debug(f"🔍 AdsPower API重试失败: {api_retry_error}")
            
            # 方法3：检查进程是否存在（Linux/Mac）
            try:
                import psutil
                for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        if proc.info['cmdline'] and any(self.profile_id in str(arg) for arg in proc.info['cmdline']):
                            logger.info(f"🔍 发现相关Chrome进程: {proc.info['pid']}")
                            return "actually_active"
            except Exception as process_check_error:
                logger.debug(f"🔍 进程检查失败: {process_check_error}")
            
            # 所有验证都失败，确认为真正关闭
            return "truly_closed"
            
        except Exception as e:
            logger.warning(f"⚠️ 浏览器状态二次验证失败: {e}")
            return "verification_failed"
    
    async def _handle_browser_closed(self):
        """🔧 【增强】处理浏览器被关闭的情况 - 强制完整资源清理"""
        try:
            logger.info("🔧 开始智能资源清理流程...")
            
            # 1. 【新增】：最后一次挽救尝试
            logger.info("🚑 执行最后挽救尝试...")
            salvage_result = await self._attempt_browser_salvage()
            
            if salvage_result == "salvaged":
                logger.info("🎉 浏览器挽救成功，继续执行！")
                return  # 不需要清理，继续执行
            
            # 2. 确认无法挽救，开始正式清理
            logger.warning("💔 确认浏览器无法挽救，开始资源清理...")
            
            # 停止Agent执行
            if self.agent_reference:
                self.agent_reference.state.stopped = True
                logger.info("✅ Agent执行已停止")
            
            # 🔥 【关键修复】：强制执行完整的两步AdsPower资源清理
            logger.info("🚀 开始强制完整AdsPower资源清理（两步骤流程）...")
            
            # 第一步：停止浏览器实例
            await self._force_close_browser_profile()
            
            # 第二步：删除配置文件（关键步骤 - 从AdsPower列表中完全移除）
            await self._force_delete_browser_profile()
            
            # 清理本地资源
            await self._cleanup_local_resources()
            
            # 发送清理完成通知
            logger.info("🎉 浏览器关闭后资源清理完成")
            
        except Exception as e:
            logger.error(f"❌ 资源清理失败: {e}")
    
    async def _force_close_browser_profile(self):
        """强制关闭浏览器配置文件 - 基于AdsPower API"""
        try:
            # 使用AdsPower关闭浏览器API
            url = f"{self.adspower_host}/api/v1/browser/stop"
            data = {"user_id": self.profile_id}
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                async with session.post(url, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get("code") == 0:
                            logger.info("✅ AdsPower浏览器配置文件已关闭")
                        else:
                            logger.warning(f"⚠️ AdsPower关闭响应: {result}")
                    else:
                        logger.warning(f"⚠️ AdsPower关闭请求失败: {response.status}")
                        
        except Exception as e:
            logger.error(f"❌ 强制关闭AdsPower配置文件失败: {e}")
    
    async def _cleanup_local_resources(self):
        """清理本地资源"""
        try:
            # 停止监控
            self.monitoring_active = False
            
            # 清理其他资源
            logger.info("✅ 本地资源清理完成")
            
        except Exception as e:
            logger.error(f"❌ 本地资源清理失败: {e}")
    
    async def cleanup_all_resources(self):
        """清理所有资源"""
        await self._handle_browser_closed()

    async def _attempt_browser_salvage(self) -> str:
        """🚑 【新增】：尝试挽救浏览器连接"""
        try:
            logger.info("🚑 尝试挽救浏览器连接...")
            
            # 尝试1：重新连接现有浏览器
            try:
                if hasattr(self, 'agent_reference') and self.agent_reference:
                    browser_context = getattr(self.agent_reference, 'browser_context', None)
                    if browser_context:
                        # 尝试刷新连接
                        page = await browser_context.get_current_page()
                        await page.reload()
                        logger.info("✅ 浏览器连接挽救成功")
                        return "salvaged"
            except Exception as salvage1_error:
                logger.debug(f"🚑 挽救尝试1失败: {salvage1_error}")
            
            # 尝试2：重新启动AdsPower浏览器
            try:
                logger.info("🚑 尝试重新启动AdsPower浏览器...")
                start_url = f"{self.adspower_host}/api/v1/browser/start"
                start_data = {"user_id": self.profile_id}
                
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
                    async with session.post(start_url, json=start_data) as response:
                        if response.status == 200:
                            result = await response.json()
                            if result.get("code") == 0:
                                logger.info("✅ AdsPower浏览器重新启动成功")
                                await asyncio.sleep(5)  # 等待启动完成
                                return "salvaged"
            except Exception as salvage2_error:
                logger.debug(f"🚑 挽救尝试2失败: {salvage2_error}")
            
            return "failed"
            
        except Exception as e:
            logger.warning(f"⚠️ 浏览器挽救过程失败: {e}")
            return "failed"

    async def _force_delete_browser_profile(self):
        """🗑️ 【新增】强制删除AdsPower配置文件 - 从应用列表中完全移除"""
        try:
            logger.info("🗑️ 开始删除AdsPower配置文件（从应用列表中完全移除）...")
            
            # 等待浏览器停止完成
            await asyncio.sleep(2)
            
            # 使用AdsPower删除配置文件API
            url = f"{self.adspower_host}/api/v1/user/delete"
            data = {"user_ids": [self.profile_id]}
            
            logger.info(f"🗑️ 调用AdsPower删除API: {url}")
            logger.info(f"🗑️ 删除配置文件ID: {self.profile_id}")
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=15)) as session:
                async with session.post(url, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get("code") == 0:
                            logger.info("✅ AdsPower配置文件删除成功")
                            logger.info("🎯 配置文件已从AdsPower应用列表中完全移除")
                            logger.info("💾 浏览器额度已释放，可创建新的配置文件")
                        else:
                            logger.warning(f"⚠️ AdsPower配置文件删除失败: {result.get('msg', '未知错误')}")
                    else:
                        logger.warning(f"⚠️ AdsPower删除请求失败: {response.status}")
                        
        except Exception as e:
            logger.error(f"❌ 强制删除AdsPower配置文件失败: {e}")
            logger.error("❌ 配置文件可能仍占用AdsPower浏览器额度")

"""浏览器使用代理模块"""
import json
import time
import logging
import asyncio
from types import SimpleNamespace
from typing import Dict, Any, Optional, List, Tuple, Union

class BrowserUseAgent:
    """浏览器使用代理"""
    
    def __init__(self, task: Optional[str] = None, llm: Any = None, browser_config: Optional[Dict] = None, 
                 context_config: Optional[Dict] = None, existing_browser: Any = None, 
                 controller: Any = None, browser_context: Any = None, use_vision: bool = True, 
                 max_actions_per_step: int = 15, tool_calling_method: str = 'auto', 
                 digital_human_info: Optional[Dict] = None, questionnaire_mode: bool = True,
                 never_give_up_mode: bool = True):
        """初始化浏览器使用代理
        
        Args:
            task: 任务描述
            llm: 语言模型实例
            browser_config: 浏览器配置
            context_config: 上下文配置
            existing_browser: 现有浏览器实例
            controller: 控制器实例
            browser_context: 浏览器上下文
            use_vision: 是否使用视觉功能
            max_actions_per_step: 每步最大操作数
            tool_calling_method: 工具调用方法
            digital_human_info: 数字人信息
            questionnaire_mode: 是否为问卷模式
            never_give_up_mode: 是否为永不放弃模式
        """
        # 基本属性
        self.task = task
        self.llm = llm
        self.controller = controller
        self.browser_context = browser_context
        self.digital_human_info = digital_human_info or {}
        self.existing_browser = existing_browser
        
        # 从配置加载设置
        browser_config = config.get('browser', {})
        self.use_vision = browser_config.get('use_vision', use_vision)
        self.max_actions_per_step = browser_config.get('max_actions_per_step', max_actions_per_step)
        self.questionnaire_mode = browser_config.get('questionnaire_mode', questionnaire_mode)
        self.never_give_up_mode = browser_config.get('never_give_up_mode', never_give_up_mode)
        
        # 初始化日志
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # 初始化状态
        self.state = SimpleNamespace()
        self.state.consecutive_failures = 0
        self.state.max_failures = browser_config.get('max_failures', 20)
        self.state.last_action_time = time.time()
        
        # 问卷特定状态
        if questionnaire_mode:
            self.state.questions_answered = 0
            self.state.pages_navigated = 0
            self.state.submit_attempts = 0
            self.state.loading_wait_time = 0
            self.state.last_question_time = time.time()
        
        # 初始化问答记忆库
        self.answer_memory = QuestionAnswerMemoryBank(self.digital_human_info)
        
        # 初始化浏览器
        self.browser = existing_browser
        if not self.browser and browser_config:
            self.browser = Browser(browser_config)
            
        # 初始化资源管理器
        if self.browser and hasattr(self.browser, 'profile_id'):
            self.resource_manager = AdsPowerResourceManager(
                self.browser.profile_id,
                config.get('adspower', {}).get('api_base', 'http://local.adspower.net:50325')
            )
            self.resource_manager.set_agent_reference(self)
            
        # 设置工具调用方法
        self.tool_calling_method = tool_calling_method
        
        logger.info("✅ BrowserUseAgent初始化完成")
        
    async def multi_act(self, actions: List[Dict], check_for_new_elements: bool = True) -> Dict:
        """执行多个操作"""
        try:
            results = []
            for action in actions:
                result = await self._execute_action(action)
                results.append(result)
                if not result.get("success"):
                    break
                    
            return {
                "success": all(r.get("success", False) for r in results),
                "results": results
            }
            
        except Exception as e:
            self.logger.error(f"❌ 执行多个操作失败: {e}")
            return {"success": False, "error": str(e)}
            
    async def create_browser_session(self):
        """创建浏览器会话 - 使用已存在的AdsPower浏览器"""
        try:
            if self.existing_browser:
                self.logger.info("✅ 使用已存在的AdsPower浏览器实例")
                return self.existing_browser
            else:
                raise Exception("必须提供AdsPower浏览器实例，不允许创建新的浏览器实例")
        except Exception as e:
            self.logger.error(f"❌ 创建浏览器会话失败: {e}")
            raise
            
    async def execute_questionnaire(self, url: str, prompt: Optional[str] = None):
        """执行问卷任务 - 只在AdsPower浏览器中执行"""
        try:
            # 使用已存在的浏览器实例
            browser = await self.create_browser_session()
            if not browser:
                raise Exception("无法获取AdsPower浏览器实例")
                
            # 使用浏览器实例执行任务
            result = await self._execute_in_adspower(browser, url, prompt)
            return result
            
        except Exception as e:
            self.logger.error(f"❌ 执行问卷任务失败: {e}")
            return {"success": False, "error": str(e)}
            
    async def _execute_in_adspower(self, browser: Any, url: str, prompt: Optional[str] = None):
        """在AdsPower浏览器中执行问卷任务"""
        try:
            # 导航到问卷页面
            await browser.goto(url)
            self.logger.info(f"✅ 成功导航到问卷页面: {url}")
            
            # 等待页面加载完成
            await browser.wait_for_load_state("networkidle")
            self.logger.info("✅ 页面加载完成")
            
            # 获取页面标题
            title = await browser.title()
            self.logger.info(f"📄 问卷标题: {title}")
            
            # 使用控制器执行问卷填写
            if self.controller:
                result = await self.controller.execute_questionnaire(browser, prompt)
                return result
            else:
                raise Exception("未提供问卷控制器")
                
        except Exception as e:
            self.logger.error(f"❌ 执行问卷任务失败: {e}")
            return {"success": False, "error": str(e)}
            
    async def run(self, max_steps: int = 100):
        """运行Agent任务"""
        try:
            if not self.task:
                raise Exception("未提供任务描述")
                
            # 创建浏览器会话
            browser = await self.create_browser_session()
            if not browser:
                raise Exception("无法创建浏览器会话")
                
            # 执行任务
            for step in range(max_steps):
                if self.state.consecutive_failures >= self.state.max_failures:
                    self.logger.error(f"❌ 连续失败次数过多 ({self.state.consecutive_failures})")
                    break
                    
                try:
                    # 使用LLM生成下一步操作
                    action = await self._get_next_action()
                    if not action:
                        break
                        
                    # 执行操作
                    result = await self._execute_action(action)
                    if result.get("success"):
                        self.state.consecutive_failures = 0
                    else:
                        self.state.consecutive_failures += 1
                        
                except Exception as e:
                    self.logger.error(f"❌ 步骤 {step} 执行失败: {e}")
                    self.state.consecutive_failures += 1
                    
            return {"success": True, "message": "任务执行完成"}
            
        except Exception as e:
            self.logger.error(f"❌ 任务执行失败: {e}")
            return {"success": False, "error": str(e)}
            
    async def _get_next_action(self) -> Optional[Dict]:
        """使用LLM生成下一步操作"""
        if not self.llm:
            raise Exception("未提供LLM实例")
            
        try:
            # 获取当前状态
            state = await self._get_current_state()
            
            # 生成提示词
            prompt = self._create_action_prompt(state)
            
            # 🔥 修复：兼容不同的LLM调用方法
            response = None
            try:
                # 尝试使用ainvoke方法（Gemini等）
                if hasattr(self.llm, 'ainvoke'):
                    response = await self.llm.ainvoke(prompt)
                # 尝试使用agenerate方法
                elif hasattr(self.llm, 'agenerate'):
                    response = await self.llm.agenerate([prompt])
                # 尝试使用acall方法
                elif hasattr(self.llm, 'acall'):
                    response = await self.llm.acall(prompt)
                else:
                    raise Exception("LLM不支持异步调用方法")
            except Exception as llm_error:
                self.logger.warning(f"⚠️ 异步LLM调用失败，尝试同步调用: {llm_error}")
                # 尝试同步调用
                if hasattr(self.llm, 'invoke'):
                    response = self.llm.invoke(prompt)
                elif hasattr(self.llm, 'generate'):
                    response = self.llm.generate([prompt])
                else:
                    raise Exception("LLM不支持任何调用方法")
            
            # 解析响应
            action = self._parse_llm_response(response)
            return action
            
        except Exception as e:
            self.logger.error(f"❌ 生成下一步操作失败: {e}")
            return None
            
    async def _get_current_state(self) -> Dict:
        """获取当前状态"""
        try:
            if not self.browser_context:
                return {}
                
            # 获取当前页面信息
            page = self.browser_context.current_page
            if not page:
                return {}
                
            # 🔥 修复：兼容不同的页面属性访问方式
            state = {
                "url": page.url if hasattr(page, 'url') and not callable(page.url) else (await page.url() if callable(page.url) else str(page)),
                "title": page.title if hasattr(page, 'title') and not callable(page.title) else (await page.title() if hasattr(page, 'title') and callable(page.title) else ""),
                "content": await page.content() if hasattr(page, 'content') and callable(page.content) else "",
                "viewport": await page.viewport_size() if hasattr(page, 'viewport_size') and callable(page.viewport_size) else {"width": 1280, "height": 1100},
                "cookies": await page.context.cookies() if hasattr(page, 'context') and hasattr(page.context, 'cookies') else [],
                "timestamp": time.time()
            }
            
            return state
            
        except Exception as e:
            self.logger.error(f"❌ 获取当前状态失败: {e}")
            return {}
            
    def _create_action_prompt(self, state: Dict) -> str:
        """创建动作生成提示词"""
        prompt = f"""
        当前任务: {self.task}
        
        当前状态:
        - URL: {state.get('url')}
        - 标题: {state.get('title')}
        - 视口大小: {state.get('viewport')}
        - 时间戳: {state.get('timestamp')}
        
        请根据以上信息，生成下一步具体操作。
        操作应该是可执行的浏览器命令，如：
        - 点击特定元素
        - 输入文本
        - 滚动页面
        - 等待加载
        等。
        
        请以JSON格式返回操作指令。
        """
        return prompt
        
    def _parse_llm_response(self, response: Any) -> Optional[Dict]:
        """解析LLM响应"""
        try:
            # 🔥 修复：兼容不同的LLM响应格式
            text = None

            # 尝试不同的响应格式
            if hasattr(response, 'content'):
                text = response.content
            elif hasattr(response, 'generations') and response.generations:
                if hasattr(response.generations[0][0], 'text'):
                    text = response.generations[0][0].text
                elif hasattr(response.generations[0][0], 'content'):
                    text = response.generations[0][0].content
            elif isinstance(response, str):
                text = response
            elif hasattr(response, 'text'):
                text = response.text
            else:
                # 尝试直接转换为字符串
                text = str(response)

            if not text or text.strip() == "":
                self.logger.warning("⚠️ LLM返回空响应，使用默认操作")
                return {
                    "type": "wait",
                    "params": {"duration": 1},
                    "reasoning": "LLM返回空响应，等待1秒"
                }

            # 🔥 修复：清理响应文本，移除可能的前缀/后缀
            text = text.strip()

            # 尝试提取JSON部分（如果响应包含其他文本）
            if '{' in text and '}' in text:
                start = text.find('{')
                end = text.rfind('}') + 1
                text = text[start:end]

            # 解析JSON
            try:
                action = json.loads(text)
            except json.JSONDecodeError as json_error:
                self.logger.warning(f"⚠️ JSON解析失败: {json_error}，原始文本: {text[:100]}...")
                # 返回默认操作
                return {
                    "type": "wait",
                    "params": {"duration": 1},
                    "reasoning": f"JSON解析失败: {json_error}"
                }

            # 验证必要字段
            required_fields = ["type", "params"]
            if not all(field in action for field in required_fields):
                self.logger.warning(f"⚠️ 响应缺少必要字段: {action}")
                return {
                    "type": "wait",
                    "params": {"duration": 1},
                    "reasoning": "响应格式不正确"
                }

            return action
            
        except Exception as e:
            self.logger.error(f"❌ 解析LLM响应失败: {e}")
            return None
            
    async def _execute_action(self, action: Dict) -> Dict:
        """执行操作"""
        try:
            if not self.controller:
                raise Exception("未提供控制器")
                
            # 更新最后操作时间
            self.state.last_action_time = time.time()
            
            # 执行操作
            result = await self.controller.execute_action(action)
            
            # 记录操作结果
            if result.get("success"):
                self.logger.info(f"✅ 操作执行成功: {action['type']}")
            else:
                self.logger.warning(f"⚠️ 操作执行失败: {action['type']}")
                
            return result
            
        except Exception as e:
            self.logger.error(f"❌ 执行操作失败: {e}")
            return {"success": False, "error": str(e)} 