"""
智能问卷填写Agent - 完全兼容browser-use的智能代理
基于browser_use框架，集成LLM推理和问卷专用逻辑
"""

import logging
import asyncio
import json
import time
from typing import Optional, Dict, Any, List, Callable, Awaitable
from datetime import datetime

# 导入browser_use组件
try:
    from browser_use.agent.views import Agent<PERSON><PERSON>oryList, AgentStepInfo, AgentOutput
    from browser_use.browser.context import BrowserContext
    from browser_use.browser.views import BrowserState
    from browser_use.browser.browser import Browser
    browser_use_available = True
except ImportError as e:
    logging.warning(f"⚠️ browser_use组件导入失败: {e}")
    browser_use_available = False

    # 创建占位符类
    class AgentHistoryList:
        def __init__(self): self.history = []
        def total_duration_seconds(self): return 0
        def total_input_tokens(self): return 0
        def final_result(self): return None
        def errors(self): return []

    class AgentStepInfo: pass
    class AgentOutput: pass
    class BrowserState: pass
    class BrowserContext: pass
    class Browser: pass


class IntelligentQuestionnaireAgent:
    """智能问卷填写Agent - 专门针对问卷优化的智能代理"""
    
    def __init__(self, task: str, llm, browser_context: BrowserContext, 
                 controller=None, digital_human_info: Dict = None, **kwargs):
        """初始化智能问卷Agent"""
        self.logger = logging.getLogger(f"{__name__}.IntelligentQuestionnaireAgent")
        
        if not browser_use_available:
            self.logger.error("❌ browser_use不可用")
            raise ImportError("browser_use组件不可用")
        
        # 调用父类初始化
        super().__init__(
            task=task,
            llm=llm,
            browser_context=browser_context
        )
        
        # 设置问卷专用属性
        self.controller = controller
        self.digital_human_info = digital_human_info or {}
        self.llm = llm
        self.questionnaire_state = {
            "current_question": 0,
            "total_questions": 0,
            "answered_questions": [],
            "errors": []
        }
        
        self.logger.info("✅ 智能问卷Agent初始化完成")
    
    async def run(self, max_steps: int = 100) -> Dict[str, Any]:
        """执行问卷填写任务"""
        self.logger.info(f"🚀 开始智能问卷填写，最大步数: {max_steps}")
        
        try:
            # 分析页面结构
            page_analysis = await self.analyze_questionnaire_page()
            if not page_analysis["success"]:
                return {"success": False, "error": "页面分析失败"}
            
            # 执行问卷填写
            result = await self.execute_questionnaire_filling(max_steps)
            return result
            
        except Exception as e:
            self.logger.error(f"❌ 问卷填写过程中发生错误: {e}")
            return {
                "success": False,
                "error": str(e),
                "steps": self._step_count,
                "questionnaire_state": self.questionnaire_state
            }
    
    async def analyze_questionnaire_page(self) -> Dict[str, Any]:
        """深度分析问卷页面结构"""
        try:
            self.logger.info("🔍 开始深度分析问卷页面结构...")

            page = await self.browser_context.get_current_page()

            # 获取页面基本信息
            title = await page.title()
            url = page.url

            # 等待页面完全加载
            await page.wait_for_load_state('networkidle')

            # 深度分析问卷元素
            questionnaire_analysis = await self.deep_analyze_questionnaire_elements(page)

            self.questionnaire_state["total_questions"] = questionnaire_analysis["total_questions"]
            self.questionnaire_state["element_map"] = questionnaire_analysis["element_map"]

            self.logger.info(f"✅ 深度页面分析完成: 标题={title}, 问题数={questionnaire_analysis['total_questions']}, 可交互元素={questionnaire_analysis['interactive_count']}")

            return {
                "success": True,
                "title": title,
                "url": url,
                "questionnaire_analysis": questionnaire_analysis
            }

        except Exception as e:
            self.logger.error(f"❌ 页面分析失败: {e}")
            return {"success": False, "error": str(e)}

    async def deep_analyze_questionnaire_elements(self, page) -> Dict[str, Any]:
        """深度分析问卷元素结构"""
        try:
            # 查找所有可能的问卷容器
            question_containers = await page.query_selector_all('.field, .div_question, [class*="question"], [class*="field"]')

            # 查找所有输入元素
            all_inputs = await page.query_selector_all('input, select, textarea')

            # 查找单选按钮组
            radio_groups = await page.query_selector_all('input[type="radio"]')

            # 查找复选框
            checkboxes = await page.query_selector_all('input[type="checkbox"]')

            # 查找下拉框
            selects = await page.query_selector_all('select')

            # 查找文本输入框
            text_inputs = await page.query_selector_all('input[type="text"], textarea')

            # 构建元素映射
            element_map = {
                "radio_groups": [],
                "checkboxes": [],
                "selects": [],
                "text_inputs": [],
                "all_interactive": []
            }

            # 分析单选按钮组
            for radio in radio_groups:
                try:
                    name = await radio.get_attribute('name')
                    value = await radio.get_attribute('value')
                    is_visible = await radio.is_visible()
                    if name and is_visible:
                        element_map["radio_groups"].append({
                            "name": name,
                            "value": value,
                            "selector": f'input[name="{name}"][value="{value}"]',
                            "type": "radio"
                        })
                except:
                    continue

            # 分析下拉框
            for select in selects:
                try:
                    name = await select.get_attribute('name') or await select.get_attribute('id')
                    is_visible = await select.is_visible()
                    if name and is_visible:
                        # 获取选项
                        options = await select.query_selector_all('option')
                        option_values = []
                        for option in options:
                            value = await option.get_attribute('value')
                            text = await option.inner_text()
                            if value and text:
                                option_values.append({"value": value, "text": text})

                        element_map["selects"].append({
                            "name": name,
                            "selector": f'select[name="{name}"]' if await select.get_attribute('name') else f'#{name}',
                            "options": option_values,
                            "type": "select"
                        })
                except:
                    continue

            # 分析文本输入框
            for text_input in text_inputs:
                try:
                    name = await text_input.get_attribute('name') or await text_input.get_attribute('id')
                    is_visible = await text_input.is_visible()
                    if name and is_visible:
                        element_map["text_inputs"].append({
                            "name": name,
                            "selector": f'input[name="{name}"]' if await text_input.get_attribute('name') else f'#{name}',
                            "type": "text"
                        })
                except:
                    continue

            # 合并所有可交互元素
            element_map["all_interactive"] = (
                element_map["radio_groups"] +
                element_map["selects"] +
                element_map["text_inputs"]
            )

            return {
                "total_questions": len(question_containers),
                "interactive_count": len(element_map["all_interactive"]),
                "element_map": element_map,
                "radio_count": len(element_map["radio_groups"]),
                "select_count": len(element_map["selects"]),
                "text_count": len(element_map["text_inputs"])
            }

        except Exception as e:
            self.logger.error(f"❌ 深度元素分析失败: {e}")
            return {
                "total_questions": 0,
                "interactive_count": 0,
                "element_map": {"all_interactive": []},
                "error": str(e)
            }
    
    async def execute_questionnaire_filling(self, max_steps: int) -> Dict[str, Any]:
        """执行问卷填写逻辑"""
        self.logger.info("🎯 开始执行智能问卷填写...")
        
        filled_count = 0
        
        for step in range(max_steps):
            self._step_count = step + 1
            
            try:
                # 观察当前状态
                observation = await self.observe_questionnaire_state()
                
                # 使用LLM分析并决定动作
                action_decision = await self.llm_analyze_and_decide(observation)
                
                if action_decision.get("completed", False):
                    self.logger.info("✅ 问卷填写完成")
                    break
                
                # 执行动作
                action_result = await self.execute_questionnaire_action(action_decision)
                
                if action_result.get("success", False):
                    filled_count += 1
                    self.questionnaire_state["answered_questions"].append({
                        "step": self._step_count,
                        "action": action_decision,
                        "result": action_result
                    })
                
                # 短暂等待
                await asyncio.sleep(1)
                
            except Exception as e:
                self.logger.error(f"❌ 步骤 {self._step_count} 执行失败: {e}")
                self.questionnaire_state["errors"].append({
                    "step": self._step_count,
                    "error": str(e)
                })
        
        return {
            "success": filled_count > 0,
            "steps": self._step_count,
            "filled_questions": filled_count,
            "questionnaire_state": self.questionnaire_state,
            "message": f"完成 {filled_count} 个问题的填写"
        }
    
    async def observe_questionnaire_state(self) -> Dict[str, Any]:
        """智能观察当前问卷状态"""
        try:
            page = await self.browser_context.get_current_page()

            # 获取当前可交互的元素
            current_interactive = await self.get_current_interactive_elements(page)

            # 分析填写进度
            progress_analysis = await self.analyze_filling_progress(page)

            return {
                "url": page.url,
                "title": await page.title(),
                "step": self._step_count,
                "digital_human": self.digital_human_info,
                "current_interactive": current_interactive,
                "progress": progress_analysis,
                "element_map": self.questionnaire_state.get("element_map", {}),
                "next_action_candidates": await self.identify_next_action_candidates(page)
            }

        except Exception as e:
            self.logger.error(f"❌ 状态观察失败: {e}")
            return {"error": str(e), "step": self._step_count}

    async def get_current_interactive_elements(self, page) -> List[Dict[str, Any]]:
        """获取当前可交互的元素"""
        try:
            interactive_elements = []

            # 查找可见的未填写单选按钮
            radios = await page.query_selector_all('input[type="radio"]:visible')
            for radio in radios:
                try:
                    name = await radio.get_attribute('name')
                    value = await radio.get_attribute('value')
                    is_checked = await radio.is_checked()
                    if name and not is_checked:
                        interactive_elements.append({
                            "type": "radio",
                            "name": name,
                            "value": value,
                            "selector": f'input[name="{name}"][value="{value}"]',
                            "status": "unchecked"
                        })
                except:
                    continue

            # 查找可见的未选择下拉框
            selects = await page.query_selector_all('select:visible')
            for select in selects:
                try:
                    name = await select.get_attribute('name') or await select.get_attribute('id')
                    selected_value = await select.input_value()
                    if name and (not selected_value or selected_value == ""):
                        interactive_elements.append({
                            "type": "select",
                            "name": name,
                            "selector": f'select[name="{name}"]' if await select.get_attribute('name') else f'#{name}',
                            "status": "unselected"
                        })
                except:
                    continue

            # 查找可见的空文本框
            text_inputs = await page.query_selector_all('input[type="text"]:visible, textarea:visible')
            for text_input in text_inputs:
                try:
                    name = await text_input.get_attribute('name') or await text_input.get_attribute('id')
                    value = await text_input.input_value()
                    if name and (not value or value.strip() == ""):
                        interactive_elements.append({
                            "type": "text",
                            "name": name,
                            "selector": f'input[name="{name}"]' if await text_input.get_attribute('name') else f'#{name}',
                            "status": "empty"
                        })
                except:
                    continue

            return interactive_elements[:5]  # 返回前5个最相关的元素

        except Exception as e:
            self.logger.error(f"❌ 获取交互元素失败: {e}")
            return []

    async def analyze_filling_progress(self, page) -> Dict[str, Any]:
        """分析填写进度"""
        try:
            total_elements = len(self.questionnaire_state.get("element_map", {}).get("all_interactive", []))
            filled_count = 0

            # 检查已填写的单选按钮
            checked_radios = await page.query_selector_all('input[type="radio"]:checked')
            radio_groups = set()
            for radio in checked_radios:
                name = await radio.get_attribute('name')
                if name:
                    radio_groups.add(name)
            filled_count += len(radio_groups)

            # 检查已选择的下拉框
            selects = await page.query_selector_all('select')
            for select in selects:
                try:
                    value = await select.input_value()
                    if value and value.strip():
                        filled_count += 1
                except:
                    continue

            # 检查已填写的文本框
            text_inputs = await page.query_selector_all('input[type="text"], textarea')
            for text_input in text_inputs:
                try:
                    value = await text_input.input_value()
                    if value and value.strip():
                        filled_count += 1
                except:
                    continue

            progress_percentage = (filled_count / total_elements * 100) if total_elements > 0 else 0

            return {
                "total_elements": total_elements,
                "filled_count": filled_count,
                "remaining": total_elements - filled_count,
                "progress_percentage": progress_percentage
            }

        except Exception as e:
            self.logger.error(f"❌ 进度分析失败: {e}")
            return {"total_elements": 0, "filled_count": 0, "remaining": 0, "progress_percentage": 0}

    async def identify_next_action_candidates(self, page) -> List[Dict[str, Any]]:
        """识别下一步动作候选"""
        try:
            candidates = []

            # 优先处理可见的第一个未填写元素
            current_interactive = await self.get_current_interactive_elements(page)

            for element in current_interactive[:3]:  # 只考虑前3个
                if element["type"] == "radio":
                    candidates.append({
                        "action": "click",
                        "target": element["selector"],
                        "reasoning": f"选择单选按钮 {element['name']}"
                    })
                elif element["type"] == "select":
                    candidates.append({
                        "action": "select",
                        "target": element["selector"],
                        "reasoning": f"选择下拉框 {element['name']}"
                    })
                elif element["type"] == "text":
                    candidates.append({
                        "action": "fill",
                        "target": element["selector"],
                        "reasoning": f"填写文本框 {element['name']}"
                    })

            return candidates

        except Exception as e:
            self.logger.error(f"❌ 识别候选动作失败: {e}")
            return []
    
    async def llm_analyze_and_decide(self, observation: Dict[str, Any]) -> Dict[str, Any]:
        """使用LLM分析当前状态并决定下一步动作"""
        try:
            # 构建提示词
            prompt = self.build_questionnaire_prompt(observation)
            
            # 调用LLM
            response = await self.llm.ainvoke(prompt)
            
            # 解析LLM响应
            action_decision = self.parse_llm_response(response.content)
            
            return action_decision
            
        except Exception as e:
            self.logger.error(f"❌ LLM分析失败: {e}")
            return {"action": "wait", "completed": False, "error": str(e)}
    
    def build_questionnaire_prompt(self, observation: Dict[str, Any]) -> str:
        """构建智能问卷填写的提示词"""
        digital_human = self.digital_human_info
        current_interactive = observation.get('current_interactive', [])
        progress = observation.get('progress', {})
        candidates = observation.get('next_action_candidates', [])

        # 构建当前可交互元素的描述
        interactive_desc = ""
        if current_interactive:
            interactive_desc = "当前可交互元素：\n"
            for i, element in enumerate(current_interactive[:3]):
                interactive_desc += f"{i+1}. {element['type']} - {element['name']} (选择器: {element['selector']})\n"

        # 构建候选动作描述
        candidates_desc = ""
        if candidates:
            candidates_desc = "推荐动作：\n"
            for i, candidate in enumerate(candidates[:3]):
                candidates_desc += f"{i+1}. {candidate['action']} - {candidate['target']} ({candidate['reasoning']})\n"

        prompt = f"""
你是一个专业的问卷填写AI助手。请根据数字人信息智能填写问卷。

数字人档案：
- 姓名：{digital_human.get('name', '未知')}
- 年龄：{digital_human.get('age', '未知')}
- 性别：{digital_human.get('gender', '未知')}
- 职业：{digital_human.get('occupation', '未知')}
- 品牌偏好：{digital_human.get('brand_preferences', '未知')}

当前状态：
- 页面：{observation.get('title', '')}
- 进度：{progress.get('filled_count', 0)}/{progress.get('total_elements', 0)} ({progress.get('progress_percentage', 0):.1f}%)

{interactive_desc}

{candidates_desc}

请选择最合适的下一步动作。返回JSON格式：
{{
    "action": "click|fill|select|wait|completed",
    "target": "精确的CSS选择器",
    "value": "要填入的值（仅对fill/select动作）",
    "reasoning": "选择理由",
    "completed": false
}}

填写规则：
1. 根据数字人特征选择合适的答案
2. 优先填写最上方的未填写元素
3. 如果所有元素都已填写，设置 "completed": true
4. 使用提供的精确选择器，不要自己猜测

现在请选择下一步动作：
"""
        return prompt
    
    def parse_llm_response(self, response: str) -> Dict[str, Any]:
        """解析LLM响应"""
        try:
            # 尝试解析JSON
            if "{" in response and "}" in response:
                json_start = response.find("{")
                json_end = response.rfind("}") + 1
                json_str = response[json_start:json_end]
                return json.loads(json_str)
            else:
                # 如果不是JSON，返回默认动作
                return {
                    "action": "wait",
                    "reasoning": "无法解析LLM响应",
                    "completed": False
                }
        except Exception as e:
            self.logger.error(f"❌ LLM响应解析失败: {e}")
            return {
                "action": "wait",
                "error": str(e),
                "completed": False
            }
    
    async def execute_questionnaire_action(self, action_decision: Dict[str, Any]) -> Dict[str, Any]:
        """智能执行问卷动作"""
        try:
            page = await self.browser_context.get_current_page()
            action = action_decision.get("action", "wait")
            target = action_decision.get("target", "")
            value = action_decision.get("value", "")

            self.logger.info(f"🎯 执行动作: {action} -> {target}")

            if action == "completed":
                return {"success": True, "action": "completed", "completed": True}

            if action == "click":
                return await self.smart_click_element(page, target)

            elif action == "fill":
                return await self.smart_fill_element(page, target, value)

            elif action == "select":
                return await self.smart_select_element(page, target, value)

            elif action == "wait":
                await asyncio.sleep(2)
                return {"success": True, "action": "wait"}

            return {"success": False, "error": f"未知动作: {action}"}

        except Exception as e:
            self.logger.error(f"❌ 动作执行失败: {e}")
            return {"success": False, "error": str(e)}

    async def smart_click_element(self, page, target: str) -> Dict[str, Any]:
        """智能点击元素"""
        try:
            # 尝试多种定位策略
            selectors_to_try = [
                target,
                f"[name='{target}']",
                f"#{target}",
                f".{target}",
                f"[id*='{target}']",
                f"[class*='{target}']"
            ]

            for selector in selectors_to_try:
                try:
                    # 等待元素可见
                    await page.wait_for_selector(selector, timeout=5000)
                    element = await page.query_selector(selector)

                    if element and await element.is_visible():
                        # 滚动到元素位置
                        await element.scroll_into_view_if_needed()
                        await asyncio.sleep(0.5)

                        # 点击元素
                        await element.click()

                        self.logger.info(f"✅ 成功点击元素: {selector}")
                        return {"success": True, "action": "click", "target": selector}

                except Exception as e:
                    continue

            return {"success": False, "error": f"无法找到可点击的元素: {target}"}

        except Exception as e:
            return {"success": False, "error": f"点击操作失败: {str(e)}"}

    async def smart_fill_element(self, page, target: str, value: str) -> Dict[str, Any]:
        """智能填写元素"""
        try:
            # 根据数字人信息生成合适的值
            if not value:
                value = self.generate_appropriate_value(target)

            # 尝试多种定位策略
            selectors_to_try = [
                target,
                f"[name='{target}']",
                f"#{target}",
                f"input[name='{target}']",
                f"textarea[name='{target}']"
            ]

            for selector in selectors_to_try:
                try:
                    await page.wait_for_selector(selector, timeout=5000)
                    element = await page.query_selector(selector)

                    if element and await element.is_visible():
                        # 清空并填写
                        await element.clear()
                        await element.fill(value)

                        self.logger.info(f"✅ 成功填写元素: {selector} = {value}")
                        return {"success": True, "action": "fill", "target": selector, "value": value}

                except Exception as e:
                    continue

            return {"success": False, "error": f"无法找到可填写的元素: {target}"}

        except Exception as e:
            return {"success": False, "error": f"填写操作失败: {str(e)}"}

    async def smart_select_element(self, page, target: str, value: str) -> Dict[str, Any]:
        """智能选择下拉框"""
        try:
            # 根据数字人信息生成合适的选择
            if not value:
                value = await self.generate_appropriate_select_value(page, target)

            # 尝试多种定位策略
            selectors_to_try = [
                target,
                f"[name='{target}']",
                f"#{target}",
                f"select[name='{target}']"
            ]

            for selector in selectors_to_try:
                try:
                    await page.wait_for_selector(selector, timeout=5000)
                    element = await page.query_selector(selector)

                    if element and await element.is_visible():
                        # 尝试按值选择
                        try:
                            await element.select_option(value=value)
                        except:
                            # 如果按值失败，尝试按文本选择
                            await element.select_option(label=value)

                        self.logger.info(f"✅ 成功选择元素: {selector} = {value}")
                        return {"success": True, "action": "select", "target": selector, "value": value}

                except Exception as e:
                    continue

            return {"success": False, "error": f"无法找到可选择的元素: {target}"}

        except Exception as e:
            return {"success": False, "error": f"选择操作失败: {str(e)}"}

    def generate_appropriate_value(self, field_name: str) -> str:
        """根据字段名和数字人信息生成合适的值"""
        digital_human = self.digital_human_info
        field_lower = field_name.lower()

        if "name" in field_lower or "姓名" in field_lower:
            return digital_human.get('name', '张三')
        elif "age" in field_lower or "年龄" in field_lower:
            return str(digital_human.get('age', 25))
        elif "phone" in field_lower or "电话" in field_lower:
            return "13800138000"
        elif "email" in field_lower or "邮箱" in field_lower:
            return "<EMAIL>"
        else:
            return "测试内容"

    async def generate_appropriate_select_value(self, page, target: str) -> str:
        """为下拉框生成合适的选择值"""
        try:
            element = await page.query_selector(target)
            if element:
                options = await element.query_selector_all('option')
                if len(options) > 1:  # 跳过第一个空选项
                    # 选择第二个选项作为默认
                    second_option = options[1]
                    return await second_option.get_attribute('value') or await second_option.inner_text()
            return ""
        except:
            return ""
