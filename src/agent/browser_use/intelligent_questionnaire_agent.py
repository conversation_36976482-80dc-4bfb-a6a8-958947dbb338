"""
智能问卷填写Agent - 专门用于问卷自动填写的智能代理
基于browser_use框架，集成LLM推理和问卷专用逻辑
"""

import logging
import asyncio
import json
from typing import Optional, Dict, Any, List
from datetime import datetime

# 导入browser_use组件
try:
    from browser_use.agent.service import Agent
    from browser_use.browser.context import BrowserContext
    browser_use_available = True
except ImportError as e:
    logging.warning(f"⚠️ browser_use组件导入失败: {e}")
    browser_use_available = False
    
    # 创建占位符类
    class Agent:
        def __init__(self, **kwargs): pass
        async def run(self, max_steps=100): return {"success": False, "error": "browser_use不可用"}


class IntelligentQuestionnaireAgent(Agent):
    """智能问卷填写Agent - 专门针对问卷优化的智能代理"""
    
    def __init__(self, task: str, llm, browser_context: BrowserContext, 
                 controller=None, digital_human_info: Dict = None, **kwargs):
        """初始化智能问卷Agent"""
        self.logger = logging.getLogger(f"{__name__}.IntelligentQuestionnaireAgent")
        
        if not browser_use_available:
            self.logger.error("❌ browser_use不可用")
            raise ImportError("browser_use组件不可用")
        
        # 调用父类初始化
        super().__init__(
            task=task,
            llm=llm,
            browser_context=browser_context
        )
        
        # 设置问卷专用属性
        self.controller = controller
        self.digital_human_info = digital_human_info or {}
        self.llm = llm
        self.questionnaire_state = {
            "current_question": 0,
            "total_questions": 0,
            "answered_questions": [],
            "errors": []
        }
        
        self.logger.info("✅ 智能问卷Agent初始化完成")
    
    async def run(self, max_steps: int = 100) -> Dict[str, Any]:
        """执行问卷填写任务"""
        self.logger.info(f"🚀 开始智能问卷填写，最大步数: {max_steps}")
        
        try:
            # 分析页面结构
            page_analysis = await self.analyze_questionnaire_page()
            if not page_analysis["success"]:
                return {"success": False, "error": "页面分析失败"}
            
            # 执行问卷填写
            result = await self.execute_questionnaire_filling(max_steps)
            return result
            
        except Exception as e:
            self.logger.error(f"❌ 问卷填写过程中发生错误: {e}")
            return {
                "success": False,
                "error": str(e),
                "steps": self._step_count,
                "questionnaire_state": self.questionnaire_state
            }
    
    async def analyze_questionnaire_page(self) -> Dict[str, Any]:
        """分析问卷页面结构"""
        try:
            self.logger.info("🔍 开始分析问卷页面结构...")
            
            page = await self.browser_context.get_current_page()
            
            # 获取页面基本信息
            title = await page.title()
            url = page.url
            
            # 查找问卷元素
            questions = await page.query_selector_all('[class*="field"], [class*="question"], .div_question, .field')
            inputs = await page.query_selector_all('input, select, textarea')
            
            self.questionnaire_state["total_questions"] = len(questions)
            
            self.logger.info(f"✅ 页面分析完成: 标题={title}, 问题数={len(questions)}, 输入元素={len(inputs)}")
            
            return {
                "success": True,
                "title": title,
                "url": url,
                "questions_count": len(questions),
                "inputs_count": len(inputs)
            }
            
        except Exception as e:
            self.logger.error(f"❌ 页面分析失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def execute_questionnaire_filling(self, max_steps: int) -> Dict[str, Any]:
        """执行问卷填写逻辑"""
        self.logger.info("🎯 开始执行智能问卷填写...")
        
        filled_count = 0
        
        for step in range(max_steps):
            self._step_count = step + 1
            
            try:
                # 观察当前状态
                observation = await self.observe_questionnaire_state()
                
                # 使用LLM分析并决定动作
                action_decision = await self.llm_analyze_and_decide(observation)
                
                if action_decision.get("completed", False):
                    self.logger.info("✅ 问卷填写完成")
                    break
                
                # 执行动作
                action_result = await self.execute_questionnaire_action(action_decision)
                
                if action_result.get("success", False):
                    filled_count += 1
                    self.questionnaire_state["answered_questions"].append({
                        "step": self._step_count,
                        "action": action_decision,
                        "result": action_result
                    })
                
                # 短暂等待
                await asyncio.sleep(1)
                
            except Exception as e:
                self.logger.error(f"❌ 步骤 {self._step_count} 执行失败: {e}")
                self.questionnaire_state["errors"].append({
                    "step": self._step_count,
                    "error": str(e)
                })
        
        return {
            "success": filled_count > 0,
            "steps": self._step_count,
            "filled_questions": filled_count,
            "questionnaire_state": self.questionnaire_state,
            "message": f"完成 {filled_count} 个问题的填写"
        }
    
    async def observe_questionnaire_state(self) -> Dict[str, Any]:
        """观察当前问卷状态"""
        try:
            page = await self.browser_context.get_current_page()
            
            # 获取页面截图用于视觉分析
            screenshot = await page.screenshot()
            
            # 获取页面HTML结构
            html_content = await page.content()
            
            # 查找未填写的问题
            unfilled_inputs = await page.query_selector_all('input:not([value]), select:not([value]), textarea:empty')
            
            return {
                "url": page.url,
                "title": await page.title(),
                "screenshot": screenshot,
                "html_snippet": html_content[:2000],  # 前2000字符
                "unfilled_count": len(unfilled_inputs),
                "step": self._step_count,
                "digital_human": self.digital_human_info
            }
            
        except Exception as e:
            self.logger.error(f"❌ 状态观察失败: {e}")
            return {"error": str(e), "step": self._step_count}
    
    async def llm_analyze_and_decide(self, observation: Dict[str, Any]) -> Dict[str, Any]:
        """使用LLM分析当前状态并决定下一步动作"""
        try:
            # 构建提示词
            prompt = self.build_questionnaire_prompt(observation)
            
            # 调用LLM
            response = await self.llm.ainvoke(prompt)
            
            # 解析LLM响应
            action_decision = self.parse_llm_response(response.content)
            
            return action_decision
            
        except Exception as e:
            self.logger.error(f"❌ LLM分析失败: {e}")
            return {"action": "wait", "completed": False, "error": str(e)}
    
    def build_questionnaire_prompt(self, observation: Dict[str, Any]) -> str:
        """构建问卷填写的提示词"""
        digital_human = self.digital_human_info
        
        prompt = f"""
你是一个智能问卷填写助手。请根据以下信息填写问卷：

数字人信息：
- 姓名：{digital_human.get('name', '未知')}
- 年龄：{digital_human.get('age', '未知')}
- 性别：{digital_human.get('gender', '未知')}
- 职业：{digital_human.get('occupation', '未知')}
- 品牌偏好：{digital_human.get('brand_preferences', '未知')}

当前页面状态：
- URL：{observation.get('url', '')}
- 标题：{observation.get('title', '')}
- 未填写元素数量：{observation.get('unfilled_count', 0)}

请分析页面并决定下一步动作。返回JSON格式：
{{
    "action": "click|fill|select|submit|wait",
    "target": "元素选择器或描述",
    "value": "要填入的值（如果适用）",
    "reasoning": "选择这个动作的原因",
    "completed": false
}}

如果问卷已完成，设置 "completed": true
"""
        return prompt
    
    def parse_llm_response(self, response: str) -> Dict[str, Any]:
        """解析LLM响应"""
        try:
            # 尝试解析JSON
            if "{" in response and "}" in response:
                json_start = response.find("{")
                json_end = response.rfind("}") + 1
                json_str = response[json_start:json_end]
                return json.loads(json_str)
            else:
                # 如果不是JSON，返回默认动作
                return {
                    "action": "wait",
                    "reasoning": "无法解析LLM响应",
                    "completed": False
                }
        except Exception as e:
            self.logger.error(f"❌ LLM响应解析失败: {e}")
            return {
                "action": "wait",
                "error": str(e),
                "completed": False
            }
    
    async def execute_questionnaire_action(self, action_decision: Dict[str, Any]) -> Dict[str, Any]:
        """执行具体的问卷动作"""
        try:
            page = await self.browser_context.get_current_page()
            action = action_decision.get("action", "wait")
            
            self.logger.info(f"🎯 执行动作: {action}")
            
            if action == "click":
                # 执行点击动作
                target = action_decision.get("target", "")
                if target:
                    await page.click(target)
                    return {"success": True, "action": "click", "target": target}
            
            elif action == "fill":
                # 执行填写动作
                target = action_decision.get("target", "")
                value = action_decision.get("value", "")
                if target and value:
                    await page.fill(target, value)
                    return {"success": True, "action": "fill", "target": target, "value": value}
            
            elif action == "select":
                # 执行选择动作
                target = action_decision.get("target", "")
                value = action_decision.get("value", "")
                if target and value:
                    await page.select_option(target, value)
                    return {"success": True, "action": "select", "target": target, "value": value}
            
            elif action == "wait":
                # 等待动作
                await asyncio.sleep(2)
                return {"success": True, "action": "wait"}
            
            return {"success": False, "error": f"未知动作: {action}"}
            
        except Exception as e:
            self.logger.error(f"❌ 动作执行失败: {e}")
            return {"success": False, "error": str(e)}
