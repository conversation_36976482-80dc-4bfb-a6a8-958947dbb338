from __future__ import annotations

import asyncio
import logging
import os
import json
import random
import hashlib
import re
import time
import aiohttp
from typing import Dict, Any, Optional, List, Tuple, Callable, Awaitable

# 导入必要的browser_use组件
from browser_use.agent.service import Agent
from browser_use.agent.views import AgentStepIn<PERSON>, AgentHistoryList
from browser_use.utils import time_execution_async

# 定义AgentHookFunc类型
AgentHookFunc = Callable[['BrowserUseAgent'], Awaitable[None]]

logger = logging.getLogger(__name__)

SKIP_LLM_API_KEY_VERIFICATION = (
        os.environ.get("SKIP_LLM_API_KEY_VERIFICATION", "false").lower()[0] in "ty1"
)

# 🧠 第二层：答题一致性保障系统
class QuestionAnswerMemoryBank:
    """🧠 问题-答案记忆库：确保前后答题一致性"""
    
    def __init__(self, digital_human_info: Dict[str, Any]):
        self.digital_human_info = digital_human_info
        self.question_memory = {}  # 问题指纹 -> 答案
        self.logical_rules = self._build_logical_consistency_rules()
        self.session_start_time = time.time()
        
    def _generate_question_fingerprint(self, question_text: str, options: List[str] = None) -> str:
        """生成问题指纹：忽略细微差异，识别本质相同的问题"""
        try:
            # 标准化问题文本
            normalized_question = re.sub(r'\s+', ' ', question_text.lower().strip())
            
            # 提取关键词
            keywords = self._extract_key_concepts(normalized_question)
            
            # 标准化选项
            if options:
                normalized_options = sorted([opt.lower().strip() for opt in options if opt])
                options_hash = hashlib.md5('|'.join(normalized_options).encode()).hexdigest()
            else:
                options_hash = "no_options"
            
            # 生成唯一指纹
            fingerprint_data = {
                'keywords': sorted(keywords),
                'options_hash': options_hash
            }
            
            return hashlib.md5(json.dumps(fingerprint_data, sort_keys=True).encode()).hexdigest()
        except Exception as e:
            logger.warning(f"⚠️ 生成问题指纹失败: {e}")
            return hashlib.md5(question_text.encode()).hexdigest()
    
    def _extract_key_concepts(self, text: str) -> List[str]:
        """提取关键概念"""
        # 移除常见停用词
        stop_words = {'的', '是', '在', '有', '和', '或', '但', '如果', '因为', '所以', 
                     'the', 'is', 'are', 'and', 'or', 'but', 'if', 'because', 'so', 'what', 'how'}
        
        # 简单分词和关键词提取
        words = re.findall(r'\b\w+\b', text.lower())
        keywords = [w for w in words if len(w) > 2 and w not in stop_words]
        
        return keywords[:10]  # 取前10个关键词
    
    async def get_consistent_answer(self, question_text: str, options: List[str] = None) -> Optional[str]:
        """获取一致的答案：如果之前回答过相同问题，返回相同答案"""
        try:
            fingerprint = self._generate_question_fingerprint(question_text, options or [])
            
            if fingerprint in self.question_memory:
                previous_answer = self.question_memory[fingerprint]
                logger.info(f"🧠 发现重复问题，使用一致答案: {previous_answer}")
                return previous_answer
            
            # 新问题：根据数字人信息和逻辑规则生成答案
            answer = await self._generate_logical_answer(question_text, options or [])
            
            # 记录答案
            if answer:
                self.question_memory[fingerprint] = answer
                logger.info(f"🧠 新问题记录答案: {answer}")
            
            return answer
        except Exception as e:
            logger.error(f"❌ 获取一致答案失败: {e}")
            return None
    
    async def _generate_logical_answer(self, question_text: str, options: List[str]) -> Optional[str]:
        """根据数字人信息生成逻辑一致的答案"""
        try:
            # 基于数字人信息的智能匹配
            question_lower = question_text.lower()
            
            # 年龄相关问题
            if any(keyword in question_lower for keyword in ['age', 'old', '年龄', '岁']):
                age = self.digital_human_info.get('age', 25)
                return self._select_age_appropriate_option(options, age)
            
            # 收入相关问题
            if any(keyword in question_lower for keyword in ['income', 'salary', 'money', '收入', '工资', '薪水']):
                income = self.digital_human_info.get('income', '')
                return self._select_income_appropriate_option(options, income)
            
            # 职业相关问题
            if any(keyword in question_lower for keyword in ['job', 'work', 'profession', '工作', '职业']):
                profession = self.digital_human_info.get('profession', '')
                return self._select_profession_appropriate_option(options, profession)
            
            # 地区相关问题
            if any(keyword in question_lower for keyword in ['location', 'city', 'country', '地区', '城市', '国家']):
                location = self.digital_human_info.get('location', '')
                return self._select_location_appropriate_option(options, location)
            
            # 默认选择第一个选项
            return options[0] if options else None
            
        except Exception as e:
            logger.error(f"❌ 生成逻辑答案失败: {e}")
            return options[0] if options else None
    
    def _select_age_appropriate_option(self, options: List[str], age: int) -> str:
        """选择年龄相关的合适选项"""
        for option in options:
            option_lower = option.lower()
            if age < 25 and any(keyword in option_lower for keyword in ['18-24', '20-25', 'young']):
                return option
            elif 25 <= age < 35 and any(keyword in option_lower for keyword in ['25-34', '25-35', 'adult']):
                return option
            elif 35 <= age < 45 and any(keyword in option_lower for keyword in ['35-44', '35-45', 'middle']):
                return option
        return options[0] if options else ""
    
    def _select_income_appropriate_option(self, options: List[str], income: str) -> str:
        """选择收入相关的合适选项"""
        income_lower = income.lower()
        for option in options:
            option_lower = option.lower()
            if '5000' in income_lower and any(keyword in option_lower for keyword in ['5000', '5k', 'low']):
                return option
            elif '10000' in income_lower and any(keyword in option_lower for keyword in ['10000', '10k', 'medium']):
                return option
            elif '15000' in income_lower and any(keyword in option_lower for keyword in ['15000', '15k', 'high']):
                return option
        return options[0] if options else ""
    
    def _select_profession_appropriate_option(self, options: List[str], profession: str) -> str:
        """选择职业相关的合适选项"""
        profession_lower = profession.lower()
        for option in options:
            option_lower = option.lower()
            if 'engineer' in profession_lower and any(keyword in option_lower for keyword in ['engineer', 'tech', 'it']):
                return option
            elif 'teacher' in profession_lower and any(keyword in option_lower for keyword in ['teacher', 'education']):
                return option
            elif 'doctor' in profession_lower and any(keyword in option_lower for keyword in ['doctor', 'medical']):
                return option
        return options[0] if options else ""
    
    def _select_location_appropriate_option(self, options: List[str], location: str) -> str:
        """选择地区相关的合适选项"""
        location_lower = location.lower()
        for option in options:
            option_lower = option.lower()
            if any(keyword in location_lower for keyword in ['china', '中国', 'beijing', '北京']) and \
               any(keyword in option_lower for keyword in ['china', '中国', 'beijing', '北京', 'chinese']):
                return option
        return options[0] if options else ""
    
    def _build_logical_consistency_rules(self) -> Dict[str, Any]:
        """构建逻辑一致性规则"""
        return {
            'age_based_preferences': self._get_age_based_rules(),
            'income_based_choices': self._get_income_based_rules(),
            'profession_based_answers': self._get_profession_based_rules(),
            'location_based_preferences': self._get_location_based_rules()
        }
    
    def _get_age_based_rules(self) -> Dict[str, Any]:
        """获取基于年龄的规则"""
        age = self.digital_human_info.get('age', 25)
        if age < 25:
            return {'preferences': ['technology', 'social_media', 'gaming'], 'lifestyle': 'active'}
        elif age < 35:
            return {'preferences': ['career', 'family', 'travel'], 'lifestyle': 'balanced'}
        else:
            return {'preferences': ['stability', 'health', 'family'], 'lifestyle': 'conservative'}
    
    def _get_income_based_rules(self) -> Dict[str, Any]:
        """获取基于收入的规则"""
        income = self.digital_human_info.get('income', '')
        if '5000' in income or 'low' in income.lower():
            return {'spending': 'conservative', 'priorities': ['basic_needs', 'savings']}
        elif '15000' in income or 'high' in income.lower():
            return {'spending': 'liberal', 'priorities': ['luxury', 'investment']}
        else:
            return {'spending': 'moderate', 'priorities': ['comfort', 'security']}
    
    def _get_profession_based_rules(self) -> Dict[str, Any]:
        """获取基于职业的规则"""
        profession = self.digital_human_info.get('profession', '').lower()
        if 'engineer' in profession or 'tech' in profession:
            return {'interests': ['technology', 'innovation'], 'work_style': 'analytical'}
        elif 'teacher' in profession:
            return {'interests': ['education', 'books'], 'work_style': 'nurturing'}
        else:
            return {'interests': ['general'], 'work_style': 'balanced'}
    
    def _get_location_based_rules(self) -> Dict[str, Any]:
        """获取基于地区的规则"""
        location = self.digital_human_info.get('location', '').lower()
        if any(keyword in location for keyword in ['china', '中国', 'beijing', '北京']):
            return {'culture': 'chinese', 'language': 'chinese', 'preferences': ['chinese_food', 'local_brands']}
        else:
            return {'culture': 'international', 'language': 'english', 'preferences': ['international']}
    
    def get_answered_questions_summary(self) -> str:
        """获取已回答问题的摘要"""
        if not self.question_memory:
            return "尚未回答任何问题"
        
        summary = f"已回答 {len(self.question_memory)} 个问题："
        for i, (fingerprint, answer) in enumerate(list(self.question_memory.items())[:5]):
            summary += f"\n{i+1}. 答案: {answer}"
        
        if len(self.question_memory) > 5:
            summary += f"\n... 还有 {len(self.question_memory) - 5} 个问题"
        
        return summary
    
    async def save_session_summary(self):
        """保存会话摘要"""
        try:
            summary = {
                'digital_human': self.digital_human_info,
                'questions_answered': len(self.question_memory),
                'session_duration': time.time() - self.session_start_time,
                'answers': list(self.question_memory.values())[:10]  # 保存前10个答案
            }
            
            with open(f'questionnaire_session_{int(self.session_start_time)}.json', 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
                
            logger.info(f"✅ 会话摘要已保存: {len(self.question_memory)} 个问题")
        except Exception as e:
            logger.error(f"❌ 保存会话摘要失败: {e}")

# 🔧 第三层：AdsPower资源智能管理
class AdsPowerResourceManager:
    """🔧 AdsPower资源智能管理：基于官方API文档实现"""
    
    def __init__(self, profile_id: str, adspower_host: str = "http://local.adspower.net:50325"):
        self.profile_id = profile_id
        self.adspower_host = adspower_host
        self.browser_status = "unknown"
        self.monitoring_active = True
        self.agent_reference = None
        
    def set_agent_reference(self, agent):
        """设置Agent引用"""
        self.agent_reference = agent
        
    async def start_monitoring(self):
        """🔧 【修复】启动浏览器状态监控 - 使用增强智能判断"""
        logger.info("🔧 启动增强型浏览器状态监控...")
        asyncio.create_task(self._monitor_browser_lifecycle())
    
    async def _monitor_browser_lifecycle(self):
        """持续监控浏览器生命周期"""
        while self.monitoring_active:
            try:
                # 检查浏览器状态
                current_status = await self._check_browser_status()
                
                if current_status != self.browser_status:
                    logger.info(f"🔍 浏览器状态变化: {self.browser_status} -> {current_status}")
                    self.browser_status = current_status
                    
                    # 如果检测到浏览器被关闭
                    if current_status == "Inactive":
                        logger.warning("🚨 检测到浏览器被手动关闭，开始资源清理...")
                        await self._handle_browser_closed()
                        break
                
                await asyncio.sleep(5)  # 每5秒检查一次
                
            except Exception as e:
                logger.error(f"❌ 浏览器状态监控异常: {e}")
                await asyncio.sleep(10)
    
    async def _check_browser_status(self) -> str:
        """🔧 【关键修复】检查浏览器启动状态 - 智能判断真正关闭vs临时连接问题"""
        try:
            url = f"{self.adspower_host}/api/v1/browser/active"
            params = {"user_id": self.profile_id}
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=5)) as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("code") == 0:
                            status = data.get("data", {}).get("status", "Unknown")
                            
                            # 🔍 【新增】：智能状态验证
                            if status == "Inactive":
                                # 进行二次确认，避免误判
                                verified_status = await self._verify_browser_truly_closed()
                                if verified_status == "actually_active":
                                    logger.info("✅ 二次验证：浏览器实际仍在运行，忽略Inactive状态")
                                    return "Active"  # 修正状态
                                else:
                                    logger.warning(f"🚨 确认浏览器已关闭: {verified_status}")
                                    return "Inactive"
                            
                            return status
                    return "Unknown"
                    
        except Exception as e:
            logger.warning(f"⚠️ 无法获取浏览器状态: {e}")
            # 🔧 【修复】：API失败时不应判断为关闭
            return "Unknown"
    
    async def _verify_browser_truly_closed(self) -> str:
        """🔍 【新增】二次验证浏览器是否真正关闭"""
        try:
            # 方法1：尝试通过WebSocket连接测试
            if hasattr(self, 'agent_reference') and self.agent_reference:
                try:
                    # 尝试获取当前页面，如果成功说明浏览器还在
                    browser_context = getattr(self.agent_reference, 'browser_context', None)
                    if browser_context:
                        page = await browser_context.get_current_page()
                        current_url = page.url
                        logger.info(f"🔍 验证成功：浏览器仍在运行，当前页面: {current_url[:50]}...")
                        return "actually_active"
                except Exception as browser_test_error:
                    logger.debug(f"🔍 浏览器连接测试失败: {browser_test_error}")
            
            # 方法2：再次调用AdsPower API确认
            await asyncio.sleep(2)  # 短暂等待
            try:
                url = f"{self.adspower_host}/api/v1/browser/active"
                params = {"user_id": self.profile_id}
                
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=3)) as session:
                    async with session.get(url, params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            if data.get("code") == 0:
                                status = data.get("data", {}).get("status", "Unknown")
                                if status == "Active":
                                    return "actually_active"
            except Exception as api_retry_error:
                logger.debug(f"🔍 AdsPower API重试失败: {api_retry_error}")
            
            # 方法3：检查进程是否存在（Linux/Mac）
            try:
                import psutil
                for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        if proc.info['cmdline'] and any(self.profile_id in str(arg) for arg in proc.info['cmdline']):
                            logger.info(f"🔍 发现相关Chrome进程: {proc.info['pid']}")
                            return "actually_active"
            except Exception as process_check_error:
                logger.debug(f"🔍 进程检查失败: {process_check_error}")
            
            # 所有验证都失败，确认为真正关闭
            return "truly_closed"
            
        except Exception as e:
            logger.warning(f"⚠️ 浏览器状态二次验证失败: {e}")
            return "verification_failed"
    
    async def _handle_browser_closed(self):
        """🔧 【增强】处理浏览器被关闭的情况 - 强制完整资源清理"""
        try:
            logger.info("🔧 开始智能资源清理流程...")
            
            # 1. 【新增】：最后一次挽救尝试
            logger.info("🚑 执行最后挽救尝试...")
            salvage_result = await self._attempt_browser_salvage()
            
            if salvage_result == "salvaged":
                logger.info("🎉 浏览器挽救成功，继续执行！")
                return  # 不需要清理，继续执行
            
            # 2. 确认无法挽救，开始正式清理
            logger.warning("💔 确认浏览器无法挽救，开始资源清理...")
            
            # 停止Agent执行
            if self.agent_reference:
                self.agent_reference.state.stopped = True
                logger.info("✅ Agent执行已停止")
            
            # 🔥 【关键修复】：强制执行完整的两步AdsPower资源清理
            logger.info("🚀 开始强制完整AdsPower资源清理（两步骤流程）...")
            
            # 第一步：停止浏览器实例
            await self._force_close_browser_profile()
            
            # 第二步：删除配置文件（关键步骤 - 从AdsPower列表中完全移除）
            await self._force_delete_browser_profile()
            
            # 清理本地资源
            await self._cleanup_local_resources()
            
            # 发送清理完成通知
            logger.info("🎉 浏览器关闭后资源清理完成")
            
        except Exception as e:
            logger.error(f"❌ 资源清理失败: {e}")
    
    async def _force_close_browser_profile(self):
        """强制关闭浏览器配置文件 - 基于AdsPower API"""
        try:
            # 使用AdsPower关闭浏览器API
            url = f"{self.adspower_host}/api/v1/browser/stop"
            data = {"user_id": self.profile_id}
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                async with session.post(url, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get("code") == 0:
                            logger.info("✅ AdsPower浏览器配置文件已关闭")
                        else:
                            logger.warning(f"⚠️ AdsPower关闭响应: {result}")
                    else:
                        logger.warning(f"⚠️ AdsPower关闭请求失败: {response.status}")
                        
        except Exception as e:
            logger.error(f"❌ 强制关闭AdsPower配置文件失败: {e}")
    
    async def _cleanup_local_resources(self):
        """清理本地资源"""
        try:
            # 停止监控
            self.monitoring_active = False
            
            # 清理其他资源
            logger.info("✅ 本地资源清理完成")
            
        except Exception as e:
            logger.error(f"❌ 本地资源清理失败: {e}")
    
    async def cleanup_all_resources(self):
        """清理所有资源"""
        await self._handle_browser_closed()

    async def _attempt_browser_salvage(self) -> str:
        """🚑 【新增】：尝试挽救浏览器连接"""
        try:
            logger.info("🚑 尝试挽救浏览器连接...")
            
            # 尝试1：重新连接现有浏览器
            try:
                if hasattr(self, 'agent_reference') and self.agent_reference:
                    browser_context = getattr(self.agent_reference, 'browser_context', None)
                    if browser_context:
                        # 尝试刷新连接
                        page = await browser_context.get_current_page()
                        await page.reload()
                        logger.info("✅ 浏览器连接挽救成功")
                        return "salvaged"
            except Exception as salvage1_error:
                logger.debug(f"🚑 挽救尝试1失败: {salvage1_error}")
            
            # 尝试2：重新启动AdsPower浏览器
            try:
                logger.info("🚑 尝试重新启动AdsPower浏览器...")
                start_url = f"{self.adspower_host}/api/v1/browser/start"
                start_data = {"user_id": self.profile_id}
                
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
                    async with session.post(start_url, json=start_data) as response:
                        if response.status == 200:
                            result = await response.json()
                            if result.get("code") == 0:
                                logger.info("✅ AdsPower浏览器重新启动成功")
                                await asyncio.sleep(5)  # 等待启动完成
                                return "salvaged"
            except Exception as salvage2_error:
                logger.debug(f"🚑 挽救尝试2失败: {salvage2_error}")
            
            return "failed"
            
        except Exception as e:
            logger.warning(f"⚠️ 浏览器挽救过程失败: {e}")
            return "failed"

    async def _force_delete_browser_profile(self):
        """🗑️ 【新增】强制删除AdsPower配置文件 - 从应用列表中完全移除"""
        try:
            logger.info("🗑️ 开始删除AdsPower配置文件（从应用列表中完全移除）...")
            
            # 等待浏览器停止完成
            await asyncio.sleep(2)
            
            # 使用AdsPower删除配置文件API
            url = f"{self.adspower_host}/api/v1/user/delete"
            data = {"user_ids": [self.profile_id]}
            
            logger.info(f"🗑️ 调用AdsPower删除API: {url}")
            logger.info(f"🗑️ 删除配置文件ID: {self.profile_id}")
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=15)) as session:
                async with session.post(url, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get("code") == 0:
                            logger.info("✅ AdsPower配置文件删除成功")
                            logger.info("🎯 配置文件已从AdsPower应用列表中完全移除")
                            logger.info("💾 浏览器额度已释放，可创建新的配置文件")
                        else:
                            logger.warning(f"⚠️ AdsPower配置文件删除失败: {result.get('msg', '未知错误')}")
                    else:
                        logger.warning(f"⚠️ AdsPower删除请求失败: {response.status}")
                        
        except Exception as e:
            logger.error(f"❌ 强制删除AdsPower配置文件失败: {e}")
            logger.error("❌ 配置文件可能仍占用AdsPower浏览器额度")

class BrowserUseAgent(Agent):
    def __init__(self, *args, **kwargs):
        """🔥 五层融合架构：智能问卷系统"""
        
        # 🎯 提取配置参数
        self.digital_human_info = kwargs.pop('digital_human_info', {})
        self.never_give_up_mode = kwargs.pop('never_give_up_mode', True)
        self.questionnaire_mode = kwargs.pop('questionnaire_mode', True)
        profile_id = kwargs.pop('profile_id', 'default')
        
        # 调用父类构造函数
        super().__init__(*args, **kwargs)
        
        # 🔥 第一层：智能停止决策引擎
        self.completion_evidence_score = 0
        self.completion_evidence_details = []
        self.questionnaire_truly_completed = False
        
        # 🧠 第二层：答题一致性保障系统
        self.memory_bank = QuestionAnswerMemoryBank(self.digital_human_info)
        
        # 🔧 第三层：浏览器生命周期管理
        self.resource_manager = AdsPowerResourceManager(profile_id)
        self.resource_manager.set_agent_reference(self)
        
        # 🚀 第四层：永不放弃执行引擎
        self.page_recovery_attempts = 0
        self.max_recovery_attempts = 15  # 增加恢复次数
        self.last_successful_action_time = time.time()
        self.consecutive_failures = 0
        self.max_consecutive_failures = 10  # 增加容错次数
        
        # 🎯 问卷特定状态
        self.questions_answered = 0
        self.pages_navigated = 0
        self.submit_attempts = 0
        self.loading_wait_time = 0
        self.last_question_time = time.time()
        
        # 🧠 第五层：WebUI原生深度集成
        self.intelligent_context = {
            'scene_detection_enabled': True,
            'questionnaire_intelligence_active': True,
            'country_selection_priority': True,
            'answer_consistency_check': True,
            'memory_bank_active': True,
            'resource_monitoring_active': True
        }
        
        logger.info(f"🔥 五层融合智能问卷系统已激活")
        logger.info(f"👤 数字人: {self.digital_human_info.get('name', '未知')}")
        logger.info(f"🚀 永不放弃模式: {self.never_give_up_mode}")
        logger.info(f"📝 问卷模式: {self.questionnaire_mode}")
        logger.info(f"🧠 记忆库: 已初始化")
        logger.info(f"🔧 资源管理: 已启动")
    
    # 🔥 第一层：智能停止决策引擎
    async def _should_truly_stop_questionnaire(self) -> Tuple[bool, str]:
        """🎯 智能停止决策：保守策略，宁可继续也不轻易停止"""
        try:
            # 📊 收集多维度证据
            evidence_score = 0
            evidence_details = []
            
            # 证据1：页面内容明确完成提示 (权重: 40分)
            completion_phrases = [
                "thank you for your participation", "survey has been completed", 
                "questionnaire completed successfully", "survey completed",
                "感谢您的参与", "问卷已完成", "调查已结束",
                "提交成功", "完成问卷", "谢谢参与", "感谢参与"
            ]
            
            page_text = await self._get_page_text_safely()
            strong_completion_found = False
            for phrase in completion_phrases:
                if phrase in page_text.lower():
                    evidence_score += 40
                    evidence_details.append(f"发现强完成提示: {phrase}")
                    strong_completion_found = True
                    break
            
            # 证据2：URL明确指示完成 (权重: 20分)
            current_url = await self._get_current_url_safely()
            url_completion_keywords = ["complete", "finished", "thank", "success", "end"]
            for keyword in url_completion_keywords:
                if keyword in current_url.lower():
                    evidence_score += 20
                    evidence_details.append(f"URL包含完成关键词: {keyword}")
                    break
            
            # 证据3：统计指标达到合理阈值 (权重: 20分)
            if (self.questions_answered >= 15 and 
                self.pages_navigated >= 8 and 
                self.submit_attempts >= 5):
                evidence_score += 20
                evidence_details.append(f"统计指标: 答题{self.questions_answered}, 页面{self.pages_navigated}, 提交{self.submit_attempts}")
            
            # 证据4：页面结构指示完成 (权重: 10分)
            completion_elements = await self._detect_completion_page_structure()
            if completion_elements:
                evidence_score += 10
                evidence_details.append("页面结构指示完成")
            
            # 证据5：长时间无新问题出现 (权重: 10分)
            if self._no_new_questions_for_long_time():
                evidence_score += 10
                evidence_details.append("长时间无新问题")
            
            # 🚀 保守决策：需要至少60分才考虑停止，且必须有强完成提示
            should_stop = (evidence_score >= 60 and strong_completion_found)
            
            decision_reason = f"证据评分: {evidence_score}/100, 强完成提示: {strong_completion_found}"
            self.completion_evidence_score = evidence_score
            self.completion_evidence_details = evidence_details
            
            return should_stop, decision_reason
        except Exception as e:
            logger.error(f"❌ 智能停止决策失败: {e}")
            return False, f"决策异常: {e}"
    
    async def _get_page_text_safely(self) -> str:
        """安全获取页面文本"""
        try:
            current_page = await self.browser_context.get_current_page()
            page_text = await current_page.evaluate("document.body ? document.body.innerText : ''")
            return page_text.lower()
        except Exception as e:
            logger.warning(f"⚠️ 获取页面文本失败: {e}")
            return ""
    
    async def _get_current_url_safely(self) -> str:
        """安全获取当前URL"""
        try:
            current_page = await self.browser_context.get_current_page()
            return current_page.url.lower()
        except Exception as e:
            logger.warning(f"⚠️ 获取当前URL失败: {e}")
            return ""
    
    async def _detect_completion_page_structure(self) -> bool:
        """检测完成页面结构"""
        try:
            current_page = await self.browser_context.get_current_page()
            
            # 检查是否有感谢页面的典型元素
            completion_selectors = [
                "[class*='thank']", "[class*='complete']", "[class*='finish']",
                "[id*='thank']", "[id*='complete']", "[id*='finish']",
                "h1:contains('Thank')", "h1:contains('Complete')", "h1:contains('感谢')"
            ]
            
            for selector in completion_selectors:
                try:
                    elements = await current_page.query_selector_all(selector)
                    if elements:
                        return True
                except:
                    continue
            
            return False
        except Exception as e:
            logger.warning(f"⚠️ 检测完成页面结构失败: {e}")
            return False
    
    def _no_new_questions_for_long_time(self) -> bool:
        """检查是否长时间无新问题"""
        current_time = time.time()
        return (current_time - self.last_question_time) > 300  # 5分钟无新问题
    
    # 🚀 第四层：永不放弃执行引擎（升级版）
    async def step(self, step_info: AgentStepInfo) -> None:
        """🔥 核心修改：智能步骤执行，五层架构集成"""
        try:
            # 🎯 第一步：启动资源监控（首次执行）- 使用修复后的智能监控
            if step_info.step_number == 0:
                logger.info("🔧 启动智能资源监控...")
                await self.resource_manager.start_monitoring()
            
            # 🎯 第二步：场景检测和智能上下文注入
            await self._inject_intelligent_reasoning_context()
            
            # 🎯 第三步：检查是否需要页面恢复
            if await self._should_attempt_page_recovery():
                await self._attempt_intelligent_page_recovery()
                return
            
            # 🎯 第四步：执行原生step逻辑，但拦截done动作
            await self._execute_step_with_intelligent_done_interception(step_info)
            
            # 🎯 第五步：更新智能状态
            await self._update_intelligent_state()
            
        except Exception as e:
            logger.error(f"❌ 智能步骤执行失败: {e}")
            await self._handle_step_failure_with_consistency_check(e)
    
    async def _execute_step_with_intelligent_done_interception(self, step_info: AgentStepInfo) -> None:
        """🔥 智能Done动作拦截：结合一致性检查和资源管理"""
        try:
            # 执行原生step
            await super().step(step_info)
            
            # 检查是否有done动作
            if self.state.history.history:
                last_action = self.state.history.history[-1]
                if self._contains_done_action(last_action):
                    
                    # 🎯 多层验证是否应该真正停止
                    should_stop, reason = await self._should_truly_stop_questionnaire()
                    
                    if should_stop:
                        logger.info(f"✅ 确认问卷真正完成: {reason}")
                        # 执行优雅关闭流程
                        await self._execute_graceful_shutdown()
                    else:
                        logger.warning(f"🚫 拦截过早done动作: {reason}")
                        # 移除done动作，继续执行
                        await self._override_premature_done_with_recovery()
                        
        except Exception as e:
            logger.error(f"❌ 智能done拦截失败: {e}")
            if self.never_give_up_mode:
                await self._handle_step_failure_with_consistency_check(e)
    
    def _contains_done_action(self, action) -> bool:
        """检查动作是否包含done"""
        try:
            if hasattr(action, 'result') and action.result:
                for result in action.result:
                    if hasattr(result, 'extracted_content'):
                        content = result.extracted_content or ""
                        if 'done' in content.lower():
                            return True
            return False
        except:
            return False
    
    async def _override_premature_done_with_recovery(self) -> None:
        """重写过早的done动作，执行智能恢复"""
        try:
            logger.info("🔄 正在重写过早的done动作...")
            
            # 🎯 移除最后的done动作记录
            if self.state.history.history:
                self.state.history.history.pop()
                logger.info("✅ 已移除过早的done动作记录")
            
            # 🎯 重置Agent状态，准备继续
            self.state.stopped = False
            self.state.paused = False
            
            # 🎯 执行智能恢复动作
            await self._execute_recovery_actions()
            
        except Exception as e:
            logger.error(f"❌ 重写done动作失败: {e}")
    
    async def _execute_graceful_shutdown(self):
        """优雅关闭流程"""
        try:
            # 1. 保存答题记录
            await self.memory_bank.save_session_summary()
            
            # 2. 清理浏览器资源
            await self.resource_manager.cleanup_all_resources()
            
            # 3. 标记真正完成
            self.questionnaire_truly_completed = True
            
            logger.info("🎉 问卷完成，优雅关闭流程执行完毕")
            
        except Exception as e:
            logger.error(f"❌ 优雅关闭失败: {e}")
    
    async def _handle_step_failure_with_consistency_check(self, error: Exception) -> None:
        """🚀 处理步骤失败，集成一致性检查"""
        try:
            self.consecutive_failures += 1
            logger.warning(f"⚠️ 步骤失败 (第{self.consecutive_failures}次): {error}")
            
            if self.consecutive_failures < self.max_consecutive_failures:
                logger.info("🔄 永不放弃模式：尝试恢复并继续...")
                await self._attempt_intelligent_page_recovery()
            else:
                logger.warning("⚠️ 连续失败次数过多，但仍将继续尝试...")
                # 重置计数器，继续尝试
                self.consecutive_failures = 0
                await asyncio.sleep(10)  # 等待更长时间
                
        except Exception as e:
            logger.error(f"❌ 失败处理失败: {e}")
    
    async def _should_attempt_page_recovery(self) -> bool:
        """🎯 判断是否需要尝试页面恢复"""
        try:
            # 🎯 检查是否在恢复次数限制内
            if self.page_recovery_attempts >= self.max_recovery_attempts:
                return False
            
            # 🎯 检查是否有连续失败
            if self.consecutive_failures >= 3:
                return True
            
            # 🎯 检查页面是否长时间无响应
            current_time = asyncio.get_event_loop().time()
            if (self.last_successful_action_time and 
                current_time - self.last_successful_action_time > 30):  # 30秒无成功动作
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ 页面恢复判断失败: {e}")
            return False
    
    async def _attempt_intelligent_page_recovery(self) -> None:
        """🔧 尝试智能页面恢复"""
        try:
            self.page_recovery_attempts += 1
            logger.info(f"🔧 开始第 {self.page_recovery_attempts} 次智能页面恢复...")
            
            current_page = await self.browser_context.get_current_page()
            
            # 🔧 恢复步骤1: 检查页面状态
            try:
                ready_state = await current_page.evaluate("document.readyState")
                logger.info(f"📄 页面状态: {ready_state}")
                
                if ready_state != "complete":
                    logger.info("⏳ 等待页面加载完成...")
                    await asyncio.sleep(5)
            except Exception as e:
                logger.warning(f"⚠️ 无法获取页面状态: {e}")
            
            # 🔧 恢复步骤2: 尝试重新激活页面
            try:
                await current_page.bring_to_front()
                await current_page.evaluate("window.focus()")
                logger.info("✅ 页面重新激活")
            except Exception as e:
                logger.warning(f"⚠️ 页面激活失败: {e}")
            
            # 🔧 恢复步骤3: 检查是否有错误提示或加载指示
            try:
                error_elements = await current_page.query_selector_all("[class*='error'], [class*='loading'], [id*='error'], [id*='loading']")
                if error_elements:
                    logger.info(f"🔍 发现 {len(error_elements)} 个错误或加载元素")
                    # 尝试点击重试按钮
                    for element in error_elements:
                        try:
                            text = await element.inner_text()
                            if any(keyword in text.lower() for keyword in ['retry', 'reload', '重试', '重新加载']):
                                await element.click()
                                logger.info(f"🔄 点击重试按钮: {text}")
                                await asyncio.sleep(3)
                                break
                        except:
                            continue
            except Exception as e:
                logger.warning(f"⚠️ 错误元素检查失败: {e}")
            
            # 🔧 恢复步骤4: 重置失败计数
            self.consecutive_failures = 0
            self.last_successful_action_time = asyncio.get_event_loop().time()
            
            logger.info("✅ 智能页面恢复完成")
            
        except Exception as e:
            logger.error(f"❌ 智能页面恢复失败: {e}")
    
    async def _update_intelligent_state(self) -> None:
        """🧠 更新智能状态"""
        try:
            # 🎯 更新成功动作时间
            self.last_successful_action_time = asyncio.get_event_loop().time()
            
            # 🎯 分析最近的动作，更新统计
            if self.state.history.history:
                recent_actions = self.state.history.history[-5:]  # 最近5个动作
                
                # 统计答题动作
                for action in recent_actions:
                    if hasattr(action, 'result') and action.result:
                        for result in action.result:
                            if hasattr(result, 'extracted_content'):
                                content = result.extracted_content or ""
                                if any(keyword in content.lower() for keyword in ['clicked', 'input', 'selected', '点击', '输入', '选择']):
                                    self.questions_answered += 1
                                elif any(keyword in content.lower() for keyword in ['continue', 'next', 'submit', '继续', '下一', '提交']):
                                    self.pages_navigated += 1
            
            logger.debug(f"📊 智能状态更新: 答题{self.questions_answered}, 页面{self.pages_navigated}")
            
        except Exception as e:
            logger.error(f"❌ 智能状态更新失败: {e}")

    async def _execute_recovery_actions(self) -> None:
        """🔧 执行智能恢复动作"""
        try:
            logger.info("🔧 开始执行智能恢复动作...")
            
            # 🎯 获取当前页面状态
            current_page = await self.browser_context.get_current_page()
            
            # 🔧 恢复策略1: 等待页面加载
            logger.info("⏳ 等待页面完全加载...")
            await asyncio.sleep(5)
            
            # 🔧 恢复策略2: 检查页面是否响应
            try:
                await current_page.evaluate("document.readyState")
                logger.info("✅ 页面响应正常")
            except:
                logger.warning("⚠️ 页面可能未响应，尝试刷新...")
                await current_page.reload()
                await asyncio.sleep(3)
            
            # 🔧 恢复策略3: 寻找继续按钮或提交按钮
            try:
                continue_buttons = await current_page.query_selector_all("button, input[type='submit'], input[type='button']")
                if continue_buttons:
                    for btn in continue_buttons[:3]:  # 尝试前3个按钮
                        try:
                            text = await btn.inner_text()
                            if any(keyword in text.lower() for keyword in ['continue', '继续', 'next', '下一', 'submit', '提交']):
                                logger.info(f"🎯 找到继续按钮: {text}")
                                await btn.click()
                                await asyncio.sleep(2)
                                break
                        except:
                            continue
            except Exception as e:
                logger.warning(f"⚠️ 按钮搜索失败: {e}")
            
            # 🔧 恢复策略4: 滚动页面寻找更多内容
            try:
                logger.info("📜 滚动页面寻找更多内容...")
                await current_page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                await asyncio.sleep(2)
            except Exception as e:
                logger.warning(f"⚠️ 页面滚动失败: {e}")
            
            logger.info("✅ 智能恢复动作执行完成")
            
        except Exception as e:
            logger.error(f"❌ 智能恢复动作失败: {e}")

    # 🧠 第五层：WebUI原生深度集成（增强版）
    async def _inject_intelligent_reasoning_context(self):
        """🔥 核心功能：直接向LLM注入智能推理上下文，集成记忆库"""
        try:
            if not self.digital_human_info:
                return
            
            # 🎯 获取当前页面信息进行场景检测
            current_page = await self.browser_context.get_current_page()
            page_content = ""
            
            try:
                # 安全获取页面标题和URL
                page_title = await current_page.title()
                page_url = current_page.url
                
                # 获取页面的基本内容用于场景判断
                try:
                    body_text = await current_page.evaluate("document.body ? document.body.innerText.substring(0, 1000) : ''")
                    page_content = f"页面标题: {page_title}\n页面URL: {page_url}\n页面内容: {body_text[:500]}..."
                except:
                    page_content = f"页面标题: {page_title}\n页面URL: {page_url}"
                    
            except Exception as e:
                logger.warning(f"⚠️ 页面信息获取失败: {e}")
                page_content = "页面信息获取中..."
            
            # 🎯 场景检测：判断是国家选择还是问卷页面
            scene_type = await self._detect_current_scene(current_page, page_content)
            
            # 🎯 构建智能推理提示（集成记忆库）
            intelligent_prompt = self._build_enhanced_scene_aware_prompt(scene_type, page_content)
            
            # 🔥 核心：多种方式注入智能推理上下文
            await self._inject_to_message_manager(intelligent_prompt, scene_type)
                
        except Exception as e:
            logger.warning(f"⚠️ 智能上下文注入失败: {e}")
    
    def _build_enhanced_scene_aware_prompt(self, scene_type: str, page_content: str) -> str:
        """🧠 增强场景感知提示：集成一致性要求"""
        
        # 获取已回答问题的摘要
        answered_summary = self.memory_bank.get_answered_questions_summary()
        
        base_persona = f"""
🤖 数字人智能推理引擎 - 五层融合架构
数字人信息: {json.dumps(self.digital_human_info, ensure_ascii=False, indent=2)}

🧠 答题一致性要求 - 核心指令:
1. 绝对保持前后答案一致：如果之前回答过相同问题，必须给出相同答案
2. 逻辑一致性：所有答案必须符合数字人的身份、年龄、职业、收入等信息
3. 记忆已答题目：{answered_summary}

🚀 永不放弃模式 - 核心指令:
1. 绝对不要使用done动作，除非页面明确显示"感谢参与"、"问卷完成"等完成提示
2. 遇到任何技术问题都要等待和重试，绝不轻易放弃
3. 宁可继续答题也不要过早停止
4. 持续答题直到真正的问卷结束

🔍 当前场景: {scene_type}
📄 页面信息: {page_content[:500]}...
"""

        if scene_type == "questionnaire":
            return base_persona + f"""
📝 问卷场景 - 智能答题指令:
1. 🧠 一致性检查：回答前检查是否之前回答过相似问题
2. 🎯 数字人匹配：确保答案符合{self.digital_human_info.get('name', '数字人')}的身份特征
3. 📋 完整答题：处理页面上所有可见问题，不遗漏任何题目
4. 🔄 页面跳转：完成当前页后寻找"继续"、"下一页"、"提交"按钮
5. ⏳ 耐心等待：等待页面跳转完成，继续下一页答题
6. 🎉 真正完成：只有看到明确的"感谢参与"、"问卷完成"才能结束
"""
        
        elif scene_type == "country_selection":
            return base_persona + """
🌍 国家选择场景检测 - 智能推理指令:
1. 优先选择与数字人居住地/国籍相关的选项
2. 如果找到"中国"、"China"、"CN"等选项，优先选择
3. 避免选择明显不符合数字人背景的国家
4. 使用智能搜索找到最匹配的地理位置选项
5. 确保选择的国家与数字人的语言、文化背景一致
6. 选择完成后等待页面跳转，不要立即结束任务
"""
        
        else:
            return base_persona + """
🔍 通用页面场景 - 智能操作指令:
1. 仔细观察页面内容，识别可能的交互元素
2. 根据数字人信息做出合理的选择和操作
3. 保持操作的自然性和一致性
4. 等待页面完全加载后再进行操作
5. 如果页面看起来有问题，尝试刷新或等待
6. 寻找继续进行的方式，不要轻易放弃
"""

    async def _inject_to_message_manager(self, intelligent_prompt: str, scene_type: str):
        """🔥 多种方式注入到消息管理器，确保LLM能够接收到智能推理上下文"""
        try:
            # 方法1：直接注入到消息管理器
            injected = False
            if hasattr(self, 'message_manager') and intelligent_prompt:
                # 创建系统消息
                system_message = {
                    "role": "system", 
                    "content": intelligent_prompt
                }
                
                # 尝试多种属性名称和方法
                try:
                    # 尝试1: 直接添加系统消息
                    if hasattr(self.message_manager, 'add_message'):
                        self.message_manager.add_message(system_message)
                        injected = True
                        logger.info(f"✅ 通过add_message注入智能推理上下文 - 场景: {scene_type}")
                    
                    # 尝试2: 通过messages列表
                    elif hasattr(self.message_manager, 'messages'):
                        if isinstance(self.message_manager.messages, list):
                            self.message_manager.messages.insert(0, system_message)
                            injected = True
                            logger.info(f"✅ 通过messages列表注入智能推理上下文 - 场景: {scene_type}")
                    
                    # 尝试3: 检查其他可能的属性
                    else:
                        message_attrs = ['_messages', 'message_history', '_message_history', 'chat_history']
                        for attr in message_attrs:
                            if hasattr(self.message_manager, attr):
                                messages = getattr(self.message_manager, attr)
                                if isinstance(messages, list):
                                    messages.insert(0, system_message)
                                    injected = True
                                    logger.info(f"✅ 智能推理上下文已注入到 {attr} - 场景: {scene_type}")
                                    break
                    
                except Exception as e:
                    logger.warning(f"⚠️ 消息管理器注入失败: {e}")
                
                if not injected:
                    logger.debug("⚠️ 未找到合适的消息属性进行注入，尝试其他方法")
            
            # 方法2：尝试通过系统提示词注入
            if hasattr(self, 'system_prompt') and intelligent_prompt:
                original_prompt = getattr(self, 'system_prompt', '')
                enhanced_prompt = f"{intelligent_prompt}\n\n{original_prompt}"
                setattr(self, 'system_prompt', enhanced_prompt)
                logger.info("✅ 智能推理上下文已注入到系统提示词")
            
            # 方法3：尝试通过设置注入
            if hasattr(self, 'settings') and hasattr(self.settings, 'system_prompt'):
                original_prompt = getattr(self.settings, 'system_prompt', '')
                enhanced_prompt = f"{intelligent_prompt}\n\n{original_prompt}"
                setattr(self.settings, 'system_prompt', enhanced_prompt)
                logger.info("✅ 智能推理上下文已注入到设置系统提示词")
                
        except Exception as e:
            logger.error(f"❌ 消息管理器注入失败: {e}")
    
    async def _detect_current_scene(self, page, page_content: str) -> str:
        """🎯 场景检测：识别当前页面类型"""
        try:
            # 检测关键词
            content_lower = page_content.lower()
            
            # 国家/语言选择页面检测
            country_keywords = ['country', 'nation', 'location', '国家', '地区', 'select your country', 'choose country']
            if any(keyword in content_lower for keyword in country_keywords):
                return "country_selection"
            
            # 问卷页面检测
            questionnaire_keywords = ['question', 'survey', '问卷', '调查', '选择', 'choice', 'option']
            if any(keyword in content_lower for keyword in questionnaire_keywords):
                return "questionnaire"
            
            # 默认为通用页面
            return "general"
            
        except:
            return "general"

    @time_execution_async("--run (agent)")
    async def run(
            self, max_steps: int = 100, on_step_start: AgentHookFunc | None = None,
            on_step_end: AgentHookFunc | None = None
    ) -> AgentHistoryList:
        """🚀 重写run方法，实现永不放弃的执行逻辑"""

        loop = asyncio.get_event_loop()

        # 🚀 永不放弃模式：扩展最大步数
        if self.never_give_up_mode and self.questionnaire_mode:
            max_steps = max(max_steps, 500)  # 至少500步
            logger.info(f"🚀 永不放弃模式：扩展最大步数到 {max_steps}")

        try:
            # Execute initial actions if provided
            if hasattr(self, 'initial_actions') and self.initial_actions:
                result = await self.multi_act(self.initial_actions, check_for_new_elements=False)
                self.state.last_result = result

            for step in range(max_steps):
                # 🚀 永不放弃模式：修改失败检查逻辑
                if self.never_give_up_mode:
                    # 允许更多失败，但会尝试恢复
                    if hasattr(self.state, 'consecutive_failures') and self.state.consecutive_failures >= 20:
                        logger.warning(f'⚠️ 失败次数较多 ({self.state.consecutive_failures})，但永不放弃模式继续尝试...')
                        # 重置失败计数，继续尝试
                        self.state.consecutive_failures = 0
                        await asyncio.sleep(5)  # 等待一段时间

                # Check control flags before each step
                if hasattr(self.state, 'stopped') and self.state.stopped:
                    logger.info('Agent stopped')
                    break

                if on_step_start is not None:
                    await on_step_start(self)

                step_info = AgentStepInfo(step_number=step, max_steps=max_steps)
                await self.step(step_info)

                if on_step_end is not None:
                    await on_step_end(self)

                # 🚀 永不放弃模式：修改done检查逻辑
                if hasattr(self.state.history, 'is_done') and self.state.history.is_done():
                    if self.never_give_up_mode and self.questionnaire_mode:
                        # 🎯 检查是否为真正的完成
                        should_stop, reason = await self._should_truly_stop_questionnaire()
                        if should_stop:
                            logger.info(f"🎉 检测到真正的问卷完成: {reason}")
                            break
                        else:
                            logger.warning(f"🚫 检测到过早完成，继续执行: {reason}")
                            # 重置done状态
                            if self.state.history.history:
                                last_action = self.state.history.history[-1]
                                if hasattr(last_action, 'result'):
                                    # 移除done结果
                                    last_action.result = [r for r in last_action.result 
                                                        if not (hasattr(r, 'extracted_content') and 
                                                               r.extracted_content and 
                                                               'done' in r.extracted_content.lower())]
                            continue
                    else:
                        # 原始逻辑
                        break

            # 🚀 永不放弃模式：即使达到最大步数也不算失败
            if self.never_give_up_mode and self.questionnaire_mode:
                success_message = f'问卷任务已执行{max_steps}步，已尽最大努力完成答题'
                logger.info(f'✅ {success_message}')

            return self.state.history

        except Exception as e:
            logger.error(f"❌ Agent执行失败: {e}")
            return self.state.history
    
    async def _is_truly_completed(self) -> bool:
        """🎯 检查是否真正完成问卷（保留兼容性）"""
        should_stop, _ = await self._should_truly_stop_questionnaire()
        return should_stop 