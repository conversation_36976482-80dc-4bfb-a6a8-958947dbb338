"""
Browser package initialization
"""

from .dom import DOMElement, DOMOperations
from .custom_browser import <PERSON><PERSON><PERSON><PERSON> as Browser
from .human_input import HumanLikeInput

# 创建一个简单的BrowserConfig类
class BrowserConfig:
    def __init__(self, config=None):
        config = config or {}
        self.headless = config.get('headless', False)
        self.disable_security = config.get('disable_security', True)

__all__ = ['DOMElement', 'DOMOperations', 'Browser', 'BrowserConfig', 'HumanLikeInput']
