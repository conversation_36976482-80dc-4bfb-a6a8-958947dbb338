"""
DOM operations and element handling
"""

from typing import Optional, List, Dict, Any
import logging

logger = logging.getLogger(__name__)

class DOMElement:
    """Represents a DOM element with enhanced operations"""
    
    def __init__(self, element: Any, page: Any):
        self.element = element
        self.page = page
        
    async def get_text(self) -> str:
        """Get element text content"""
        try:
            return await self.element.text_content()
        except Exception as e:
            logger.error(f"Failed to get text: {e}")
            return ""
            
    async def get_value(self) -> str:
        """Get element value"""
        try:
            return await self.element.get_attribute("value") or ""
        except Exception as e:
            logger.error(f"Failed to get value: {e}")
            return ""
            
    async def click(self, force: bool = False):
        """Click the element with optional force"""
        try:
            if force:
                await self.element.click(force=True)
            else:
                await self.element.click()
        except Exception as e:
            logger.error(f"Failed to click: {e}")
            
    async def type(self, text: str, delay: int = 100):
        """Type text into element with delay"""
        try:
            await self.element.type(text, delay=delay)
        except Exception as e:
            logger.error(f"Failed to type: {e}")
            
    async def select_option(self, value: str):
        """Select option by value"""
        try:
            await self.element.select_option(value)
        except Exception as e:
            logger.error(f"Failed to select option: {e}")

class DOMOperations:
    """DOM operation utilities"""
    
    def __init__(self, page: Any):
        self.page = page
        
    async def find_element(self, selector: str) -> Optional[DOMElement]:
        """Find single element by selector"""
        try:
            element = await self.page.query_selector(selector)
            return DOMElement(element, self.page) if element else None
        except Exception as e:
            logger.error(f"Failed to find element: {e}")
            return None
            
    async def find_elements(self, selector: str) -> List[DOMElement]:
        """Find all elements matching selector"""
        try:
            elements = await self.page.query_selector_all(selector)
            return [DOMElement(el, self.page) for el in elements]
        except Exception as e:
            logger.error(f"Failed to find elements: {e}")
            return []
            
    async def get_element_text(self, selector: str) -> str:
        """Get text of element by selector"""
        element = await self.find_element(selector)
        if element:
            return await element.get_text()
        return ""
        
    async def get_element_value(self, selector: str) -> str:
        """Get value of element by selector"""
        element = await self.find_element(selector)
        if element:
            return await element.get_value()
        return ""
        
    async def click_element(self, selector: str, force: bool = False):
        """Click element by selector"""
        element = await self.find_element(selector)
        if element:
            await element.click(force=force)
            
    async def type_text(self, selector: str, text: str, delay: int = 100):
        """Type text into element by selector"""
        element = await self.find_element(selector)
        if element:
            await element.type(text, delay=delay)
            
    async def select_option(self, selector: str, value: str):
        """Select option in element by selector"""
        element = await self.find_element(selector)
        if element:
            await element.select_option(value) 