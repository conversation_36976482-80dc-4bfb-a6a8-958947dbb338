"""人类式输入模块"""
import random
import asyncio
import logging
from typing import Any, Optional, Tuple, Union

logger = logging.getLogger(__name__)

class HumanLikeInput:
    """人类式输入类 - 模拟真实用户的输入行为"""
    
    def __init__(self, page: Optional[Any] = None):
        """初始化人类式输入
        
        Args:
            page: 浏览器页面实例
        """
        self.page = page
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # 输入速度配置（毫秒）
        self.typing_speed = {
            "min": 50,  # 最小按键间隔
            "max": 150,  # 最大按键间隔
            "pause_min": 200,  # 最小暂停时间
            "pause_max": 500   # 最大暂停时间
        }
        
        # 鼠标移动配置
        self.mouse_speed = {
            "min": 800,  # 最小移动速度（像素/秒）
            "max": 1500  # 最大移动速度（像素/秒）
        }
        
        # 点击配置（毫秒）
        self.click_timing = {
            "down_min": 50,   # 最小按下时间
            "down_max": 150,  # 最大按下时间
            "up_min": 50,     # 最小释放时间
            "up_max": 150     # 最大释放时间
        }
        
    def set_page(self, page: Any):
        """设置页面实例
        
        Args:
            page: 浏览器页面实例
        """
        self.page = page
        
    async def type(self, element: Any, text: str):
        """模拟人类输入文本
        
        Args:
            element: 目标元素
            text: 要输入的文本
        """
        try:
            # 聚焦元素
            await element.focus()
            await self._random_sleep(200, 500)
            
            # 逐字输入
            for char in text:
                # 输入字符
                await element.type(char)
                
                # 随机延迟
                delay = random.randint(self.typing_speed["min"], self.typing_speed["max"])
                await asyncio.sleep(delay / 1000)  # 转换为秒
                
                # 随机暂停（模拟思考）
                if random.random() < 0.1:  # 10%概率暂停
                    pause = random.randint(self.typing_speed["pause_min"], 
                                        self.typing_speed["pause_max"])
                    await asyncio.sleep(pause / 1000)
                    
        except Exception as e:
            self.logger.error(f"❌ 输入文本失败: {e}")
            raise
            
    async def click(self, element: Any):
        """模拟人类点击元素
        
        Args:
            element: 目标元素
        """
        try:
            if not self.page:
                raise Exception("未设置页面实例")
                
            # 获取元素位置
            box = await element.bounding_box()
            if not box:
                raise Exception("无法获取元素位置")
                
            # 计算点击位置（随机偏移）
            x = box["x"] + box["width"] * random.uniform(0.2, 0.8)
            y = box["y"] + box["height"] * random.uniform(0.2, 0.8)
            
            # 移动鼠标
            await self._move_mouse_to(x, y)
            
            # 点击元素
            down_time = random.randint(self.click_timing["down_min"], 
                                     self.click_timing["down_max"])
            up_time = random.randint(self.click_timing["up_min"], 
                                   self.click_timing["up_max"])
            
            await element.click(delay=down_time)
            await asyncio.sleep(up_time / 1000)
            
        except Exception as e:
            self.logger.error(f"❌ 点击元素失败: {e}")
            raise
            
    async def scroll(self, x: int = 0, y: int = 0):
        """模拟人类滚动页面
        
        Args:
            x: 水平滚动距离
            y: 垂直滚动距离
        """
        try:
            if not self.page:
                raise Exception("未设置页面实例")
                
            # 计算滚动步数
            steps = random.randint(5, 10)
            x_step = x / steps
            y_step = y / steps
            
            # 分步滚动
            for _ in range(steps):
                # 滚动一步
                await self.page.mouse.wheel(x_step, y_step)
                
                # 随机延迟
                delay = random.randint(50, 150)
                await asyncio.sleep(delay / 1000)
                
        except Exception as e:
            self.logger.error(f"❌ 滚动页面失败: {e}")
            raise
            
    async def _move_mouse_to(self, x: float, y: float):
        """模拟人类移动鼠标
        
        Args:
            x: 目标X坐标
            y: 目标Y坐标
        """
        try:
            if not self.page:
                raise Exception("未设置页面实例")
                
            # 获取当前鼠标位置
            current = await self.page.mouse.position()
            start_x, start_y = current["x"], current["y"]
            
            # 计算移动距离
            distance = ((x - start_x) ** 2 + (y - start_y) ** 2) ** 0.5
            
            # 计算移动时间
            speed = random.uniform(self.mouse_speed["min"], self.mouse_speed["max"])
            duration = distance / speed
            
            # 生成移动路径点
            points = self._generate_mouse_path((start_x, start_y), (x, y))
            
            # 执行移动
            time_per_point = duration / len(points)
            for point_x, point_y in points:
                await self.page.mouse.move(point_x, point_y)
                await asyncio.sleep(time_per_point)
                
        except Exception as e:
            self.logger.error(f"❌ 移动鼠标失败: {e}")
            raise
            
    def _generate_mouse_path(self, start: Tuple[float, float], 
                           end: Tuple[float, float], 
                           control_points: int = 3) -> list:
        """生成贝塞尔曲线鼠标移动路径
        
        Args:
            start: 起始坐标
            end: 目标坐标
            control_points: 控制点数量
            
        Returns:
            list: 路径点列表
        """
        # 生成控制点
        points = [start]
        for _ in range(control_points):
            # 在起点和终点之间随机生成控制点
            x = random.uniform(start[0], end[0])
            y = random.uniform(start[1], end[1])
            # 添加随机偏移
            x += random.uniform(-20, 20)
            y += random.uniform(-20, 20)
            points.append((x, y))
        points.append(end)
        
        # 生成贝塞尔曲线点
        path_points = []
        steps = 50  # 路径点数量
        
        for t in range(steps + 1):
            t = t / steps
            x = y = 0
            n = len(points) - 1
            
            for i, point in enumerate(points):
                # 计算贝塞尔曲线点
                coefficient = (
                    self._factorial(n) / 
                    (self._factorial(i) * self._factorial(n - i))
                ) * (t ** i) * ((1 - t) ** (n - i))
                
                x += coefficient * point[0]
                y += coefficient * point[1]
                
            path_points.append((x, y))
            
        return path_points
        
    def _factorial(self, n: int) -> int:
        """计算阶乘
        
        Args:
            n: 要计算阶乘的数
            
        Returns:
            int: 阶乘结果
        """
        if n == 0:
            return 1
        else:
            return n * self._factorial(n - 1)
            
    async def _random_sleep(self, min_ms: int, max_ms: int):
        """随机延迟
        
        Args:
            min_ms: 最小延迟（毫秒）
            max_ms: 最大延迟（毫秒）
        """
        delay = random.randint(min_ms, max_ms)
        await asyncio.sleep(delay / 1000) 