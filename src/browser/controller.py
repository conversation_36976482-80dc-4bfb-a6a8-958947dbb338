"""浏览器控制器模块"""
import logging
from typing import Dict, Any, Optional, List
import time
import asyncio

from src.browser.dom import DOMOperations
from src.browser.human_input import HumanLikeInput
from src.config import Config

logger = logging.getLogger(__name__)

class CustomController:
    """自定义浏览器控制器"""
    
    def __init__(self, human_input: HumanLikeInput, config: Config, 
                 force_create: bool = False, emergency_mode: bool = False):
        """初始化控制器
        
        Args:
            human_input: 人类式输入代理
            config: 配置对象
            force_create: 是否强制创建
            emergency_mode: 是否为紧急模式
        """
        self.human_input = human_input
        self.config = config
        self.force_create = force_create
        self.emergency_mode = emergency_mode
        self.dom_operations = DOMOperations()
        
        # 初始化日志
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
    async def execute_questionnaire(self, browser: Any, prompt: Optional[str] = None) -> Dict:
        """执行问卷填写
        
        Args:
            browser: 浏览器实例
            prompt: 提示词
            
        Returns:
            Dict: 执行结果
        """
        try:
            # 等待页面加载
            await self._wait_for_page_stable(browser)
            
            # 获取所有表单元素
            elements = await self._get_form_elements(browser)
            if not elements:
                raise Exception("未找到表单元素")
                
            # 按顺序填写每个表单元素
            for element in elements:
                await self._fill_form_element(browser, element, prompt)
                
            # 提交表单
            await self._submit_form(browser)
            
            return {"success": True, "message": "问卷填写完成"}
            
        except Exception as e:
            self.logger.error(f"❌ 执行问卷填写失败: {e}")
            return {"success": False, "error": str(e)}
            
    async def execute_action(self, action: Dict) -> Dict:
        """执行浏览器操作
        
        Args:
            action: 操作指令
            
        Returns:
            Dict: 执行结果
        """
        try:
            action_type = action.get("type")
            params = action.get("params", {})
            
            if action_type == "click":
                await self._click_element(params)
            elif action_type == "input":
                await self._input_text(params)
            elif action_type == "select":
                await self._select_option(params)
            elif action_type == "scroll":
                await self._scroll_page(params)
            elif action_type == "wait":
                await self._wait_for_element(params)
            else:
                raise Exception(f"不支持的操作类型: {action_type}")
                
            return {"success": True, "message": f"执行{action_type}操作成功"}
            
        except Exception as e:
            self.logger.error(f"❌ 执行操作失败: {e}")
            return {"success": False, "error": str(e)}
            
    async def _wait_for_page_stable(self, browser: Any, timeout: int = 180, check_interval: int = 3):
        """等待页面稳定
        
        Args:
            browser: 浏览器实例
            timeout: 超时时间（秒）
            check_interval: 检查间隔（秒）
        """
        start_time = time.time()
        stable_count = 0
        last_title = None
        last_element_count = 0
        
        while True:
            current_time = time.time()
            elapsed = int(current_time - start_time)
            
            if elapsed > timeout:
                raise Exception(f"等待页面稳定超时（{timeout}秒）")
                
            # 获取当前状态
            title = await browser.title()
            elements = await self._get_form_elements(browser)
            element_count = len(elements)
            
            # 检查状态是否变化
            if title == last_title and element_count == last_element_count:
                stable_count += 1
                self.logger.info(f"✅ 页面稳定检查 {stable_count}/3")
                if stable_count >= 3:
                    self.logger.info(f"🎉 页面已稳定加载完成 (等待了{elapsed}秒)")
                    break
            else:
                stable_count = 0
                self.logger.info(f"🔄 等待检查 {elapsed}s/{timeout}s: 标题='{title}', 表单元素={element_count}")
                
            # 更新上次状态
            last_title = title
            last_element_count = element_count
            
            # 等待下次检查
            await asyncio.sleep(check_interval)
            
    async def _get_form_elements(self, browser: Any) -> List[Dict]:
        """获取表单元素
        
        Args:
            browser: 浏览器实例
            
        Returns:
            List[Dict]: 表单元素列表
        """
        try:
            # 获取页面上所有元素
            elements = await browser.query_selector_all("*")
            
            # 过滤出表单元素
            form_elements = []
            for element in elements:
                tag_name = await element.get_property("tagName")
                tag_name = await tag_name.json_value()
                
                if tag_name.lower() in ["input", "select", "textarea"]:
                    # 获取元素属性
                    element_info = {
                        "tagName": tag_name,
                        "type": await (await element.get_property("type")).json_value(),
                        "name": await (await element.get_property("name")).json_value(),
                        "value": await (await element.get_property("value")).json_value(),
                        "element": element
                    }
                    form_elements.append(element_info)
                    
            return form_elements
            
        except Exception as e:
            self.logger.error(f"❌ 获取表单元素失败: {e}")
            return []
            
    async def _fill_form_element(self, browser: Any, element: Dict, prompt: Optional[str] = None):
        """填写表单元素
        
        Args:
            browser: 浏览器实例
            element: 元素信息
            prompt: 提示词
        """
        try:
            element_type = element.get("type", "").lower()
            
            if element_type in ["text", "email", "tel", "number"]:
                # 文本输入
                await self._input_text(element)
            elif element_type == "radio":
                # 单选按钮
                await self._select_radio(element)
            elif element_type == "checkbox":
                # 复选框
                await self._toggle_checkbox(element)
            elif element.get("tagName").lower() == "select":
                # 下拉选择
                await self._select_option(element)
            elif element.get("tagName").lower() == "textarea":
                # 文本区域
                await self._input_text(element)
                
        except Exception as e:
            self.logger.error(f"❌ 填写表单元素失败: {e}")
            
    async def _submit_form(self, browser: Any):
        """提交表单
        
        Args:
            browser: 浏览器实例
        """
        try:
            # 查找提交按钮
            submit_button = await browser.query_selector("button[type='submit']")
            if not submit_button:
                submit_button = await browser.query_selector("input[type='submit']")
                
            if submit_button:
                # 使用人类式点击
                await self.human_input.click(submit_button)
                self.logger.info("✅ 表单提交成功")
            else:
                raise Exception("未找到提交按钮")
                
        except Exception as e:
            self.logger.error(f"❌ 提交表单失败: {e}")
            raise
            
    async def _click_element(self, params: Dict):
        """点击元素
        
        Args:
            params: 点击参数
        """
        try:
            element = params.get("element")
            if not element:
                raise Exception("未提供目标元素")
                
            await self.human_input.click(element)
            
        except Exception as e:
            self.logger.error(f"❌ 点击元素失败: {e}")
            raise
            
    async def _input_text(self, params: Dict):
        """输入文本
        
        Args:
            params: 输入参数
        """
        try:
            element = params.get("element")
            text = params.get("text")
            
            if not element or not text:
                raise Exception("未提供目标元素或文本内容")
                
            await self.human_input.type(element, text)
            
        except Exception as e:
            self.logger.error(f"❌ 输入文本失败: {e}")
            raise
            
    async def _select_option(self, params: Dict):
        """选择选项
        
        Args:
            params: 选择参数
        """
        try:
            element = params.get("element")
            value = params.get("value")
            
            if not element or not value:
                raise Exception("未提供目标元素或选项值")
                
            await element.select_option(value=value)
            
        except Exception as e:
            self.logger.error(f"❌ 选择选项失败: {e}")
            raise
            
    async def _scroll_page(self, params: Dict):
        """滚动页面
        
        Args:
            params: 滚动参数
        """
        try:
            x = params.get("x", 0)
            y = params.get("y", 0)
            
            await self.human_input.scroll(x, y)
            
        except Exception as e:
            self.logger.error(f"❌ 滚动页面失败: {e}")
            raise
            
    async def _wait_for_element(self, params: Dict):
        """等待元素
        
        Args:
            params: 等待参数
        """
        try:
            selector = params.get("selector")
            timeout = params.get("timeout", 30000)
            
            if not selector:
                raise Exception("未提供目标选择器")
                
            await self.page.wait_for_selector(selector, timeout=timeout)
            
        except Exception as e:
            self.logger.error(f"❌ 等待元素失败: {e}")
            raise 