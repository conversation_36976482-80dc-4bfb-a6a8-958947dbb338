import pdb
import re

import pyperclip
from typing import Optional, Type, Callable, Dict, Any, Union, Awaitable, TypeVar, List, Tuple
from pydantic import BaseModel
from browser_use.agent.views import ActionResult, ActionModel
from browser_use.browser.context import BrowserContext
from browser_use.controller.service import Controller, DoneAction
from browser_use.controller.registry.service import Registry, RegisteredAction
from browser_use.browser.dom import DOMElementNode
# 移除 main_content_extractor 导入，使用内置的内容提取方法
from browser_use.controller.views import (
    ClickElementAction,
    DoneAction,
    ExtractPageContentAction,
    GoToUrlAction,
    InputTextAction,
    OpenTabAction,
    ScrollAction,
    SearchGoogleAction,
    SendKeysAction,
    SwitchTabAction,
)
import logging
import inspect
import asyncio
import os
import random
from langchain_core.language_models.chat_models import BaseChatModel
from browser_use.agent.views import ActionModel, ActionResult
import time
import json
import hashlib

from src.utils.mcp_client import create_tool_param_model, setup_mcp_client_and_tools

from browser_use.utils import time_execution_sync

logger = logging.getLogger(__name__)

Context = TypeVar('Context')


class CustomController(Controller):
    def __init__(self, exclude_actions: list[str] = [],
                 output_model: Optional[Type[BaseModel]] = None,
                 ask_assistant_callback: Optional[Union[Callable[[str, BrowserContext], Dict[str, Any]], Callable[
                     [str, BrowserContext], Awaitable[Dict[str, Any]]]]] = None,
                 ):
        # 🔥 修复：使用正确的基类初始化参数
        super().__init__(config={'exclude_actions': exclude_actions, 'output_model': output_model})

        # 🔥 添加缺失的registry属性
        from browser_use.controller.registry.service import Registry
        self.registry = Registry()

        self._register_custom_actions()
        self.ask_assistant_callback = ask_assistant_callback
        self.mcp_client = None
        self.mcp_server_config = None
        
        # 🔥 核心修改：简化并优化核心功能
        self.digital_human_info = {}  # 数字人信息存储
        
        # 🎯 页面跳转智能检测状态
        self.page_transition_state = {
            'last_url': '',
            'last_title': '',
            'transition_count': 0,
            'waiting_for_load': False,
            'load_start_time': None,
            'max_wait_time': 30  # 最大等待30秒
        }
        
        # 🎯 问卷答题状态追踪
        self.questionnaire_state = {
            'answered_questions': set(),
            'current_page_questions': [],
            'answer_consistency_map': {},
            'page_completion_status': {}
        }
        
        # 🛡️ 页面恢复引擎状态 - 修复缺失的属性
        self.page_recovery_state = {
            'recovery_attempts': 0,
            'last_stable_timestamp': time.time(),
            'loading_start_time': None,
            'loading_detection_count': 0,
            'questionnaire_progress': {},
            'emergency_recovery_enabled': False
        }
        
        # 🎯 已回答问题追踪 - 修复缺失的属性
        self.answered_questions = set()
        self.question_hashes = {}
        
        # 🔍 页面URL追踪 - 修复缺失的属性
        self._last_page_url = ""
        
        # 🔥 核心重构：直接在实例上维护数字人信息和浏览器上下文
        self._digital_human_info = {}
        self._browser_context_cache = None
        self._original_act_method = None
        
        # 🔥 新增：智能区域选择引擎状态
        self.nationality_region_engine = {
            'detected_page_structure': {},
            'selected_options': set(),
            'complex_page_cache': {},
            'multi_select_detection': False
        }
        
        # 🔥 新增：智能题型处理引擎集成 - 站在系统架构层级的全面增强
        self._initialize_intelligent_engines()
        
        # 🔥 注册核心增强动作
        self._register_core_enhancements()
        
        # 🚀 注册原生智能输入引擎
        try:
            self.register_native_intelligent_input_engine()
        except AttributeError:
            pass  # 方法不存在时跳过

        # 🌍 注册智能国籍区域选择引擎
        try:
            self.register_intelligent_nationality_region_engine()
        except AttributeError:
            pass  # 方法不存在时跳过

        # 🔥 新增：注册增强版智能选择系统
        try:
            self._register_enhanced_intelligent_selection_system()
        except AttributeError:
            pass  # 方法不存在时跳过
        
        # 🎯 新增：注册智能下拉框引擎 - 解决职业选择等问题
        try:
            self.register_intelligent_dropdown_engine()
        except Exception as dropdown_error:
            logger.warning(f"⚠️ 智能下拉框引擎注册失败: {dropdown_error}")
        
        logger.info("✅ CustomController核心引擎已激活 - 重构完成，支持智能状态管理")
        # 🔥 核心新增：智能链接过滤器和标签页管理器
        try:
            self._initialize_smart_link_filter()
            self._initialize_tab_manager()
            logger.info('🛡️ 智能链接过滤和标签页管理系统已激活')
        except AttributeError:
            logger.info('⚠️ 智能链接过滤和标签页管理系统跳过（方法不存在）')

    def _initialize_intelligent_engines(self):
        """🧠 强制初始化所有智能处理引擎 - 确保100%加载成功"""
        logger.info("🔥 开始强制初始化所有智能引擎...")
        
        # 智能引擎状态
        self.engine_status = {
            'nationality_engine': False,
            'radio_handler': False,
            'drag_drop_engine': False,
            'enhanced_radio_system': False,
            'conservative_radio_handler': False  # 新增保守版本
        }
        
        # 1. 智能国籍区域选择引擎
        for module_name in ['intelligent_nationality_region_engine', 'IntelligentNationalityRegionEngine']:
            try:
                if module_name == 'intelligent_nationality_region_engine':
                    from intelligent_nationality_region_engine import IntelligentNationalityRegionEngine
                    self.nationality_engine = IntelligentNationalityRegionEngine()
                    self.engine_status['nationality_engine'] = True
                    logger.info("✅ 智能国籍区域选择引擎已加载")
                    break
            except (ImportError, AttributeError) as e:
                logger.debug(f"⚠️ 尝试加载 {module_name} 失败: {e}")
                continue
        
        if not self.engine_status['nationality_engine']:
            self.nationality_engine = None
            logger.warning("⚠️ 智能国籍区域选择引擎不可用")
        
        # 2. 自定义单选按钮处理器（多版本兼容）
        for module_name in ['custom_radio_button_handler', 'enhanced_custom_radio_system', 'conservative_custom_radio_handler']:
            try:
                if module_name == 'custom_radio_button_handler':
                    from custom_radio_button_handler import CustomRadioButtonHandler
                    self.radio_handler = CustomRadioButtonHandler()
                    self.engine_status['radio_handler'] = True
                    logger.info("✅ 自定义单选按钮处理器已加载")
                elif module_name == 'enhanced_custom_radio_system':
                    from enhanced_custom_radio_system import EnhancedCustomRadioSystem
                    self.enhanced_radio_system = EnhancedCustomRadioSystem()
                    self.engine_status['enhanced_radio_system'] = True
                    logger.info("✅ 增强自定义Radio系统已加载")
                elif module_name == 'conservative_custom_radio_handler':
                    from conservative_custom_radio_handler import ConservativeCustomRadioHandler
                    self.conservative_radio_handler = ConservativeCustomRadioHandler()
                    self.engine_status['conservative_radio_handler'] = True
                    logger.info("✅ 保守版自定义Radio处理器已加载")
            except (ImportError, AttributeError) as e:
                logger.debug(f"⚠️ 尝试加载 {module_name} 失败: {e}")
                continue
        
        # 设置默认值
        if not self.engine_status['radio_handler']:
            self.radio_handler = None
        if not self.engine_status['enhanced_radio_system']:
            self.enhanced_radio_system = None
        if not self.engine_status['conservative_radio_handler']:
            self.conservative_radio_handler = None
        
        # 3. 智能拖拽排序引擎
        try:
            from intelligent_drag_drop_ranking_engine import IntelligentDragDropRankingEngine
            self.drag_drop_engine = IntelligentDragDropRankingEngine()
            self.engine_status['drag_drop_engine'] = True
            logger.info("✅ 智能拖拽排序引擎已加载")
        except (ImportError, AttributeError) as e:
            self.drag_drop_engine = None
            logger.warning(f"⚠️ 智能拖拽排序引擎不可用: {e}")
        
        # 统计结果
        active_engines = sum(self.engine_status.values())
        total_engines = len(self.engine_status)
        logger.info(f"🧠 智能引擎强制初始化完成: {active_engines}/{total_engines} 个引擎可用")
        logger.info(f"📊 详细引擎状态: {self.engine_status}")

    def _register_custom_actions(self):
        """注册自定义动作到控制器注册表"""
        try:
            logger.info("🔍 注册智能选项搜索引擎动作...")
            
            # 注意：动作注册通过装饰器方式进行，这里只是初始化标记
            logger.info("✅ 智能选项搜索引擎 + 页面恢复引擎动作将通过装饰器注册")
            
        except Exception as e:
            logger.error(f"❌ 注册智能搜索引擎动作失败: {e}")

    def _initialize_smart_link_filter(self):
        """🛡️ 初始化智能链接过滤器"""
        class SmartLinkFilter:
            def __init__(self):
                # 🚫 绝对禁止的链接模式
                self.forbidden_patterns = [
                    r'privacy.*policy', r'隐私.*政策', r'cookie.*policy', r'cookie.*通知',
                    r'terms.*service', r'服务.*条款', r'evidon\.com', r'trustpilot\.com',
                    r'facebook\.com', r'twitter\.com', r'help.*center', r'support',
                    r'contact.*us', r'about.*us', r'download', r'app.*store'
                ]
                
                # ✅ 问卷相关的允许模式
                self.allowed_patterns = [
                    r'survey', r'questionnaire', r'问卷', r'调查', r'continue', r'继续',
                    r'next', r'下一步', r'submit', r'提交', r'start', r'开始'
                ]
            
            def should_allow_click(self, element_text: str, element_href: str = "", element_tag: str = "") -> dict:
                """核心过滤逻辑"""
                combined_text = f"{element_text} {element_href}".lower()
                
                # 🚫 检查禁止模式
                for pattern in self.forbidden_patterns:
                    if re.search(pattern, combined_text, re.IGNORECASE):
                        return {
                            "allowed": False,
                            "reason": f"匹配禁止模式: {pattern}",
                            "risk_level": "high"
                        }
                
                # ✅ 检查允许模式  
                for pattern in self.allowed_patterns:
                    if re.search(pattern, combined_text, re.IGNORECASE):
                        return {
                            "allowed": True,
                            "reason": f"匹配问卷模式: {pattern}",
                            "confidence": "high"
                        }
                
                # 🔍 特殊检查：国家选择链接
                if element_tag == 'a' and any(country in combined_text for country in ['中国', 'china', 'philippines']):
                    return {
                        "allowed": True,
                        "reason": "国家选择链接",
                        "confidence": "medium"
                    }
                
                # 🚨 默认策略：外部链接禁止，内部元素允许
                if 'http' in element_href and element_href.startswith('http'):
                    return {
                        "allowed": False,
                        "reason": "外部链接，安全考虑",
                        "risk_level": "high"
                    }
                
                return {"allowed": True, "reason": "内部元素，允许点击", "confidence": "medium"}
        
        self.smart_link_filter = SmartLinkFilter()
        logger.info("✅ 智能链接过滤器初始化完成")

    def _initialize_tab_manager(self):
        """🔍 初始化标签页管理器"""
        class TabManager:
            def __init__(self, controller):
                self.controller = controller
                self.main_tab_url_pattern = None
                self.allowed_tab_count = 1
            
            async def monitor_and_manage_tabs(self, browser_context):
                """监控和管理标签页"""
                try:
                    # 获取所有标签页 - 使用正确的browser-use API
                    page = await browser_context.get_current_page()
                    playwright_context = page.context
                    pages = playwright_context.pages
                    
                    if len(pages) > self.allowed_tab_count:
                        logger.warning(f"🚨 检测到多余标签页: {len(pages)}个，开始清理")
                        
                        # 识别主问卷标签页
                        main_page = await self._identify_main_questionnaire_page(pages, browser_context)
                        
                        # 关闭其他标签页
                        closed_count = 0
                        for page in pages:
                            if page != main_page:
                                try:
                                    await page.close()
                                    closed_count += 1
                                    logger.info(f"✅ 关闭多余标签页: {page.url}")
                                except Exception as e:
                                    logger.warning(f"⚠️ 关闭标签页失败: {e}")
                        
                        if closed_count > 0:
                            logger.info(f"🧹 标签页清理完成，关闭了{closed_count}个多余标签页")
                            
                            # 确保焦点在主标签页
                            await main_page.bring_to_front()
                            
                        return {"cleaned": True, "closed_count": closed_count}
                    
                    return {"cleaned": False, "message": "标签页数量正常"}
                    
                except Exception as e:
                    logger.error(f"❌ 标签页管理失败: {e}")
                    return {"cleaned": False, "error": str(e)}
            
            async def _identify_main_questionnaire_page(self, pages, browser_context):
                """识别主问卷标签页"""
                try:
                    questionnaire_indicators = ['survey', 'questionnaire', '问卷', '调查', 'form']
                    
                    for page in pages:
                        try:
                            url = page.url.lower()
                            title = await page.title()
                            
                            # 检查URL和标题是否包含问卷关键词
                            if any(indicator in url or indicator in title.lower() for indicator in questionnaire_indicators):
                                logger.info(f"🎯 识别到主问卷页面: {url}")
                                return page
                        except:
                            continue
                    
                    # 如果没有明确的问卷页面，返回第一个页面
                    return pages[0] if pages else None
                    
                except Exception as e:
                    logger.error(f"❌ 识别主页面失败: {e}")
                    return pages[0] if pages else None
        
        self.tab_manager = TabManager(self)
        logger.info("✅ 标签页管理器初始化完成")

    def _register_core_enhancements(self):
        """🔥 注册核心增强方法 - 最关键的控制点"""
        
        # 🎯 核心修改：重写click_element_by_index，集成所有智能功能
        @self.registry.action(
            'click_element_by_index - ULTIMATE OVERRIDE WITH HIGHEST PRIORITY',
        )
        async def click_element_by_index(index: int, browser: BrowserContext) -> ActionResult:
            """🎯 终极智能点击 - 集成链接过滤、标签页管理、智能答题的统一入口"""
            try:
                logger.info(f"🎯 终极智能点击启动 - 元素索引: {index}")
                
                # 🔍 第一步：获取元素信息
                selector_map = await browser.get_selector_map()
                if index not in selector_map:
                    return ActionResult(error=f"Element index {index} not found")
                
                dom_element = selector_map[index]
                element_text = getattr(dom_element, 'text', '') or ''
                element_tag = getattr(dom_element, 'tag_name', '')
                element_href = getattr(dom_element, 'href', '') or ''
                
                logger.info(f"🔍 元素分析: text='{element_text[:50]}...', tag='{element_tag}', href='{element_href[:50]}...'")
                
                # 🛡️ 第二步：智能链接过滤检查
                if hasattr(self, 'smart_link_filter'):
                    filter_result = self.smart_link_filter.should_allow_click(element_text, element_href, element_tag)
                    
                    if not filter_result.get('allowed', True):
                        logger.warning(f"🚫 链接过滤器阻止点击: {filter_result['reason']}")
                        
                        # 🔍 如果是国家选择页面，尝试智能处理
                        if self._is_country_selection_context(element_text, await browser.get_current_page()):
                            logger.info("🗺️ 检测到国家选择上下文，启动智能国籍引擎")
                            nationality_result = await self._handle_intelligent_nationality_selection(browser)
                            if nationality_result.get('success'):
                                return ActionResult(
                                    extracted_content=f"智能国籍选择: {nationality_result['message']}",
                                    include_in_memory=True
                                )
                        
                        return ActionResult(
                            extracted_content=f"已过滤危险链接: {element_text} - {filter_result['reason']}",
                            include_in_memory=True
                        )
                
                # 🔍 第三步：标签页监控（点击前）
                if hasattr(self, 'tab_manager'):
                    await self.tab_manager.monitor_and_manage_tabs(browser)
                
                # 🎯 第四步：智能题型检测和处理 - 增强版全页面分析
                if hasattr(self, 'digital_human_info') and self.digital_human_info:
                    # 🔍 首先进行全页面智能分析
                    page = await browser.get_current_page()
                    page_analysis_result = await self._analyze_full_page_context(page, browser)
                    
                    if page_analysis_result.get('is_nationality_page'):
                        logger.info("🗺️ 检测到国籍选择页面，启动智能国籍引擎（优先级最高）")
                        nationality_result = await self._handle_intelligent_nationality_selection(browser)
                        
                        if nationality_result.get('success'):
                            # 🔍 点击后再次检查标签页
                            await asyncio.sleep(1)
                            if hasattr(self, 'tab_manager'):
                                await self.tab_manager.monitor_and_manage_tabs(browser)
                            
                            return ActionResult(
                                extracted_content=f"✅ 智能国籍选择完成: {nationality_result.get('message', '已选择合适的国籍')}",
                                include_in_memory=True
                            )
                    
                    # 如果不是国籍页面，继续原有的智能处理
                    intelligent_result = await self._execute_intelligent_question_processing(
                        index, element_text, element_tag, browser, self.digital_human_info
                    )
                    
                    if intelligent_result.get('success'):
                        # 🔍 点击后再次检查标签页
                        await asyncio.sleep(1)  # 等待可能的页面跳转
                        if hasattr(self, 'tab_manager'):
                            await self.tab_manager.monitor_and_manage_tabs(browser)
                        
                        return ActionResult(
                            extracted_content=intelligent_result.get('message', '智能处理成功'),
                            include_in_memory=True
                        )
                
                # 🔧 第五步：安全回退点击
                logger.info(f"🛡️ 执行安全回退点击")
                page = await browser.get_current_page()
                xpath = '//' + dom_element.xpath
                element_locator = page.locator(xpath)
                
                # 确保元素可见
                await element_locator.scroll_into_view_if_needed()
                await asyncio.sleep(random.uniform(0.2, 0.5))  # 人类化延迟
                
                await element_locator.click()
                
                # 🔍 点击后标签页管理
                await asyncio.sleep(1)
                if hasattr(self, 'tab_manager'):
                    await self.tab_manager.monitor_and_manage_tabs(browser)
                
                return ActionResult(
                    extracted_content=f"安全点击完成: {element_text}",
                    include_in_memory=True
                )
                
            except Exception as e:
                logger.error(f"❌ 终极智能点击失败: {e}")
                return ActionResult(error=f"终极智能点击失败: {e}")

        # 🎯 核心功能1：智能页面跳转等待
        @self.registry.action(
            'Intelligent page transition detection and waiting - core WebUI enhancement',
        )
        async def intelligent_wait_for_page_transition(browser: BrowserContext, max_wait_seconds: int = 30) -> ActionResult:
            """🎯 智能页面跳转检测和等待 - 确保多次跳转后仍能正常答题"""
            try:
                page = await browser.get_current_page()
                current_url = page.url
                current_title = await page.title()
                
                logger.info(f"🔄 开始智能页面跳转检测 - 当前页面: {current_title}")
                
                # 更新跳转状态
                self.page_transition_state.update({
                    'last_url': current_url,
                    'last_title': current_title,
                    'waiting_for_load': True,
                    'load_start_time': time.time()
                })
                
                # 智能等待页面稳定
                stable_count = 0
                required_stable_count = 3  # 需要连续3次检测都稳定
                
                for _ in range(max_wait_seconds * 2):  # 每0.5秒检测一次
                    await asyncio.sleep(0.5)
                    
                    try:
                        new_url = page.url
                        new_title = await page.title()
                        
                        # 检测页面是否已经稳定
                        if new_url == current_url and new_title == current_title:
                            stable_count += 1
                            if stable_count >= required_stable_count:
                                # 页面已稳定，检查是否有新的问卷内容
                                await self._detect_questionnaire_content(browser)
                                break
                        else:
                            # 页面仍在变化
                            current_url = new_url
                            current_title = new_title
                            stable_count = 0
                            self.page_transition_state['transition_count'] += 1
                            logger.info(f"🔄 检测到页面跳转 #{self.page_transition_state['transition_count']}: {new_title}")
                    
                    except Exception as e:
                        logger.warning(f"⚠️ 页面状态检测失败: {e}")
                        stable_count = 0
                
                # 更新最终状态
                self.page_transition_state['waiting_for_load'] = False
                
                msg = f"✅ 页面跳转检测完成 - 总跳转次数: {self.page_transition_state['transition_count']}"
                logger.info(msg)
                return ActionResult(extracted_content=msg, include_in_memory=True)
                
            except Exception as e:
                error_msg = f"❌ 页面跳转检测失败: {e}"
                logger.error(error_msg)
                return ActionResult(error=error_msg)

    async def _detect_questionnaire_content(self, browser_context: BrowserContext) -> Dict[str, Any]:
        """
        Detect and extract questionnaire content using built-in ExtractPageContentAction
        Returns a dictionary containing extracted content and metadata
        """
        try:
            # Use built-in ExtractPageContentAction
            extract_action = ExtractPageContentAction()
            result = await extract_action.act(browser_context)
            
            if not result.success:
                logger.warning("Failed to extract page content")
                return {
                    'success': False,
                    'content': '',
                    'questions': [],
                    'error': result.error if hasattr(result, 'error') else 'Unknown error'
                }
            
            content = result.data.get('content', '')
            if not content:
                logger.warning("No content extracted from page")
                return {
                    'success': False,
                    'content': '',
                    'questions': [],
                    'error': 'No content extracted'
                }
            
            # Process extracted content to identify questions
            questions = []
            current_page_url = await browser_context.get_current_url()
            
            # Simple question detection - can be enhanced based on specific questionnaire format
            lines = content.split('\n')
            for line in lines:
                line = line.strip()
                # Basic question detection - ends with ? or contains common question indicators
                if (line.endswith('?') or 
                    ':' in line or 
                    line.lower().startswith(('what', 'when', 'where', 'who', 'why', 'how', 'please', 'select'))):
                    
                    question_hash = self._generate_question_fingerprint(line, {
                        'url': current_page_url,
                        'context': content[:100]  # Use first 100 chars as context
                    })
                    
                    questions.append({
                        'text': line,
                        'hash': question_hash,
                        'answered': question_hash in self.answered_questions
                    })
            
            return {
                'success': True,
                'content': content,
                'questions': questions,
                'url': current_page_url
            }
            
        except Exception as e:
            logger.error(f"Error in _detect_questionnaire_content: {str(e)}")
            return {
                'success': False,
                'content': '',
                'questions': [],
                'error': str(e)
            }

    async def _extract_and_process_content(self, browser_context: BrowserContext) -> ActionResult:
        """
        Extract and process page content, updating questionnaire state
        Returns ActionResult with success/failure and extracted data
        """
        try:
            # Extract content
            content_data = await self._detect_questionnaire_content(browser_context)
            
            if not content_data['success']:
                return ActionResult(
                    success=False,
                    error=content_data.get('error', 'Failed to extract content'),
                    data=content_data
                )
            
            # Update questionnaire state
            self.questionnaire_state.update({
                'current_page_questions': content_data['questions'],
                'page_completion_status': {
                    q['hash']: q['answered'] for q in content_data['questions']
                }
            })
            
            # Track URL for page transition detection
            self.page_transition_state['last_url'] = content_data['url']
            
            return ActionResult(
                success=True,
                data={
                    'content': content_data['content'],
                    'questions': content_data['questions'],
                    'completion_status': self.questionnaire_state['page_completion_status']
                }
            )
            
        except Exception as e:
            logger.error(f"Error in _extract_and_process_content: {str(e)}")
            return ActionResult(
                success=False,
                error=str(e),
                data=None
            )

    def _should_use_intelligent_selection(self, element, text: str) -> bool:
        """🎯 判断是否应该使用智能选择"""
        # 如果没有数字人信息，不使用智能选择
        if not self.digital_human_info:
            return False
        
        # 如果文本为空或者是智能选择关键词，使用智能选择
        if not text or text.lower() in ['auto', 'intelligent', 'smart', '智能选择']:
            return True
        
        # 如果是选择类元素，使用智能选择
        if element.tag_name.lower() in ['select', 'input']:
            return True
        
        return False
    
    async def _make_intelligent_choice(self, element, original_text: str, page) -> Dict:
        """🎯 基于数字人信息做出智能选择"""
        try:
            # 获取元素的所有选项
            options = []
            
            if element.tag_name.lower() == 'select':
                # 下拉框选项
                option_elements = await page.locator(f"//{element.xpath}//option").all()
                for opt in option_elements:
                    opt_text = await opt.text_content()
                    if opt_text and opt_text.strip():
                        options.append(opt_text.strip())
            
            if not options:
                return {'success': False, 'reason': '未找到可选选项'}
            
            # 🧠 基于数字人信息进行智能匹配
            best_option = self._find_best_option_for_persona(options, self.digital_human_info)
            
            if best_option:
                return {
                    'success': True,
                    'recommended_text': best_option['text'],
                    'reason': best_option['reason']
                }
            else:
                return {'success': False, 'reason': '未找到合适的选项'}
                
        except Exception as e:
            logger.warning(f"⚠️ 智能选择决策失败: {e}")
            return {'success': False, 'reason': f'决策过程出错: {e}'}
    
    def _find_best_option_for_persona(self, options: List[str], persona_info: Dict) -> Optional[Dict]:
        """🧠 为数字人找到最佳选项"""
        try:
            # 获取数字人的关键信息
            location = persona_info.get('location', '').lower()
            nationality = persona_info.get('nationality', '').lower()
            age = persona_info.get('age', '')
            gender = persona_info.get('gender', '').lower()
            
            # 评分系统
            best_score = 0
            best_option = None
            
            for option in options:
                option_lower = option.lower()
                score = 0
                reasons = []
                
                # 地理位置匹配
                if '中国' in location or 'china' in location:
                    if any(keyword in option_lower for keyword in ['中国', 'china', 'chinese', 'cn']):
                        score += 10
                        reasons.append('地理位置匹配')
                
                # 性别匹配
                if gender:
                    if ('男' in gender or 'male' in gender) and any(keyword in option_lower for keyword in ['男', 'male', 'mr']):
                        score += 5
                        reasons.append('性别匹配')
                    elif ('女' in gender or 'female' in gender) and any(keyword in option_lower for keyword in ['女', 'female', 'ms', 'mrs']):
                        score += 5
                        reasons.append('性别匹配')
                
                # 年龄相关匹配
                try:
                    age_num = int(''.join(filter(str.isdigit, str(age))))
                    if age_num:
                        if age_num < 25 and any(keyword in option_lower for keyword in ['年轻', 'young', '学生']):
                            score += 3
                            reasons.append('年龄段匹配')
                        elif 25 <= age_num < 60 and any(keyword in option_lower for keyword in ['成年', 'adult', '工作']):
                            score += 3
                            reasons.append('年龄段匹配')
                        elif age_num >= 60 and any(keyword in option_lower for keyword in ['老年', 'senior', '退休']):
                            score += 3
                            reasons.append('年龄段匹配')
                except:
                    pass
                
                if score > best_score:
                    best_score = score
                    best_option = {
                        'text': option,
                        'score': score,
                        'reason': ', '.join(reasons) if reasons else '系统推荐'
                    }
            
            return best_option if best_score > 0 else None
            
        except Exception as e:
            logger.warning(f"⚠️ 选项匹配失败: {e}")
            return None
    
    async def _record_answer_state(self, element, answer_text: str):
        """🎯 记录答题状态，确保一致性"""
        try:
            # 生成问题的唯一标识
            question_id = f"{element.xpath}_{element.tag_name}"
            
            # 记录答案
            self.questionnaire_state['answer_consistency_map'][question_id] = {
                'answer': answer_text,
                'timestamp': time.time(),
                'element_info': {
                    'tag': element.tag_name,
                    'xpath': element.xpath
                }
            }
            
            # 添加到已回答问题集合
            self.questionnaire_state['answered_questions'].add(question_id)
            
            logger.info(f"📝 记录答题状态: {question_id} -> {answer_text}")
            
        except Exception as e:
            logger.warning(f"⚠️ 答题状态记录失败: {e}")
    
    async def _record_answer_state_enhanced(self, element_info, answer_text: str, digital_human_info: Dict):
        """🎯 【增强版】记录答题状态，确保前后一致性和智能判断记忆"""
        try:
            # 生成问题的智能指纹（忽略细微差异）
            question_fingerprint = self._generate_question_fingerprint(answer_text, element_info)
            
            # 检查是否之前回答过相同问题
            if hasattr(self, 'answer_memory_bank'):
                existing_answer = self.answer_memory_bank.get(question_fingerprint)
                if existing_answer:
                    logger.info(f"🧠 检测到重复问题，使用一致答案: {existing_answer}")
                    return existing_answer
            else:
                self.answer_memory_bank = {}
            
            # 🎯 【核心】：基于数字人信息生成智能一致的答案
            consistent_answer = self._generate_consistent_answer(answer_text, digital_human_info)
            
            # 记录到记忆库
            self.answer_memory_bank[question_fingerprint] = {
                'answer': consistent_answer,
                'original_text': answer_text,
                'digital_human_context': {
                    'nationality': digital_human_info.get('nationality', ''),
                    'location': digital_human_info.get('location', ''),
                    'name': digital_human_info.get('name', '')
                },
                'timestamp': time.time(),
                'consistency_score': 1.0
            }
            
            logger.info(f"🧠 智能答题记忆已更新: {question_fingerprint} -> {consistent_answer}")
            return consistent_answer
            
        except Exception as e:
            logger.warning(f"⚠️ 增强答题状态记录失败: {e}")
            return answer_text

    def _generate_question_fingerprint(self, question_text: str, element_info) -> str:
        """🧠 生成问题指纹，忽略选项顺序等细微差异"""
        try:
            # 提取问题核心关键词
            text_lower = question_text.lower()
            
            # 国籍相关问题指纹
            if any(keyword in text_lower for keyword in ['china', '中国', 'country', 'nationality', '国籍']):
                return "nationality_question"
            
            # 语言相关问题指纹  
            if any(keyword in text_lower for keyword in ['language', '语言', 'english', 'chinese']):
                return "language_question"
            
            # 性别相关问题指纹
            if any(keyword in text_lower for keyword in ['gender', '性别', 'male', 'female', '男', '女']):
                return "gender_question"
            
            # 年龄相关问题指纹
            if any(keyword in text_lower for keyword in ['age', '年龄', '岁']):
                return "age_question"
            
            # 默认使用文本哈希
            import hashlib
            return hashlib.md5(text_lower.encode()).hexdigest()[:16]
            
        except Exception as e:
            logger.warning(f"⚠️ 生成问题指纹失败: {e}")
            return "unknown_question"

    def _generate_consistent_answer(self, answer_text: str, digital_human_info: Dict) -> str:
        """🧠 基于数字人信息生成一致性答案"""
        try:
            text_lower = answer_text.lower()
            location = digital_human_info.get('location', '').lower()
            nationality = digital_human_info.get('nationality', '').lower()
            
            # 🌍 国籍判断逻辑增强
            if any(keyword in text_lower for keyword in ['china', '中国']):
                if '中国' in location or 'china' in location or '北京' in location or '上海' in location:
                    return answer_text  # 正确选择
                else:
                    # 寻找中国相关选项（这部分由智能选项发现引擎处理）
                    return answer_text
            
            # 🗣️ 语言判断逻辑
            if any(keyword in text_lower for keyword in ['chinese', '中文', '简体']):
                if '中国' in location or 'china' in location:
                    return answer_text
            
            return answer_text
            
        except Exception as e:
            logger.warning(f"⚠️ 生成一致性答案失败: {e}")
            return answer_text

    def set_digital_human_info(self, info: Dict[str, Any]):
        """设置数字人信息

        Args:
            info: 数字人信息字典
        """
        self.digital_human_info = info
        logger.info(f"✅ 数字人信息已设置: {info.get('name', '未知')}")

    def get_digital_human_info(self) -> Dict[str, Any]:
        """获取数字人信息

        Returns:
            Dict[str, Any]: 数字人信息字典
        """
        return self.digital_human_info
