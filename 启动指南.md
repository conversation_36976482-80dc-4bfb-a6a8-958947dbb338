# 🤖 智能问卷填写系统 - 启动指南

## 🚀 快速启动

### 1️⃣ 启动主服务
```bash
python main.py
```

系统将在 **http://localhost:5002** 启动

### 2️⃣ 验证系统状态
```bash
python test_complete_system_final.py
```

## 🌐 Web界面使用

1. 打开浏览器访问：http://localhost:5002
2. 页面顶部会显示所有外部服务的状态指示器
3. 输入问卷URL
4. 选择敢死队人数（1-5人）
5. 选择大部队人数（5-20人）
6. 点击"开始执行任务"

## 📊 系统架构说明

### 🎯 工作流程
1. **敢死队阶段**：少量数字人先探索问卷，收集答题经验
2. **经验分析阶段**：分析敢死队的成功经验，生成指导规则
3. **大部队阶段**：根据指导规则，大规模数字人智能答题

### 🔧 核心组件
- **AdsPower浏览器管理**：独立浏览器环境，避免检测
- **数字人系统**：多样化的虚拟答题者
- **知识库管理**：收集和分析答题经验
- **Gemini AI**：智能答题决策

## ⚙️ 外部服务状态

### ✅ 核心服务（必需）
- **AdsPower**: ✅ 正常连接
- **数据库**: ✅ 连接正常
- **Gemini API**: ✅ 可用

### ⚠️ 辅助服务（可选）
- **青果代理**: ⚠️ 认证问题（不影响核心功能）
- **小社会系统**: ⚠️ 连接问题（会使用备选数字人）

## 🛠️ 常用命令

### 测试AdsPower连接
```bash
python test_adspower_connection.py
```

### 单独测试问卷填写
```bash
python testWenjuanFinal.py --digital-human-id 1 --url "问卷URL"
```

### 检查系统状态
```bash
curl http://localhost:5002/system_status | python -m json.tool
```

## 📋 使用建议

1. **首次使用**：建议先用1个敢死队 + 5个大部队进行测试
2. **生产使用**：推荐2个敢死队 + 10-15个大部队
3. **大规模任务**：可使用3-5个敢死队 + 20个大部队

## 🔍 故障排除

### AdsPower问题
- 确保AdsPower客户端已启动
- 检查API密钥是否正确
- 账户配置文件数量是否超限

### 数据库问题
- 确保MySQL服务运行正常
- 检查数据库连接配置
- 确认数据库表结构已创建

### Gemini API问题
- 检查API密钥是否有效
- 确认网络连接正常
- 验证API配额是否充足

## 📞 技术支持

如遇问题，请：
1. 运行完整测试：`python test_complete_system_final.py`
2. 查看系统日志输出
3. 检查各服务状态指示器
4. 联系技术支持并提供错误日志

---

🎉 **系统已准备就绪！** 开始体验智能问卷填写功能吧！ 