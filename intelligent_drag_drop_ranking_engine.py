#!/usr/bin/env python3
"""
🎯 智能拖拽排序引擎
专门处理排序题、穿梭框题等复杂交互题型

核心功能：
1. 自动检测拖拽题型（排序题、穿梭框题、优先级排序等）
2. 智能评估选项重要性
3. 模拟人类拖拽行为
4. 绕过反作弊机制
5. 与WebUI完美集成

作者: AI Assistant
日期: 2024
"""

import asyncio
import random
import time
from typing import Dict, List, Optional, Any
import logging

logger = logging.getLogger(__name__)

class IntelligentDragDropRankingEngine:
    """🎯 智能拖拽排序引擎"""
    
    def __init__(self):
        self.ranking_strategies = {
            'ranking_sort': self._handle_ranking_sort_question,
            'shuttle_transfer': self._handle_shuttle_transfer_question, 
            'priority_ordering': self._handle_priority_ordering_question,
            'generic_drag_drop': self._handle_generic_drag_drop_question
        }
        
        self.drag_execution_strategies = [
            self._try_html5_drag_drop,
            self._try_mouse_drag_drop,
            self._try_javascript_reorder,
            self._try_sortable_library_reorder
        ]
        
    async def handle_drag_drop_question(self, page, digital_human_info: Dict, ranking_context: str = "importance") -> Dict:
        """
        🚀 主入口：处理拖拽排序题
        """
        try:
            logger.info(f"🎯 启动智能拖拽排序引擎: context={ranking_context}")
            
            # Step 1: 智能检测题型
            question_analysis = await self._analyze_drag_drop_question_type(page, digital_human_info)
            
            if not question_analysis.get('is_drag_drop_question'):
                return {
                    'success': False,
                    'error': '当前页面不包含拖拽排序题型',
                    'question_analysis': question_analysis
                }
            
            logger.info(f"🔍 检测到题型: {question_analysis['question_type']}")
            
            # Step 2: 根据题型执行对应策略
            question_type = question_analysis['question_type']
            if question_type in self.ranking_strategies:
                strategy_func = self.ranking_strategies[question_type]
                result = await strategy_func(page, question_analysis, digital_human_info, ranking_context)
            else:
                result = await self._handle_generic_drag_drop_question(
                    page, question_analysis, digital_human_info, ranking_context
                )
            
            if result.get('success'):
                logger.info(f"✅ 拖拽排序完成: {result['method']}")
            else:
                logger.error(f"❌ 拖拽排序失败: {result.get('error')}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 智能拖拽排序引擎异常: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    async def _analyze_drag_drop_question_type(self, page, digital_human_info: Dict) -> Dict:
        """
        🔍 分析拖拽排序题型
        智能检测各种拖拽交互题型
        """
        try:
            logger.info("🔍 开始分析拖拽排序题型...")
            
            # 获取页面所有元素
            page_content = await page.evaluate("""
                () => {
                    const analysis = {
                        draggable_elements: [],
                        drop_zones: [],
                        shuttle_components: [],
                        sortable_lists: [],
                        question_text: '',
                        page_structure: {}
                    };
                    
                    // 1. 检测可拖拽元素
                    const draggableElements = document.querySelectorAll('[draggable="true"], .draggable, .sortable-item, .drag-item, .ui-draggable');
                    draggableElements.forEach((el, index) => {
                        analysis.draggable_elements.push({
                            index: index,
                            text: el.textContent?.trim() || '',
                            className: el.className || '',
                            id: el.id || '',
                            tagName: el.tagName.toLowerCase(),
                            position: el.getBoundingClientRect()
                        });
                    });
                    
                    // 2. 检测投放区域
                    const dropZones = document.querySelectorAll('.drop-zone, .dropzone, .sortable-container, [data-drop], .ui-droppable');
                    dropZones.forEach((el, index) => {
                        analysis.drop_zones.push({
                            index: index,
                            text: el.textContent?.trim() || '',
                            className: el.className || '',
                            id: el.id || '',
                            position: el.getBoundingClientRect()
                        });
                    });
                    
                    // 3. 检测穿梭框组件
                    const shuttleComponents = document.querySelectorAll('.transfer, .shuttle, .dual-list, .ant-transfer');
                    shuttleComponents.forEach((el, index) => {
                        const leftList = el.querySelector('.left-list, .source-list, .available-list, .ant-transfer-list:first-child');
                        const rightList = el.querySelector('.right-list, .target-list, .selected-list, .ant-transfer-list:last-child');
                        const transferButtons = el.querySelectorAll('.transfer-btn, .shuttle-btn, .ant-transfer-operation button, button');
                        
                        analysis.shuttle_components.push({
                            index: index,
                            leftList: leftList ? {
                                items: Array.from(leftList.querySelectorAll('li, .item, .option, .ant-transfer-list-content-item')).map(item => ({
                                    text: item.textContent?.trim() || '',
                                    selected: item.classList.contains('selected') || item.classList.contains('ant-transfer-list-content-item-checked') || item.checked
                                }))
                            } : null,
                            rightList: rightList ? {
                                items: Array.from(rightList.querySelectorAll('li, .item, .option, .ant-transfer-list-content-item')).map(item => ({
                                    text: item.textContent?.trim() || ''
                                }))
                            } : null,
                            transferButtons: Array.from(transferButtons).map(btn => ({
                                text: btn.textContent?.trim() || '',
                                direction: btn.textContent?.includes('→') || btn.textContent?.includes('>') || btn.classList.contains('ant-transfer-operation-move-right') ? 'right' : 
                                          btn.textContent?.includes('←') || btn.textContent?.includes('<') || btn.classList.contains('ant-transfer-operation-move-left') ? 'left' : 'unknown'
                            }))
                        });
                    });
                    
                    // 4. 检测排序列表
                    const sortableLists = document.querySelectorAll('ul.sortable, ol.sortable, .sortable-list, [data-sortable], .ui-sortable');
                    sortableLists.forEach((el, index) => {
                        analysis.sortable_lists.push({
                            index: index,
                            items: Array.from(el.children).map((item, itemIndex) => ({
                                index: itemIndex,
                                text: item.textContent?.trim() || '',
                                className: item.className || '',
                                position: item.getBoundingClientRect()
                            }))
                        });
                    });
                    
                    // 5. 获取问题文本
                    const questionElements = document.querySelectorAll('.question, .question-text, h1, h2, h3, .title, .survey-question');
                    if (questionElements.length > 0) {
                        analysis.question_text = questionElements[0].textContent?.trim() || '';
                    }
                    
                    // 6. 页面结构分析
                    analysis.page_structure = {
                        has_sortable_js: !!window.Sortable,
                        has_jquery_ui: !!window.jQuery && !!window.jQuery.ui,
                        has_drag_events: document.ondragstart !== undefined,
                        has_antd: !!window.antd || document.querySelector('.ant-transfer'),
                        total_interactive_elements: document.querySelectorAll('input, button, select, [draggable], [onclick]').length
                    };
                    
                    return analysis;
                }
            """)
            
            # 分析结果并确定题型
            question_type = self._determine_drag_drop_question_type(page_content)
            
            analysis_result = {
                'is_drag_drop_question': question_type != 'none',
                'question_type': question_type,
                'page_content': page_content,
                'question_text': page_content.get('question_text', ''),
                'complexity_level': self._assess_question_complexity(page_content)
            }
            
            logger.info(f"🔍 题型分析完成: {question_type} (复杂度: {analysis_result['complexity_level']})")
            return analysis_result
            
        except Exception as e:
            logger.error(f"❌ 拖拽题型分析失败: {e}")
            return {
                'is_drag_drop_question': False,
                'question_type': 'none',
                'error': str(e)
            }

    def _determine_drag_drop_question_type(self, page_content: Dict) -> str:
        """确定拖拽题型"""
        try:
            draggable_count = len(page_content.get('draggable_elements', []))
            drop_zone_count = len(page_content.get('drop_zones', []))
            shuttle_count = len(page_content.get('shuttle_components', []))
            sortable_count = len(page_content.get('sortable_lists', []))
            
            # 穿梭框题型
            if shuttle_count > 0:
                return 'shuttle_transfer'
            
            # 排序题型
            if sortable_count > 0 or (draggable_count > 0 and drop_zone_count > 0):
                question_text = page_content.get('question_text', '').lower()
                if any(keyword in question_text for keyword in ['排序', '重要性', '优先级', 'rank', 'order', 'priority', 'importance']):
                    return 'ranking_sort'
                elif any(keyword in question_text for keyword in ['拖拽', '拖动', 'drag', 'drop']):
                    return 'priority_ordering'
            
            # 通用拖拽题型
            if draggable_count > 0 or drop_zone_count > 0:
                return 'generic_drag_drop'
            
            return 'none'
            
        except Exception as e:
            logger.error(f"❌ 题型确定失败: {e}")
            return 'none'

    def _assess_question_complexity(self, page_content: Dict) -> str:
        """评估题目复杂度"""
        try:
            total_elements = (
                len(page_content.get('draggable_elements', [])) +
                len(page_content.get('drop_zones', [])) +
                len(page_content.get('shuttle_components', [])) +
                len(page_content.get('sortable_lists', []))
            )
            
            if total_elements <= 3:
                return 'simple'
            elif total_elements <= 8:
                return 'medium'
            else:
                return 'complex'
                
        except Exception as e:
            logger.error(f"❌ 复杂度评估失败: {e}")
            return 'unknown'

    async def _handle_ranking_sort_question(self, page, question_analysis: Dict, digital_human_info: Dict, ranking_context: str) -> Dict:
        """
        🎯 处理排序题
        用户需要将选项按重要性排序
        """
        try:
            logger.info("🎯 开始处理排序题...")
            
            # Step 1: 获取所有可排序选项
            options = await self._extract_sortable_options(page, question_analysis)
            
            if not options:
                return {'success': False, 'error': '未找到可排序选项'}
            
            logger.info(f"🔍 找到 {len(options)} 个可排序选项")
            
            # Step 2: 智能评估选项重要性
            ranked_options = await self._intelligent_ranking_evaluation(
                options, digital_human_info, ranking_context, question_analysis.get('question_text', '')
            )
            
            # Step 3: 执行拖拽排序
            result = await self._execute_drag_drop_sorting(page, ranked_options, question_analysis)
            
            if result.get('success'):
                logger.info("✅ 排序题处理完成")
                return {
                    'success': True,
                    'method': 'intelligent_drag_drop_sorting',
                    'ranked_options': ranked_options,
                    'execution_result': result
                }
            else:
                logger.error(f"❌ 排序执行失败: {result.get('error')}")
                return {
                    'success': False,
                    'error': result.get('error')
                }
                
        except Exception as e:
            logger.error(f"❌ 排序题处理异常: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    async def _handle_shuttle_transfer_question(self, page, question_analysis: Dict, digital_human_info: Dict, ranking_context: str) -> Dict:
        """
        🎯 处理穿梭框题
        用户需要选择左侧选项并穿梭到右侧
        """
        try:
            logger.info("🎯 开始处理穿梭框题...")
            
            shuttle_components = question_analysis['page_content'].get('shuttle_components', [])
            
            if not shuttle_components:
                return {'success': False, 'error': '未找到穿梭框组件'}
            
            results = []
            
            for shuttle in shuttle_components:
                # Step 1: 分析左侧可选项
                left_items = shuttle.get('leftList', {}).get('items', [])
                
                if not left_items:
                    continue
                
                # Step 2: 智能选择需要穿梭的选项
                selected_items = await self._intelligent_shuttle_selection(
                    left_items, digital_human_info, ranking_context, question_analysis.get('question_text', '')
                )
                
                # Step 3: 执行穿梭操作
                shuttle_result = await self._execute_shuttle_transfer(page, shuttle, selected_items)
                results.append(shuttle_result)
            
            if any(r.get('success') for r in results):
                logger.info("✅ 穿梭框题处理完成")
                return {
                    'success': True,
                    'method': 'intelligent_shuttle_transfer',
                    'results': results
                }
            else:
                return {
                    'success': False,
                    'error': '所有穿梭框操作均失败'
                }
                
        except Exception as e:
            logger.error(f"❌ 穿梭框题处理异常: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    async def _handle_priority_ordering_question(self, page, question_analysis: Dict, digital_human_info: Dict, ranking_context: str) -> Dict:
        """处理优先级排序题"""
        # 使用与排序题相同的逻辑，但评分标准略有不同
        return await self._handle_ranking_sort_question(page, question_analysis, digital_human_info, "priority")

    async def _handle_generic_drag_drop_question(self, page, question_analysis: Dict, digital_human_info: Dict, ranking_context: str) -> Dict:
        """处理通用拖拽题"""
        # 通用拖拽处理逻辑
        return await self._handle_ranking_sort_question(page, question_analysis, digital_human_info, ranking_context)

    async def _extract_sortable_options(self, page, question_analysis: Dict) -> List[Dict]:
        """提取可排序选项"""
        try:
            options = []
            
            # 从拖拽元素中提取
            draggable_elements = question_analysis['page_content'].get('draggable_elements', [])
            for element in draggable_elements:
                if element.get('text'):
                    options.append({
                        'text': element['text'],
                        'type': 'draggable',
                        'element_info': element,
                        'original_index': element.get('index', 0)
                    })
            
            # 从排序列表中提取
            sortable_lists = question_analysis['page_content'].get('sortable_lists', [])
            for sortable_list in sortable_lists:
                for item in sortable_list.get('items', []):
                    if item.get('text'):
                        options.append({
                            'text': item['text'],
                            'type': 'sortable_item',
                            'element_info': item,
                            'original_index': item.get('index', 0)
                        })
            
            logger.info(f"🔍 提取到 {len(options)} 个可排序选项")
            return options
            
        except Exception as e:
            logger.error(f"❌ 提取可排序选项失败: {e}")
            return []

    async def _intelligent_ranking_evaluation(self, options: List[Dict], digital_human_info: Dict, ranking_context: str, question_text: str) -> List[Dict]:
        """
        🧠 智能排序评估
        根据数字人信息和排序上下文智能评估选项重要性
        """
        try:
            logger.info(f"🧠 开始智能排序评估: {ranking_context}")
            
            # 为每个选项计算重要性得分
            for option in options:
                option['importance_score'] = await self._calculate_importance_score(
                    option['text'], digital_human_info, ranking_context, question_text
                )
                option['ranking_reason'] = self._generate_ranking_reason(
                    option['text'], digital_human_info, ranking_context
                )
            
            # 按重要性得分排序（从高到低）
            ranked_options = sorted(options, key=lambda x: x['importance_score'], reverse=True)
            
            # 添加排序位置
            for i, option in enumerate(ranked_options):
                option['target_position'] = i + 1
                logger.info(f"📊 排序 #{i+1}: {option['text'][:30]}... (得分: {option['importance_score']:.3f})")
            
            return ranked_options
            
        except Exception as e:
            logger.error(f"❌ 智能排序评估失败: {e}")
            return options

    async def _calculate_importance_score(self, option_text: str, digital_human_info: Dict, ranking_context: str, question_text: str) -> float:
        """计算选项重要性得分"""
        try:
            base_score = 0.5  # 基础分
            
            # 根据数字人信息调整得分
            persona_name = digital_human_info.get('name', '').lower()
            persona_location = digital_human_info.get('location', '').lower()
            persona_profession = digital_human_info.get('profession', '').lower()
            persona_age = digital_human_info.get('age', '')
            
            option_lower = option_text.lower()
            
            # 1. 地理位置相关性
            if any(loc in option_lower for loc in [persona_location, '中国', '北京', '上海']):
                base_score += 0.3
            
            # 2. 职业相关性
            if persona_profession and persona_profession in option_lower:
                base_score += 0.25
            
            # 3. 年龄相关性
            try:
                age_num = int(''.join(filter(str.isdigit, persona_age)))
                if age_num:
                    if age_num < 30 and any(word in option_lower for word in ['年轻', '创新', '科技', '互联网']):
                        base_score += 0.2
                    elif age_num >= 30 and any(word in option_lower for word in ['稳定', '传统', '经验', '管理']):
                        base_score += 0.2
            except:
                pass
            
            # 4. 排序上下文相关性
            if ranking_context == 'importance':
                if any(word in option_lower for word in ['重要', '关键', '核心', '主要']):
                    base_score += 0.15
            elif ranking_context == 'preference':
                if any(word in option_lower for word in ['喜欢', '偏好', '选择', '倾向']):
                    base_score += 0.15
            
            # 5. 问题文本相关性
            question_lower = question_text.lower()
            if any(word in question_lower and word in option_lower for word in ['健康', '教育', '工作', '家庭', '经济']):
                base_score += 0.1
            
            # 确保得分在合理范围内
            return max(0.0, min(1.0, base_score))
            
        except Exception as e:
            logger.error(f"❌ 重要性得分计算失败: {e}")
            return 0.5

    def _generate_ranking_reason(self, option_text: str, digital_human_info: Dict, ranking_context: str) -> str:
        """生成排序理由"""
        try:
            persona_name = digital_human_info.get('name', '用户')
            persona_location = digital_human_info.get('location', '北京')
            persona_profession = digital_human_info.get('profession', '职场人士')
            
            reasons = []
            
            # 基于数字人信息生成理由
            if persona_location.lower() in option_text.lower():
                reasons.append(f"与{persona_name}的居住地{persona_location}高度相关")
            
            if persona_profession.lower() in option_text.lower():
                reasons.append(f"符合{persona_name}的职业背景({persona_profession})")
            
            if not reasons:
                reasons.append(f"基于{ranking_context}评估，适合{persona_name}的情况")
            
            return '; '.join(reasons)
            
        except Exception as e:
            logger.error(f"❌ 排序理由生成失败: {e}")
            return "系统智能推荐"

    async def _execute_drag_drop_sorting(self, page, ranked_options: List[Dict], question_analysis: Dict) -> Dict:
        """
        🎯 执行拖拽排序操作
        使用多种策略确保拖拽成功
        """
        try:
            logger.info("🎯 开始执行拖拽排序...")
            
            # 尝试各种拖拽策略
            for strategy in self.drag_execution_strategies:
                try:
                    result = await strategy(page, ranked_options)
                    if result.get('success'):
                        return result
                except Exception as e:
                    logger.warning(f"⚠️ 拖拽策略失败: {e}")
                    continue
            
            return {
                'success': False,
                'error': '所有拖拽策略均失败',
                'attempted_strategies': [s.__name__ for s in self.drag_execution_strategies]
            }
            
        except Exception as e:
            logger.error(f"❌ 拖拽排序执行异常: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    async def _try_html5_drag_drop(self, page, ranked_options: List[Dict]) -> Dict:
        """尝试HTML5拖拽API"""
        try:
            logger.info("🔄 尝试HTML5拖拽API...")
            
            # 生成拖拽脚本
            drag_script = """
                (rankedOptions) => {
                    return new Promise((resolve) => {
                        const results = [];
                        let completed = 0;
                        
                        rankedOptions.forEach((option, targetIndex) => {
                            // 查找源元素
                            let sourceElement = document.querySelector(`[data-index="${option.original_index}"]`);
                            if (!sourceElement) {
                                // 尝试通过文本内容查找
                                const elements = Array.from(document.querySelectorAll('*'));
                                sourceElement = elements.find(el => 
                                    el.textContent?.trim() === option.text && 
                                    (el.draggable || el.classList.contains('draggable') || el.classList.contains('sortable-item'))
                                );
                            }
                            
                            if (sourceElement) {
                                // 模拟HTML5拖拽事件序列
                                const dataTransfer = new DataTransfer();
                                dataTransfer.setData('text/plain', option.text);
                                
                                const dragStartEvent = new DragEvent('dragstart', {
                                    bubbles: true,
                                    cancelable: true,
                                    dataTransfer: dataTransfer
                                });
                                
                                const dragEndEvent = new DragEvent('dragend', {
                                    bubbles: true,
                                    cancelable: true,
                                    dataTransfer: dataTransfer
                                });
                                
                                sourceElement.dispatchEvent(dragStartEvent);
                                
                                // 找到目标位置
                                const dropZone = document.querySelector('.drop-zone') ||
                                               document.querySelector('.sortable-container') ||
                                               sourceElement.parentElement;
                                
                                if (dropZone) {
                                    const dropEvent = new DragEvent('drop', {
                                        bubbles: true,
                                        cancelable: true,
                                        dataTransfer: dataTransfer
                                    });
                                    
                                    dropZone.dispatchEvent(dropEvent);
                                    sourceElement.dispatchEvent(dragEndEvent);
                                    
                                    results.push({
                                        option: option.text,
                                        success: true,
                                        method: 'html5_drag_drop'
                                    });
                                } else {
                                    results.push({
                                        option: option.text,
                                        success: false,
                                        error: 'No drop zone found'
                                    });
                                }
                            } else {
                                results.push({
                                    option: option.text,
                                    success: false,
                                    error: 'Source element not found'
                                });
                            }
                            
                            completed++;
                            if (completed === rankedOptions.length) {
                                resolve(results);
                            }
                        });
                        
                        // 超时处理
                        setTimeout(() => {
                            resolve(results);
                        }, 10000);
                    });
                }
            """
            
            results = await page.evaluate(drag_script, ranked_options)
            
            success_count = sum(1 for r in results if r.get('success'))
            
            if success_count > 0:
                logger.info(f"✅ HTML5拖拽成功: {success_count}/{len(ranked_options)}")
                return {
                    'success': True,
                    'method': 'html5_drag_drop',
                    'results': results,
                    'success_count': success_count
                }
            else:
                logger.warning("⚠️ HTML5拖拽失败")
                return {
                    'success': False,
                    'error': 'HTML5 drag drop failed',
                    'results': results
                }
                
        except Exception as e:
            logger.error(f"❌ HTML5拖拽异常: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    async def _try_mouse_drag_drop(self, page, ranked_options: List[Dict]) -> Dict:
        """尝试鼠标模拟拖拽"""
        try:
            logger.info("🔄 尝试鼠标模拟拖拽...")
            
            success_count = 0
            results = []
            
            for i, option in enumerate(ranked_options):
                try:
                    # 查找源元素
                    source_selector = f"*:has-text('{option['text']}')"
                    source_element = page.locator(source_selector).first
                    
                    if await source_element.count() == 0:
                        results.append({
                            'option': option['text'],
                            'success': False,
                            'error': 'Source element not found'
                        })
                        continue
                    
                    # 获取源元素位置
                    source_box = await source_element.bounding_box()
                    if not source_box:
                        results.append({
                            'option': option['text'],
                            'success': False,
                            'error': 'Cannot get source element position'
                        })
                        continue
                    
                    # 计算目标位置
                    target_y = source_box['y'] + (i * 60)  # 假设每个选项高度60px
                    
                    # 执行拖拽
                    source_x = source_box['x'] + source_box['width']/2
                    source_y = source_box['y'] + source_box['height']/2
                    
                    await page.mouse.move(source_x, source_y)
                    await asyncio.sleep(0.1)
                    await page.mouse.down()
                    await asyncio.sleep(0.2)
                    
                    # 分步移动到目标位置
                    steps = 10
                    for step in range(steps):
                        progress = (step + 1) / steps
                        current_y = source_y + (target_y - source_y) * progress
                        await page.mouse.move(source_x, current_y)
                        await asyncio.sleep(0.05)
                    
                    await page.mouse.up()
                    
                    # 添加人类化延迟
                    await asyncio.sleep(random.uniform(0.3, 0.7))
                    
                    success_count += 1
                    results.append({
                        'option': option['text'],
                        'success': True,
                        'method': 'mouse_drag_drop'
                    })
                    
                except Exception as e:
                    results.append({
                        'option': option['text'],
                        'success': False,
                        'error': str(e)
                    })
            
            if success_count > 0:
                logger.info(f"✅ 鼠标拖拽成功: {success_count}/{len(ranked_options)}")
                return {
                    'success': True,
                    'method': 'mouse_drag_drop',
                    'results': results,
                    'success_count': success_count
                }
            else:
                logger.warning("⚠️ 鼠标拖拽失败")
                return {
                    'success': False,
                    'error': 'Mouse drag drop failed',
                    'results': results
                }
                
        except Exception as e:
            logger.error(f"❌ 鼠标拖拽异常: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    async def _try_javascript_reorder(self, page, ranked_options: List[Dict]) -> Dict:
        """尝试JavaScript直接重排序"""
        try:
            logger.info("🔄 尝试JavaScript直接重排序...")
            
            reorder_script = """
                (rankedOptions) => {
                    try {
                        const results = [];
                        
                        // 查找容器
                        const containers = document.querySelectorAll('.sortable-container, .sortable-list, ul.sortable, ol.sortable, .ui-sortable');
                        
                        if (containers.length === 0) {
                            return { success: false, error: 'No sortable container found' };
                        }
                        
                        const container = containers[0];
                        const items = Array.from(container.children);
                        
                        // 创建新的排序
                        const newOrder = [];
                        rankedOptions.forEach(option => {
                            const item = items.find(item => 
                                item.textContent?.trim().includes(option.text) ||
                                item.getAttribute('data-text') === option.text ||
                                item.querySelector('*')?.textContent?.trim().includes(option.text)
                            );
                            if (item) {
                                newOrder.push(item);
                                results.push({
                                    option: option.text,
                                    success: true,
                                    method: 'javascript_reorder'
                                });
                            } else {
                                results.push({
                                    option: option.text,
                                    success: false,
                                    error: 'Item not found'
                                });
                            }
                        });
                        
                        // 重新排列DOM
                        newOrder.forEach(item => {
                            container.appendChild(item);
                        });
                        
                        // 触发相关事件
                        const events = ['change', 'input', 'sortupdate', 'sortchange'];
                        events.forEach(eventType => {
                            const event = new Event(eventType, { bubbles: true });
                            container.dispatchEvent(event);
                        });
                        
                        return {
                            success: true,
                            method: 'javascript_reorder',
                            results: results,
                            reordered_count: newOrder.length
                        };
                        
                    } catch (error) {
                        return {
                            success: false,
                            error: error.message
                        };
                    }
                }
            """
            
            result = await page.evaluate(reorder_script, ranked_options)
            
            if result.get('success'):
                logger.info(f"✅ JavaScript重排序成功: {result.get('reordered_count', 0)}")
                return result
            else:
                logger.warning(f"⚠️ JavaScript重排序失败: {result.get('error')}")
                return result
                
        except Exception as e:
            logger.error(f"❌ JavaScript重排序异常: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    async def _try_sortable_library_reorder(self, page, ranked_options: List[Dict]) -> Dict:
        """尝试使用SortableJS等库进行重排序"""
        try:
            logger.info("🔄 尝试SortableJS库重排序...")
            
            sortable_script = """
                (rankedOptions) => {
                    try {
                        // 检查SortableJS是否可用
                        if (typeof Sortable !== 'undefined') {
                            const containers = document.querySelectorAll('.sortable, [data-sortable]');
                            if (containers.length > 0) {
                                const container = containers[0];
                                const sortableInstance = Sortable.get(container);
                                
                                if (sortableInstance) {
                                    // 使用SortableJS的排序功能
                                    const items = Array.from(container.children);
                                    const newOrder = [];
                                    
                                    rankedOptions.forEach(option => {
                                        const item = items.find(item => 
                                            item.textContent?.trim().includes(option.text)
                                        );
                                        if (item) {
                                            newOrder.push(items.indexOf(item));
                                        }
                                    });
                                    
                                    // 应用新排序
                                    sortableInstance.sort(newOrder);
                                    
                                    return {
                                        success: true,
                                        method: 'sortable_library',
                                        reordered_count: newOrder.length
                                    };
                                }
                            }
                        }
                        
                        // 检查jQuery UI Sortable
                        if (typeof jQuery !== 'undefined' && jQuery.ui && jQuery.ui.sortable) {
                            const $sortable = jQuery('.ui-sortable');
                            if ($sortable.length > 0) {
                                // 使用jQuery UI Sortable
                                const items = $sortable.children().toArray();
                                const newOrder = [];
                                
                                rankedOptions.forEach(option => {
                                    const item = items.find(item => 
                                        jQuery(item).text().trim().includes(option.text)
                                    );
                                    if (item) {
                                        newOrder.push(jQuery(item));
                                    }
                                });
                                
                                // 重新排列
                                $sortable.empty();
                                newOrder.forEach(item => {
                                    $sortable.append(item);
                                });
                                
                                $sortable.sortable('refresh');
                                
                                return {
                                    success: true,
                                    method: 'jquery_ui_sortable',
                                    reordered_count: newOrder.length
                                };
                            }
                        }
                        
                        return { success: false, error: 'No sortable library found' };
                        
                    } catch (error) {
                        return {
                            success: false,
                            error: error.message
                        };
                    }
                }
            """
            
            result = await page.evaluate(sortable_script, ranked_options)
            
            if result.get('success'):
                logger.info(f"✅ 排序库重排序成功: {result.get('reordered_count', 0)}")
                return result
            else:
                logger.warning(f"⚠️ 排序库重排序失败: {result.get('error')}")
                return result
                
        except Exception as e:
            logger.error(f"❌ 排序库重排序异常: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    async def _intelligent_shuttle_selection(self, left_items: List[Dict], digital_human_info: Dict, ranking_context: str, question_text: str) -> List[Dict]:
        """智能选择需要穿梭的选项"""
        try:
            selected_items = []
            
            for item in left_items:
                score = await self._calculate_importance_score(
                    item['text'], digital_human_info, ranking_context, question_text
                )
                
                # 选择评分高于阈值的选项
                if score >= 0.6:
                    item['selection_score'] = score
                    item['selection_reason'] = self._generate_ranking_reason(
                        item['text'], digital_human_info, ranking_context
                    )
                    selected_items.append(item)
            
            logger.info(f"🎯 智能选择了 {len(selected_items)} 个穿梭选项")
            return selected_items
            
        except Exception as e:
            logger.error(f"❌ 智能穿梭选择失败: {e}")
            return []

    async def _execute_shuttle_transfer(self, page, shuttle_info: Dict, selected_items: List[Dict]) -> Dict:
        """执行穿梭操作"""
        try:
            logger.info("🔄 执行穿梭操作...")
            
            # 实现穿梭框的选择和转移逻辑
            transfer_script = """
                (shuttleInfo, selectedItems) => {
                    try {
                        const results = [];
                        
                        // 查找穿梭框容器
                        const shuttleContainers = document.querySelectorAll('.transfer, .shuttle, .dual-list, .ant-transfer');
                        
                        if (shuttleContainers.length === 0) {
                            return { success: false, error: 'No shuttle container found' };
                        }
                        
                        const shuttle = shuttleContainers[0];
                        const leftList = shuttle.querySelector('.left-list, .source-list, .available-list, .ant-transfer-list:first-child');
                        const rightButton = shuttle.querySelector('.transfer-btn[data-direction="right"], .ant-transfer-operation button:first-child, button:contains("→")');
                        
                        if (!leftList || !rightButton) {
                            return { success: false, error: 'Shuttle components not found' };
                        }
                        
                        // 选择左侧选项
                        selectedItems.forEach(item => {
                            const listItems = leftList.querySelectorAll('li, .item, .option, .ant-transfer-list-content-item');
                            const targetItem = Array.from(listItems).find(li => 
                                li.textContent?.trim().includes(item.text)
                            );
                            
                            if (targetItem) {
                                // 选中该项
                                targetItem.click();
                                results.push({
                                    item: item.text,
                                    action: 'selected',
                                    success: true
                                });
                            }
                        });
                        
                        // 点击右移按钮
                        setTimeout(() => {
                            rightButton.click();
                            results.push({
                                action: 'transfer',
                                success: true
                            });
                        }, 500);
                        
                        return {
                            success: true,
                            method: 'shuttle_transfer',
                            results: results
                        };
                        
                    } catch (error) {
                        return {
                            success: false,
                            error: error.message
                        };
                    }
                }
            """
            
            result = await page.evaluate(transfer_script, shuttle_info, selected_items)
            
            if result.get('success'):
                logger.info("✅ 穿梭操作成功")
                return result
            else:
                logger.warning(f"⚠️ 穿梭操作失败: {result.get('error')}")
                return result
                
        except Exception as e:
            logger.error(f"❌ 穿梭操作异常: {e}")
            return {
                'success': False,
                'error': str(e)
            } 