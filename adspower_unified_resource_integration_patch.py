# 🔥 AdsPower统一资源管理集成补丁
# 解决所有资源生命周期管理不统一的问题，确保AdsPower两步清理在整个系统中统一应用

import asyncio
import logging
import time
from typing import Dict, Any, Optional, List

logger = logging.getLogger(__name__)

class AdsPowerUnifiedResourceManager:
    """🔥 AdsPower统一资源管理器 - 专门解决AdsPower资源生命周期问题"""
    
    def __init__(self):
        # 🔧 AdsPower资源注册表
        self.adspower_resources: Dict[str, Dict[str, Any]] = {}
        self.resource_locks: Dict[str, asyncio.Lock] = {}
        
        # 🔧 清理状态跟踪
        self.cleanup_states: Dict[str, str] = {}  # "cleaning", "cleaned", "error"
        
        # 🔧 AdsPower API配置
        self.adspower_host = "http://local.adspower.net:50325"
        
        logger.info("🔥 AdsPower统一资源管理器已初始化")
    
    async def register_profile(
        self, 
        profile_id: str, 
        debug_port: int, 
        persona_name: str = "未知",
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """🔧 注册AdsPower配置文件"""
        resource_key = f"adspower_{profile_id}"
        
        if resource_key not in self.resource_locks:
            self.resource_locks[resource_key] = asyncio.Lock()
        
        async with self.resource_locks[resource_key]:
            resource_info = {
                "profile_id": profile_id,
                "debug_port": debug_port,
                "persona_name": persona_name,
                "registered_at": time.time(),
                "state": "active",
                "cleanup_attempts": 0,
                "metadata": metadata or {}
            }
            
            self.adspower_resources[resource_key] = resource_info
            logger.info(f"✅ AdsPower配置文件已注册: {profile_id} (端口: {debug_port})")
            
        return resource_key
    
    async def cleanup_profile_two_step(self, profile_id: str, force: bool = False) -> Dict[str, Any]:
        """🔥 AdsPower配置文件两步清理 - 核心修复方法"""
        resource_key = f"adspower_{profile_id}"
        
        if resource_key not in self.adspower_resources:
            logger.warning(f"⚠️ AdsPower配置文件未注册: {profile_id}")
            return {"success": False, "reason": "profile_not_registered"}
        
        if resource_key not in self.resource_locks:
            self.resource_locks[resource_key] = asyncio.Lock()
        
        async with self.resource_locks[resource_key]:
            resource_info = self.adspower_resources[resource_key]
            
            # 检查是否已在清理中
            if resource_info.get("state") == "cleaning" and not force:
                logger.info(f"⏳ AdsPower配置文件正在清理中: {profile_id}")
                return {"success": False, "reason": "already_cleaning"}
            
            if resource_info.get("state") == "cleaned" and not force:
                logger.info(f"✅ AdsPower配置文件已清理: {profile_id}")
                return {"success": True, "reason": "already_cleaned"}
            
            try:
                resource_info["state"] = "cleaning"
                resource_info["cleanup_attempts"] += 1
                persona_name = resource_info.get("persona_name", "未知")
                
                logger.info(f"🧹 开始AdsPower两步清理: {profile_id} ({persona_name})")
                
                # 🔥 第一步：停止浏览器实例
                logger.info(f"🔧 第一步：停止浏览器实例 {profile_id}")
                stop_result = await self._stop_browser_instance(profile_id)
                
                if stop_result["success"]:
                    logger.info(f"✅ 浏览器实例停止成功: {profile_id}")
                else:
                    logger.warning(f"⚠️ 浏览器实例停止失败: {stop_result.get('error', '未知错误')}")
                    logger.warning(f"⚠️ 继续执行第二步删除配置文件")
                
                # 等待1秒确保停止完成
                await asyncio.sleep(1)
                
                # 🔥 第二步：删除配置文件（关键步骤 - 从AdsPower列表中完全移除）
                logger.info(f"🔧 第二步：删除配置文件 {profile_id}")
                delete_result = await self._delete_profile_from_adspower(profile_id)
                
                if delete_result["success"]:
                    logger.info(f"✅ 配置文件删除成功，AdsPower资源完全释放: {profile_id}")
                    resource_info["state"] = "cleaned"
                    resource_info["cleaned_at"] = time.time()
                    
                    return {
                        "success": True,
                        "profile_id": profile_id,
                        "persona_name": persona_name,
                        "stop_success": stop_result["success"],
                        "delete_success": delete_result["success"],
                        "full_cleanup": True,
                        "message": "AdsPower资源完全释放"
                    }
                else:
                    logger.error(f"❌ 配置文件删除失败，AdsPower资源未完全释放: {profile_id}")
                    logger.error(f"❌ 错误详情: {delete_result.get('error', '未知错误')}")
                    resource_info["state"] = "error"
                    
                    return {
                        "success": False,
                        "profile_id": profile_id,
                        "persona_name": persona_name,
                        "stop_success": stop_result["success"],
                        "delete_success": delete_result["success"],
                        "full_cleanup": False,
                        "error": delete_result.get("error", "配置文件删除失败"),
                        "message": "AdsPower资源未完全释放"
                    }
                    
            except Exception as e:
                logger.error(f"❌ AdsPower两步清理异常: {profile_id} - {e}")
                resource_info["state"] = "error"
                resource_info["last_error"] = str(e)
                
                return {
                    "success": False,
                    "profile_id": profile_id,
                    "error": str(e),
                    "full_cleanup": False,
                    "message": "AdsPower清理过程中发生异常"
                }
    
    async def _stop_browser_instance(self, profile_id: str) -> Dict[str, Any]:
        """第一步：停止浏览器实例"""
        try:
            import aiohttp
            
            timeout = aiohttp.ClientTimeout(total=10)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                url = f"{self.adspower_host}/api/v1/browser/stop"
                data = {"user_id": profile_id}
                
                async with session.post(url, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get("code") == 0:
                            return {
                                "success": True,
                                "message": "浏览器实例停止成功",
                                "api_response": result
                            }
                        else:
                            return {
                                "success": False,
                                "error": f"AdsPower API返回错误: {result}",
                                "api_response": result
                            }
                    else:
                        return {
                            "success": False,
                            "error": f"HTTP请求失败: {response.status}",
                            "http_status": response.status
                        }
                        
        except ImportError:
            logger.error("❌ aiohttp模块未安装，无法执行AdsPower API调用")
            return {
                "success": False,
                "error": "aiohttp模块未安装"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"停止浏览器实例失败: {e}"
            }
    
    async def _delete_profile_from_adspower(self, profile_id: str) -> Dict[str, Any]:
        """第二步：删除配置文件（从AdsPower列表中完全移除）"""
        try:
            import aiohttp
            
            timeout = aiohttp.ClientTimeout(total=15)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                url = f"{self.adspower_host}/api/v1/user/delete"
                data = {"user_ids": [profile_id]}
                
                async with session.post(url, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get("code") == 0:
                            return {
                                "success": True,
                                "message": "配置文件删除成功，AdsPower资源完全释放",
                                "api_response": result
                            }
                        else:
                            return {
                                "success": False,
                                "error": f"AdsPower删除API返回错误: {result}",
                                "api_response": result
                            }
                    else:
                        return {
                            "success": False,
                            "error": f"HTTP删除请求失败: {response.status}",
                            "http_status": response.status
                        }
                        
        except ImportError:
            logger.error("❌ aiohttp模块未安装，无法执行AdsPower删除API调用")
            return {
                "success": False,
                "error": "aiohttp模块未安装"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"删除配置文件失败: {e}"
            }
    
    async def cleanup_all_profiles(self, force: bool = False) -> Dict[str, Any]:
        """🔧 清理所有AdsPower配置文件"""
        logger.info("🧹 开始清理所有AdsPower配置文件...")
        
        active_profiles = [
            resource_key for resource_key, resource_info in self.adspower_resources.items()
            if resource_info.get("state") in ["active", "error"] or force
        ]
        
        if not active_profiles:
            logger.info("✅ 没有需要清理的AdsPower配置文件")
            return {"success": True, "cleaned_count": 0, "total_count": 0}
        
        cleanup_results = []
        success_count = 0
        
        for resource_key in active_profiles:
            try:
                profile_id = resource_key.replace("adspower_", "")
                result = await self.cleanup_profile_two_step(profile_id, force)
                
                cleanup_results.append({
                    "profile_id": profile_id,
                    "resource_key": resource_key,
                    "result": result
                })
                
                if result.get("success"):
                    success_count += 1
                    logger.info(f"✅ AdsPower配置文件清理成功: {profile_id}")
                else:
                    logger.error(f"❌ AdsPower配置文件清理失败: {profile_id}")
                    
            except Exception as e:
                logger.error(f"❌ AdsPower配置文件清理异常: {resource_key} - {e}")
                cleanup_results.append({
                    "profile_id": profile_id,
                    "resource_key": resource_key,
                    "result": {"success": False, "error": str(e)}
                })
        
        total_count = len(cleanup_results)
        logger.info(f"✅ AdsPower配置文件清理完成: {success_count}/{total_count}")
        
        return {
            "success": success_count > 0,
            "cleaned_count": success_count,
            "total_count": total_count,
            "results": cleanup_results
        }
    
    async def get_status_report(self) -> Dict[str, Any]:
        """🔧 获取AdsPower资源状态报告"""
        total_resources = len(self.adspower_resources)
        active_resources = len([r for r in self.adspower_resources.values() if r.get("state") == "active"])
        cleaning_resources = len([r for r in self.adspower_resources.values() if r.get("state") == "cleaning"])
        cleaned_resources = len([r for r in self.adspower_resources.values() if r.get("state") == "cleaned"])
        error_resources = len([r for r in self.adspower_resources.values() if r.get("state") == "error"])
        
        return {
            "total_resources": total_resources,
            "active_resources": active_resources,
            "cleaning_resources": cleaning_resources,
            "cleaned_resources": cleaned_resources,
            "error_resources": error_resources,
            "resource_details": {
                resource_key: {
                    "profile_id": info.get("profile_id"),
                    "persona_name": info.get("persona_name"),
                    "state": info.get("state"),
                    "cleanup_attempts": info.get("cleanup_attempts", 0),
                    "registered_at": info.get("registered_at"),
                    "cleaned_at": info.get("cleaned_at")
                }
                for resource_key, info in self.adspower_resources.items()
            }
        }
    
    async def emergency_cleanup(self):
        """🚨 紧急清理所有AdsPower资源"""
        logger.warning("🚨 执行AdsPower紧急清理...")
        
        result = await self.cleanup_all_profiles(force=True)
        
        # 清理内部状态
        self.adspower_resources.clear()
        self.resource_locks.clear()
        self.cleanup_states.clear()
        
        logger.warning(f"🚨 AdsPower紧急清理完成: {result}")
        return result

# 🔥 全局单例实例
adspower_unified_manager = AdsPowerUnifiedResourceManager()

# 🔧 便捷函数
async def register_adspower_profile(profile_id: str, debug_port: int, persona_name: str = "未知", metadata: Optional[Dict[str, Any]] = None):
    """便捷的AdsPower配置文件注册函数"""
    return await adspower_unified_manager.register_profile(profile_id, debug_port, persona_name, metadata)

async def cleanup_adspower_profile_two_step(profile_id: str, force: bool = False):
    """便捷的AdsPower两步清理函数"""
    return await adspower_unified_manager.cleanup_profile_two_step(profile_id, force)

async def cleanup_all_adspower_profiles(force: bool = False):
    """便捷的所有AdsPower配置文件清理函数"""
    return await adspower_unified_manager.cleanup_all_profiles(force)

async def get_adspower_status_report():
    """便捷的AdsPower状态报告函数"""
    return await adspower_unified_manager.get_status_report()

async def emergency_cleanup_adspower():
    """便捷的AdsPower紧急清理函数"""
    return await adspower_unified_manager.emergency_cleanup()

# 🔧 集成补丁函数
def apply_adspower_unified_resource_patch(target_class):
    """🔥 将统一资源管理器集成到目标类中"""
    try:
        # 添加统一资源管理器实例
        target_class.adspower_unified_manager = adspower_unified_manager
        
        # 添加便捷方法
        target_class.register_adspower_profile = register_adspower_profile
        target_class.cleanup_adspower_profile_two_step = cleanup_adspower_profile_two_step
        target_class.cleanup_all_adspower_profiles = cleanup_all_adspower_profiles
        target_class.get_adspower_status_report = get_adspower_status_report
        target_class.emergency_cleanup_adspower = emergency_cleanup_adspower
        
        logger.info(f"✅ AdsPower统一资源管理补丁已应用到: {target_class.__name__}")
        return True
        
    except Exception as e:
        logger.error(f"❌ AdsPower统一资源管理补丁应用失败: {e}")
        return False 