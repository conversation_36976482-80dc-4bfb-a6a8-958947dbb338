#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
🔥 智能问卷系统完整修复方案
解决所有题型识别和处理问题，确保最大程度利用WebUI的智能性
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from browser_use.browser.context import BrowserContext
from browser_use.agent.views import ActionResult

logger = logging.getLogger(__name__)

class IntelligentQuestionnaireFixEngine:
    """🔥 智能问卷修复引擎 - 完整解决方案"""
    
    def __init__(self, custom_controller=None):
        self.custom_controller = custom_controller
        self.digital_human_info = {}
        
    def apply_complete_fix(self, custom_controller, digital_human_info):
        """应用完整修复方案"""
        logger.info("🔥 开始应用智能问卷完整修复方案...")
        
        self.custom_controller = custom_controller
        self.digital_human_info = digital_human_info
        
        # 1. 注册增强版智能点击处理器
        self._register_enhanced_click_handler()
        
        # 2. 注册国家选择专用处理器
        self._register_country_selection_handler()
        
        # 3. 注册通用智能选择处理器
        self._register_universal_intelligent_handler()
        
        # 4. 注册题型检测和路由器
        self._register_question_type_router()
        
        logger.info("✅ 智能问卷完整修复方案应用完成")
        
    def _register_enhanced_click_handler(self):
        """注册增强版智能点击处理器"""
        try:
            @self.custom_controller.registry.action(
                'Enhanced intelligent click handler with comprehensive question type support',
            )
            async def enhanced_intelligent_click_element_by_index(
                index: int, 
                browser: BrowserContext
            ) -> ActionResult:
                """🔥 增强版智能点击处理器 - 支持所有题型"""
                try:
                    # 获取元素信息
                    selector_map = await browser.get_selector_map()
                    if index not in selector_map:
                        return ActionResult(error=f"Element index {index} not found")
                    
                    dom_element = selector_map[index]
                    element_text = getattr(dom_element, 'text', '') or ''
                    element_tag = getattr(dom_element, 'tag_name', '')
                    page = await browser.get_current_page()
                    
                    logger.info(f"🎯 增强智能处理器启动 - 元素: '{element_text}' 标签: '{element_tag}'")
                    
                    # 题型检测和路由
                    question_type = self._detect_question_type(element_text, element_tag)
                    logger.info(f"🔍 检测到题型: {question_type}")
                    
                    # 根据题型使用不同处理策略
                    if question_type == "country_selection":
                        return await self._handle_country_selection_enhanced(
                            browser, page, dom_element, element_text, index
                        )
                    elif question_type == "radio_button":
                        return await self._handle_radio_selection_enhanced(
                            browser, page, dom_element, element_text, index
                        )
                    elif question_type == "dropdown":
                        return await self._handle_dropdown_selection_enhanced(
                            browser, page, dom_element, element_text, index
                        )
                    else:
                        return await self._handle_general_selection_enhanced(
                            browser, page, dom_element, element_text, index
                        )
                        
                except Exception as e:
                    logger.error(f"❌ 增强智能处理失败: {e}")
                    return await self._safe_fallback_click(browser, index)
            
            logger.info("✅ 增强版智能点击处理器已注册")
            
        except Exception as e:
            logger.error(f"❌ 增强点击处理器注册失败: {e}")

    def _register_country_selection_handler(self):
        """注册国家选择专用处理器"""
        try:
            @self.custom_controller.registry.action(
                'Specialized country selection handler with multiple strategies',
            )
            async def specialized_country_selection_handler(
                browser: BrowserContext,
                target_nationality: str = "中国"
            ) -> ActionResult:
                """🗺️ 专业国家选择处理器"""
                try:
                    page = await browser.get_current_page()
                    logger.info(f"🗺️ 专业国家选择启动，目标: {target_nationality}")
                    
                    # 策略1: 直接文本匹配
                    china_patterns = [
                        "中国", "中华人民共和国", "中国大陆", "中国(简体中文)",
                        "China", "China (Simplified Chinese)", "People's Republic of China"
                    ]
                    
                    for pattern in china_patterns:
                        try:
                            # 尝试精确文本匹配
                            element = page.locator(f'text="{pattern}"').first
                            if await element.is_visible(timeout=2000):
                                await element.click()
                                logger.info(f"✅ 国家选择成功 - 精确匹配: {pattern}")
                                return ActionResult(
                                    extracted_content=f"✅ 国家选择成功: {pattern}"
                                )
                        except:
                            # 尝试包含匹配
                            try:
                                element = page.locator(f'text*="{pattern}"').first
                                if await element.is_visible(timeout=2000):
                                    await element.click()
                                    logger.info(f"✅ 国家选择成功 - 包含匹配: {pattern}")
                                    return ActionResult(
                                        extracted_content=f"✅ 国家选择成功: {pattern}"
                                    )
                            except:
                                continue
                    
                    # 策略2: 属性值匹配
                    value_patterns = ["china", "cn", "chn", "中国"]
                    for pattern in value_patterns:
                        try:
                            element = page.locator(f'[value*="{pattern}"]').first
                            if await element.is_visible(timeout=2000):
                                await element.click()
                                logger.info(f"✅ 国家选择成功 - 属性匹配: {pattern}")
                                return ActionResult(
                                    extracted_content=f"✅ 国家选择成功: 属性值 {pattern}"
                                )
                        except:
                            continue
                    
                    # 策略3: 使用现有智能发现引擎
                    if hasattr(self.custom_controller, 'intelligent_option_discovery_engine'):
                        try:
                            discovery_result = await self.custom_controller.intelligent_option_discovery_engine(
                                page, self.digital_human_info,
                                target_question_context="nationality_selection",
                                search_scope="country_language"
                            )
                            
                            if discovery_result.get('success') and discovery_result.get('recommended_option'):
                                recommended = discovery_result['recommended_option']
                                try:
                                    await page.click(f'text="{recommended.get("text", "")}"')
                                    logger.info(f"✅ 国家选择成功 - 智能发现: {recommended.get('text', '未知')}")
                                    return ActionResult(
                                        extracted_content=f"✅ 智能发现选择成功: {recommended.get('text', '未知')}"
                                    )
                                except:
                                    pass
                        except Exception as discovery_error:
                            logger.debug(f"⚠️ 智能发现引擎失败: {discovery_error}")
                    
                    logger.warning("⚠️ 所有国家选择策略都失败")
                    return ActionResult(
                        extracted_content="⚠️ 国家选择：未找到匹配选项"
                    )
                    
                except Exception as e:
                    logger.error(f"❌ 专业国家选择失败: {e}")
                    return ActionResult(error=f"国家选择失败: {e}")
                
            logger.info("✅ 国家选择专用处理器已注册")
            
        except Exception as e:
            logger.error(f"❌ 国家选择处理器注册失败: {e}")

    def _register_universal_intelligent_handler(self):
        """注册通用智能选择处理器"""
        try:
            @self.custom_controller.registry.action(
                'Universal intelligent selection handler with digital human awareness',
            )
            async def universal_intelligent_selection_handler(
                index: int,
                browser: BrowserContext,
                element_text: str = "",
                force_intelligent: bool = True
            ) -> ActionResult:
                """🎯 通用智能选择处理器"""
                try:
                    selector_map = await browser.get_selector_map()
                    if index not in selector_map:
                        return ActionResult(error=f"Element index {index} not found")
                    
                    dom_element = selector_map[index]
                    if not element_text:
                        element_text = getattr(dom_element, 'text', '') or ''
                    
                    logger.info(f"🎯 通用智能选择: {element_text}")
                    
                    # 使用现有的智能决策机制
                    if hasattr(self.custom_controller, '_make_intelligent_selection_decision'):
                        try:
                            decision_result = await self.custom_controller._make_intelligent_selection_decision(
                                element_text, index, browser, self.digital_human_info
                            )
                            
                            if decision_result.get("should_override"):
                                logger.info(f"🎯 智能决策推荐: {decision_result.get('recommended_choice', '未知')}")
                                
                                # 尝试找到正确选项
                                if hasattr(self.custom_controller, '_find_and_click_correct_option'):
                                    correct_choice_result = await self.custom_controller._find_and_click_correct_option(
                                        decision_result['recommended_choice'], browser
                                    )
                                    
                                    if correct_choice_result.get("success"):
                                        return ActionResult(
                                            extracted_content=f"✅ 智能决策成功: {decision_result['recommended_choice']}"
                                        )
                        except Exception as decision_error:
                            logger.debug(f"⚠️ 智能决策失败: {decision_error}")
                    
                    # 回退到标准点击
                    page = await browser.get_current_page()
                    xpath = '//' + dom_element.xpath
                    element_locator = page.locator(xpath)
                    await element_locator.scroll_into_view_if_needed()
                    await element_locator.click()
                    
                    return ActionResult(
                        extracted_content=f"标准点击: {element_text}",
                        include_in_memory=True
                    )
                    
                except Exception as e:
                    logger.error(f"❌ 通用智能选择失败: {e}")
                    return ActionResult(error=f"通用智能选择失败: {e}")
                
            logger.info("✅ 通用智能选择处理器已注册")
            
        except Exception as e:
            logger.error(f"❌ 通用智能选择处理器注册失败: {e}")

    def _register_question_type_router(self):
        """注册题型检测和路由器"""
        try:
            @self.custom_controller.registry.action(
                'Question type detection and intelligent routing system',
            )
            async def question_type_detection_router(
                index: int,
                browser: BrowserContext
            ) -> ActionResult:
                """🔍 题型检测和智能路由系统"""
                try:
                    selector_map = await browser.get_selector_map()
                    if index not in selector_map:
                        return ActionResult(error=f"Element index {index} not found")
                    
                    dom_element = selector_map[index]
                    element_text = getattr(dom_element, 'text', '') or ''
                    element_tag = getattr(dom_element, 'tag_name', '')
                    
                    # 检测题型
                    question_type = self._detect_question_type(element_text, element_tag)
                    logger.info(f"🔍 路由器检测题型: {question_type} - '{element_text}'")
                    
                    # 根据题型路由到专门处理器
                    if question_type == "country_selection":
                        return await specialized_country_selection_handler(browser, "中国")
                    else:
                        return await universal_intelligent_selection_handler(
                            index, browser, element_text, True
                        )
                        
                except Exception as e:
                    logger.error(f"❌ 题型路由失败: {e}")
                    return await self._safe_fallback_click(browser, index)
                
            logger.info("✅ 题型检测和路由系统已注册")
            
        except Exception as e:
            logger.error(f"❌ 题型路由系统注册失败: {e}")

    def _detect_question_type(self, element_text: str, element_tag: str) -> str:
        """检测题型"""
        text_lower = element_text.lower()
        
        # 国家/地区选择
        country_keywords = [
            "中国", "china", "chinese", "philippines", "菲律宾", "美国", "usa",
            "japan", "日本", "korea", "韩国", "country", "nationality", "国家", "国籍"
        ]
        if any(keyword in text_lower for keyword in country_keywords):
            return "country_selection"
        
        # 下拉选择
        if element_tag.lower() == 'select':
            return "dropdown"
        
        # Radio按钮
        if 'radio' in text_lower or element_tag.lower() == 'input':
            return "radio_button"
        
        return "general"

    async def _handle_country_selection_enhanced(self, browser, page, dom_element, element_text, index):
        """增强版国家选择处理"""
        try:
            # 使用专业国家选择处理器
            if hasattr(self.custom_controller.registry, 'specialized_country_selection_handler'):
                result = await specialized_country_selection_handler(browser, "中国")
                if result and "成功" in result.extracted_content:
                    return result
            
            # 回退处理
            return await self._safe_fallback_click(browser, index)
            
        except Exception as e:
            logger.error(f"❌ 增强国家选择处理失败: {e}")
            return await self._safe_fallback_click(browser, index)

    async def _handle_radio_selection_enhanced(self, browser, page, dom_element, element_text, index):
        """增强版Radio选择处理"""
        try:
            # 使用通用智能处理
            if hasattr(self.custom_controller.registry, 'universal_intelligent_selection_handler'):
                result = await universal_intelligent_selection_handler(index, browser, element_text, True)
                if result and not result.error:
                    return result
            
            return await self._safe_fallback_click(browser, index)
            
        except Exception as e:
            logger.error(f"❌ 增强Radio选择处理失败: {e}")
            return await self._safe_fallback_click(browser, index)

    async def _handle_dropdown_selection_enhanced(self, browser, page, dom_element, element_text, index):
        """增强版下拉选择处理"""
        try:
            # 尝试智能下拉处理
            if hasattr(self.custom_controller, 'intelligent_option_discovery_engine'):
                discovery_result = await self.custom_controller.intelligent_option_discovery_engine(
                    page, self.digital_human_info,
                    target_question_context="dropdown_selection",
                    search_scope="general"
                )
                
                if discovery_result.get('success') and discovery_result.get('recommended_option'):
                    recommended = discovery_result['recommended_option']
                    try:
                        xpath = '//' + dom_element.xpath
                        await page.select_option(xpath, value=recommended.get('value', ''))
                        return ActionResult(
                            extracted_content=f"✅ 智能下拉选择成功: {recommended.get('text', '未知')}"
                        )
                    except:
                        pass
            
            return await self._safe_fallback_click(browser, index)
            
        except Exception as e:
            logger.error(f"❌ 增强下拉选择处理失败: {e}")
            return await self._safe_fallback_click(browser, index)

    async def _handle_general_selection_enhanced(self, browser, page, dom_element, element_text, index):
        """增强版通用选择处理"""
        try:
            # 使用通用智能处理
            if hasattr(self.custom_controller.registry, 'universal_intelligent_selection_handler'):
                result = await universal_intelligent_selection_handler(index, browser, element_text, True)
                if result and not result.error:
                    return result
            
            return await self._safe_fallback_click(browser, index)
            
        except Exception as e:
            logger.error(f"❌ 增强通用选择处理失败: {e}")
            return await self._safe_fallback_click(browser, index)

    async def _safe_fallback_click(self, browser, index):
        """安全回退点击"""
        try:
            page = await browser.get_current_page()
            selector_map = await browser.get_selector_map()
            
            if index in selector_map:
                dom_element = selector_map[index]
                xpath = '//' + dom_element.xpath
                element_locator = page.locator(xpath)
                await element_locator.scroll_into_view_if_needed()
                await asyncio.sleep(0.3)
                await element_locator.click()
                
                element_text = getattr(dom_element, 'text', '') or ''
                return ActionResult(
                    extracted_content=f"安全回退点击: {element_text}",
                    include_in_memory=True
                )
            else:
                return ActionResult(error=f"Element index {index} not found")
        
        except Exception as e:
            logger.error(f"❌ 安全回退点击失败: {e}")
            return ActionResult(error=f"安全回退点击失败: {e}")


def apply_intelligent_questionnaire_fix(custom_controller, digital_human_info):
    """应用智能问卷修复方案"""
    try:
        logger.info("🚀 开始应用智能问卷完整修复方案...")
        
        fix_engine = IntelligentQuestionnaireFixEngine()
        fix_engine.apply_complete_fix(custom_controller, digital_human_info)
        
        logger.info("✅ 智能问卷修复方案应用完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 智能问卷修复方案应用失败: {e}")
        return False 