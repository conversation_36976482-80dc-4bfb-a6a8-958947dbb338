#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🌍 通用多国数字人智能识别系统测试脚本
测试新的通用多国数字人识别功能，确保每个国家的数字人都能正确识别对应国家选项
"""

import asyncio
import sys
import os
import logging
from typing import Dict, Any

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_universal_nationality_detection.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

async def test_universal_nationality_detection():
    """🌍 测试通用多国数字人智能识别系统"""
    
    print("🌍 通用多国数字人智能识别系统测试开始")
    print("=" * 80)
    
    test_results = {
        'total_tests': 0,
        'passed_tests': 0,
        'failed_tests': 0,
        'test_details': []
    }
    
    try:
        # 1. 测试CustomController导入
        print("\n🔍 1. 测试CustomController导入...")
        test_results['total_tests'] += 1
        
        try:
            from src.controller.custom_controller import CustomController
            controller = CustomController()
            print("   ✅ CustomController导入成功")
            test_results['passed_tests'] += 1
            test_results['test_details'].append("✅ CustomController导入成功")
        except Exception as e:
            print(f"   ❌ CustomController导入失败: {e}")
            test_results['failed_tests'] += 1
            test_results['test_details'].append(f"❌ CustomController导入失败: {e}")
            return test_results
        
        # 2. 测试多国数字人国籍检测
        print("\n🔍 2. 测试多国数字人国籍检测...")
        test_results['total_tests'] += 1
        
        # 测试数据：各国数字人
        test_personas = [
            # 中国数字人
            {"name": "刘志强", "residence": "北京丰台", "expected": "中国"},
            {"name": "张小娟", "residence": "上海", "expected": "中国"},
            {"name": "李小芳", "residence": "广州", "expected": "中国"},
            
            # 日本数字人
            {"name": "波多野结衣", "residence": "东京", "expected": "日本"},
            {"name": "田中さくら", "residence": "大阪", "expected": "日本"},
            {"name": "yamada yuki", "residence": "kyoto", "expected": "日本"},
            
            # 美国数字人
            {"name": "John Smith", "residence": "New York", "expected": "美国"},
            {"name": "Emily Johnson", "residence": "Los Angeles", "expected": "美国"},
            {"name": "Michael Brown", "residence": "Chicago", "expected": "美国"},
            
            # 英国数字人
            {"name": "James Wilson", "residence": "London", "expected": "英国"},
            {"name": "Emma Davies", "residence": "Manchester", "expected": "英国"},
            
            # 韩国数字人
            {"name": "Kim Min-jun", "residence": "Seoul", "expected": "韩国"},
            {"name": "Park Ji-hye", "residence": "Busan", "expected": "韩国"},
            
            # 德国数字人
            {"name": "Hans Müller", "residence": "Berlin", "expected": "德国"},
            {"name": "Anna Schmidt", "residence": "Munich", "expected": "德国"},
            
            # 法国数字人
            {"name": "Pierre Martin", "residence": "Paris", "expected": "法国"},
            {"name": "Marie Dubois", "residence": "Lyon", "expected": "法国"},
            
            # 澳大利亚数字人
            {"name": "Jack Thompson", "residence": "Sydney", "expected": "澳大利亚"},
            {"name": "Sarah Wilson", "residence": "Melbourne", "expected": "澳大利亚"},
            
            # 加拿大数字人
            {"name": "David Miller", "residence": "Toronto", "expected": "加拿大"},
            {"name": "Lisa Anderson", "residence": "Vancouver", "expected": "加拿大"},
        ]
        
        nationality_detection_success = 0
        nationality_detection_total = len(test_personas)
        
        for persona in test_personas:
            try:
                detected_country = await controller._detect_persona_nationality(
                    persona["name"], 
                    persona["residence"]
                )
                
                if detected_country == persona["expected"]:
                    print(f"   ✅ {persona['name']} ({persona['residence']}) -> {detected_country}")
                    nationality_detection_success += 1
                else:
                    print(f"   ❌ {persona['name']} ({persona['residence']}) -> 期望:{persona['expected']}, 实际:{detected_country}")
                    
            except Exception as e:
                print(f"   ❌ {persona['name']} 检测失败: {e}")
        
        nationality_accuracy = (nationality_detection_success / nationality_detection_total) * 100
        print(f"\n   📊 数字人国籍检测准确率: {nationality_detection_success}/{nationality_detection_total} ({nationality_accuracy:.1f}%)")
        
        if nationality_accuracy >= 90:
            test_results['passed_tests'] += 1
            test_results['test_details'].append(f"✅ 数字人国籍检测准确率: {nationality_accuracy:.1f}%")
        else:
            test_results['failed_tests'] += 1
            test_results['test_details'].append(f"❌ 数字人国籍检测准确率过低: {nationality_accuracy:.1f}%")
        
        # 3. 测试多国选项国籍检测
        print("\n🔍 3. 测试多国选项国籍检测...")
        test_results['total_tests'] += 1
        
        # 测试数据：各国选项
        test_options = [
            # 中国选项
            {"text": "中国 (简体中文)", "expected": "中国"},
            {"text": "China (Simplified Chinese)", "expected": "中国"},
            {"text": "香港 (繁体中文)", "expected": "中国"},
            
            # 日本选项
            {"text": "日本 (Japanese)", "expected": "日本"},
            {"text": "Japan (日本語)", "expected": "日本"},
            {"text": "東京 (Tokyo)", "expected": "日本"},
            
            # 美国选项
            {"text": "United States (English)", "expected": "美国"},
            {"text": "USA (American English)", "expected": "美国"},
            {"text": "America (US English)", "expected": "美国"},
            
            # 英国选项
            {"text": "United Kingdom (British English)", "expected": "英国"},
            {"text": "UK (English)", "expected": "英国"},
            {"text": "Britain (British)", "expected": "英国"},
            
            # 韩国选项
            {"text": "Korea (Korean)", "expected": "韩国"},
            {"text": "South Korea (한국어)", "expected": "韩国"},
            {"text": "한국 (Korean)", "expected": "韩国"},
            
            # 德国选项
            {"text": "Germany (Deutsch)", "expected": "德国"},
            {"text": "Deutschland (German)", "expected": "德国"},
            
            # 法国选项
            {"text": "France (Français)", "expected": "法国"},
            {"text": "French (France)", "expected": "法国"},
            
            # 澳大利亚选项
            {"text": "Australia (English)", "expected": "澳大利亚"},
            {"text": "Australian English", "expected": "澳大利亚"},
            
            # 加拿大选项
            {"text": "Canada (English)", "expected": "加拿大"},
            {"text": "Canadian English", "expected": "加拿大"},
        ]
        
        option_detection_success = 0
        option_detection_total = len(test_options)
        
        for option in test_options:
            try:
                detected_country = await controller._detect_option_nationality(option["text"])
                
                if detected_country == option["expected"]:
                    print(f"   ✅ {option['text']} -> {detected_country}")
                    option_detection_success += 1
                else:
                    print(f"   ❌ {option['text']} -> 期望:{option['expected']}, 实际:{detected_country}")
                    
            except Exception as e:
                print(f"   ❌ {option['text']} 检测失败: {e}")
        
        option_accuracy = (option_detection_success / option_detection_total) * 100
        print(f"\n   📊 选项国籍检测准确率: {option_detection_success}/{option_detection_total} ({option_accuracy:.1f}%)")
        
        if option_accuracy >= 90:
            test_results['passed_tests'] += 1
            test_results['test_details'].append(f"✅ 选项国籍检测准确率: {option_accuracy:.1f}%")
        else:
            test_results['failed_tests'] += 1
            test_results['test_details'].append(f"❌ 选项国籍检测准确率过低: {option_accuracy:.1f}%")
        
        # 4. 测试完整的选项偏好评分系统
        print("\n🔍 4. 测试完整的选项偏好评分系统...")
        test_results['total_tests'] += 1
        
        # 测试场景：各国数字人选择对应国家选项
        test_scenarios = [
            # 中国数字人选择中国选项
            {
                "persona": {"name": "刘志强", "residence": "北京丰台", "gender": "男", "age": "35"},
                "option": "中国 (简体中文)",
                "expected_score_range": (0.95, 1.0),
                "description": "中国数字人选择中国选项"
            },
            # 日本数字人选择日本选项
            {
                "persona": {"name": "波多野结衣", "residence": "东京", "gender": "女", "age": "28"},
                "option": "日本 (Japanese)",
                "expected_score_range": (0.95, 1.0),
                "description": "日本数字人选择日本选项"
            },
            # 美国数字人选择美国选项
            {
                "persona": {"name": "John Smith", "residence": "New York", "gender": "男", "age": "32"},
                "option": "United States (English)",
                "expected_score_range": (0.95, 1.0),
                "description": "美国数字人选择美国选项"
            },
            # 中国数字人避免澳大利亚选项
            {
                "persona": {"name": "张小娟", "residence": "上海", "gender": "女", "age": "29"},
                "option": "Australia (English)",
                "expected_score_range": (0.0, 0.1),
                "description": "中国数字人避免澳大利亚选项"
            },
            # 日本数字人避免韩国选项
            {
                "persona": {"name": "田中さくら", "residence": "大阪", "gender": "女", "age": "26"},
                "option": "Korea (Korean)",
                "expected_score_range": (0.0, 0.1),
                "description": "日本数字人避免韩国选项"
            },
        ]
        
        scoring_success = 0
        scoring_total = len(test_scenarios)
        
        for scenario in test_scenarios:
            try:
                score = await controller._calculate_option_preference_score(
                    scenario["option"],
                    scenario["persona"],
                    "country_language"
                )
                
                min_score, max_score = scenario["expected_score_range"]
                if min_score <= score <= max_score:
                    print(f"   ✅ {scenario['description']}: 评分 {score:.3f} (期望范围: {min_score}-{max_score})")
                    scoring_success += 1
                else:
                    print(f"   ❌ {scenario['description']}: 评分 {score:.3f} (期望范围: {min_score}-{max_score})")
                    
            except Exception as e:
                print(f"   ❌ {scenario['description']} 评分失败: {e}")
        
        scoring_accuracy = (scoring_success / scoring_total) * 100
        print(f"\n   📊 选项偏好评分准确率: {scoring_success}/{scoring_total} ({scoring_accuracy:.1f}%)")
        
        if scoring_accuracy >= 80:
            test_results['passed_tests'] += 1
            test_results['test_details'].append(f"✅ 选项偏好评分准确率: {scoring_accuracy:.1f}%")
        else:
            test_results['failed_tests'] += 1
            test_results['test_details'].append(f"❌ 选项偏好评分准确率过低: {scoring_accuracy:.1f}%")
        
        # 5. 测试特殊案例：波多野结衣选择日本
        print("\n🔍 5. 测试特殊案例：波多野结衣选择日本...")
        test_results['total_tests'] += 1
        
        try:
            # 波多野结衣的信息
            hatano_yui = {
                "name": "波多野结衣",
                "residence": "东京",
                "gender": "女",
                "age": "28"
            }
            
            # 日本选项
            japan_option = "日本 (Japanese)"
            japan_score = await controller._calculate_option_preference_score(
                japan_option, hatano_yui, "country_language"
            )
            
            # 其他国家选项
            china_option = "中国 (简体中文)"
            china_score = await controller._calculate_option_preference_score(
                china_option, hatano_yui, "country_language"
            )
            
            usa_option = "United States (English)"
            usa_score = await controller._calculate_option_preference_score(
                usa_option, hatano_yui, "country_language"
            )
            
            print(f"   📊 波多野结衣选项评分:")
            print(f"      🇯🇵 日本选项: {japan_score:.3f}")
            print(f"      🇨🇳 中国选项: {china_score:.3f}")
            print(f"      🇺🇸 美国选项: {usa_score:.3f}")
            
            # 验证日本选项得分最高
            if japan_score > china_score and japan_score > usa_score and japan_score >= 0.95:
                print("   ✅ 波多野结衣正确识别并选择日本选项")
                test_results['passed_tests'] += 1
                test_results['test_details'].append("✅ 波多野结衣正确识别并选择日本选项")
            else:
                print("   ❌ 波多野结衣未能正确识别日本选项")
                test_results['failed_tests'] += 1
                test_results['test_details'].append("❌ 波多野结衣未能正确识别日本选项")
                
        except Exception as e:
            print(f"   ❌ 波多野结衣测试失败: {e}")
            test_results['failed_tests'] += 1
            test_results['test_details'].append(f"❌ 波多野结衣测试失败: {e}")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        test_results['failed_tests'] += 1
        test_results['test_details'].append(f"❌ 测试过程错误: {e}")
    
    # 输出测试结果
    print("\n" + "=" * 80)
    print("🎯 通用多国数字人智能识别系统测试结果")
    print("=" * 80)
    
    success_rate = (test_results['passed_tests'] / test_results['total_tests']) * 100 if test_results['total_tests'] > 0 else 0
    
    print(f"📊 总体测试结果:")
    print(f"   总测试数: {test_results['total_tests']}")
    print(f"   通过测试: {test_results['passed_tests']}")
    print(f"   失败测试: {test_results['failed_tests']}")
    print(f"   成功率: {success_rate:.1f}%")
    
    print(f"\n📋 详细测试结果:")
    for detail in test_results['test_details']:
        print(f"   {detail}")
    
    if success_rate >= 80:
        print(f"\n🎉 通用多国数字人智能识别系统测试成功！")
        print(f"✅ 系统现在支持全球多国数字人智能识别")
        print(f"✅ 每个国家的数字人都能自动识别对应国家选项")
        print(f"✅ 波多野结衣等日本数字人能正确选择日本选项")
    else:
        print(f"\n⚠️ 通用多国数字人智能识别系统需要进一步优化")
    
    return test_results

if __name__ == "__main__":
    asyncio.run(test_universal_nationality_detection()) 