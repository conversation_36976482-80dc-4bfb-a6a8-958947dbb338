# 🔥 终极浏览器稳定性修复系统
# 解决所有浏览器连接、DOM执行上下文、gRPC资源竞争和资源清理问题

import asyncio
import logging
import time
from typing import Dict, Any, Optional
from contextlib import asynccontextmanager
import weakref
import aiohttp

logger = logging.getLogger(__name__)

class UltimateBrowserStabilityManager:
    """终极浏览器稳定性管理器"""
    
    def __init__(self):
        self.browser_contexts = weakref.WeakValueDictionary()
        self.context_locks = {}
        self.dom_operation_locks = {}
        self.grpc_semaphore = asyncio.Semaphore(3)
        self.page_navigation_states = {}
        self.resource_cleanup_registry = {}
        
    async def create_ultra_stable_browser_context(self, debug_port: int, profile_id: str) -> Dict[str, Any]:
        """创建超稳定浏览器上下文"""
        context_key = f"{profile_id}_{debug_port}"
        
        if context_key in self.browser_contexts:
            existing_context = self.browser_contexts[context_key]
            if await self._verify_context_health(existing_context):
                logger.info(f"✅ 复用现有稳定浏览器上下文: {context_key}")
                return {"success": True, "context": existing_context, "reused": True}
        
        try:
            if context_key not in self.context_locks:
                self.context_locks[context_key] = asyncio.Lock()
                self.dom_operation_locks[context_key] = asyncio.Lock()
            
            async with self.context_locks[context_key]:
                from browser_use.browser.browser import Browser, BrowserConfig
                from browser_use.browser.context import BrowserContextConfig
                
                browser_config = BrowserConfig(
                    headless=False,
                    disable_security=True,
                    browser_binary_path=None,
                    cdp_url=f"http://127.0.0.1:{debug_port}",
                    extra_chromium_args=[
                        "--no-first-run",
                        "--no-default-browser-check", 
                        "--disable-background-timer-throttling",
                        "--disable-backgrounding-occluded-windows",
                        "--disable-renderer-backgrounding",
                        "--disable-features=TranslateUI",
                        "--disable-ipc-flooding-protection",
                        "--disable-hang-monitor",
                        "--disable-prompt-on-repost",
                        "--disable-domain-reliability",
                        "--aggressive-cache-discard",
                        "--max_old_space_size=4096",
                    ],
                    new_context_config=BrowserContextConfig(
                        window_width=1280,
                        window_height=1100,  # 修复：与WebUI设置保持一致
                        user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                        is_mobile=False,
                        has_touch=False,
                        viewport_width=1280,
                        viewport_height=1100,  # 修复：与WebUI设置保持一致
                        device_scale_factor=1.0,
                        locale="zh-CN",
                        timezone_id="Asia/Shanghai"
                    )
                )
                
                browser = Browser(config=browser_config)
                
                context_config = BrowserContextConfig(
                    user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                    is_mobile=False,
                    has_touch=False,
                    viewport_width=1280,
                    viewport_height=1100,  # 修复：与WebUI设置保持一致
                    device_scale_factor=1.0,
                    locale="zh-CN",
                    timezone_id="Asia/Shanghai",
                    extra_http_headers={
                        "Sec-CH-UA-Mobile": "?0",
                        "Sec-CH-UA-Platform": '"macOS"',
                        "Sec-CH-UA": '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
                        "Cache-Control": "no-cache",
                        "Pragma": "no-cache"
                    }
                )
                
                browser_context = await browser.new_context(config=context_config)
                
                await self._inject_dom_protection_script(browser_context)
                await self._start_context_health_monitor(browser_context, context_key)
                
                self.browser_contexts[context_key] = browser_context
                self.resource_cleanup_registry[context_key] = {
                    "browser": browser,
                    "context": browser_context,
                    "profile_id": profile_id,
                    "debug_port": debug_port,
                    "created_at": time.time()
                }
                
                logger.info(f"✅ 超稳定浏览器上下文创建成功: {context_key}")
                return {"success": True, "context": browser_context, "browser": browser, "reused": False}
                
        except Exception as e:
            logger.error(f"❌ 超稳定浏览器上下文创建失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def _inject_dom_protection_script(self, browser_context):
        """注入DOM保护脚本"""
        protection_script = """
        (function() {
            const originalEvaluate = document.evaluate;
            document.evaluate = function(...args) {
                try {
                    return originalEvaluate.apply(this, args);
                } catch (e) {
                    if (e.message.includes('destroyed')) {
                        console.warn('DOM上下文保护：拦截了销毁的上下文访问');
                        return null;
                    }
                    throw e;
                }
            };
            
            let navigationInProgress = false;
            const originalPushState = history.pushState;
            const originalReplaceState = history.replaceState;
            
            history.pushState = function(...args) {
                navigationInProgress = true;
                setTimeout(() => navigationInProgress = false, 2000);
                return originalPushState.apply(this, args);
            };
            
            history.replaceState = function(...args) {
                navigationInProgress = true;
                setTimeout(() => navigationInProgress = false, 2000);
                return originalReplaceState.apply(this, args);
            };
            
            window.isNavigationInProgress = () => navigationInProgress;
            window.domProtectionActive = true;
            
            console.log('🛡️ DOM执行上下文保护已激活');
        })();
        """
        
        try:
            await browser_context.add_init_script(protection_script)
            logger.info("✅ DOM保护脚本注入成功")
        except Exception as e:
            logger.warning(f"⚠️ DOM保护脚本注入失败: {e}")
    
    async def _verify_context_health(self, browser_context) -> bool:
        """验证浏览器上下文健康状态"""
        try:
            pages = browser_context.pages
            if pages:
                await pages[0].evaluate("document.readyState")
                return True
            return False
        except:
            return False
    
    async def _start_context_health_monitor(self, browser_context, context_key):
        """启动上下文健康监控"""
        async def health_monitor():
            while context_key in self.browser_contexts:
                try:
                    await asyncio.sleep(30)
                    if not await self._verify_context_health(browser_context):
                        logger.warning(f"⚠️ 检测到上下文不健康: {context_key}")
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    logger.error(f"❌ 健康监控错误: {e}")
        
        asyncio.create_task(health_monitor())
    
    @asynccontextmanager
    async def safe_dom_operation(self, context_key: str):
        """安全DOM操作上下文管理器"""
        if context_key not in self.dom_operation_locks:
            self.dom_operation_locks[context_key] = asyncio.Lock()
        
        async with self.dom_operation_locks[context_key]:
            if context_key in self.page_navigation_states:
                if self.page_navigation_states[context_key]:
                    await asyncio.sleep(2)
            
            try:
                yield
            except Exception as e:
                if "execution context was destroyed" in str(e).lower():
                    logger.warning(f"🛡️ DOM保护：拦截了执行上下文销毁错误: {context_key}")
                    self.page_navigation_states[context_key] = True
                    await asyncio.sleep(3)
                    self.page_navigation_states[context_key] = False
                else:
                    raise
    
    @asynccontextmanager 
    async def safe_grpc_operation(self):
        """安全gRPC操作上下文管理器"""
        async with self.grpc_semaphore:
            try:
                yield
            except Exception as e:
                if "resource temporarily unavailable" in str(e).lower():
                    logger.warning("🛡️ gRPC保护：拦截了资源不可用错误，等待重试")
                    await asyncio.sleep(1)
                else:
                    raise
    
    async def force_cleanup_adspower_resources(self, profile_id: str) -> Dict[str, Any]:
        """强制清理AdsPower资源"""
        logger.info(f"🔥 开始强制清理AdsPower资源: {profile_id}")
        
        try:
            # 第一步：停止浏览器实例
            stop_success = False
            try:
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                    async with session.post(
                        "http://local.adspower.net:50325/api/v1/browser/stop",
                        json={"user_id": profile_id}
                    ) as response:
                        result = await response.json()
                        if result.get("code") == 0 or "not open" in result.get("msg", "").lower():
                            stop_success = True
                            logger.info(f"✅ 浏览器实例停止成功: {profile_id}")
                        else:
                            logger.warning(f"⚠️ 浏览器停止响应: {result}")
            except Exception as e:
                logger.warning(f"⚠️ 浏览器停止请求失败: {e}")
            
            await asyncio.sleep(3)
            
            # 第二步：删除配置文件
            delete_success = False
            try:
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=15)) as session:
                    async with session.post(
                        "http://local.adspower.net:50325/api/v1/user/delete",
                        json={"user_ids": [profile_id]}
                    ) as response:
                        result = await response.json()
                        if result.get("code") == 0:
                            delete_success = True
                            logger.info(f"✅ 配置文件删除成功: {profile_id}")
                        else:
                            logger.error(f"❌ 配置文件删除失败: {result}")
            except Exception as e:
                logger.error(f"❌ 配置文件删除请求失败: {e}")
            
            # 第三步：清理本地资源
            context_keys_to_remove = [k for k in self.resource_cleanup_registry.keys() if profile_id in k]
            for key in context_keys_to_remove:
                try:
                    resource_info = self.resource_cleanup_registry.pop(key, {})
                    browser_context = resource_info.get("context")
                    browser = resource_info.get("browser")
                    
                    if browser_context:
                        await browser_context.close()
                    if browser:
                        await browser.close()
                        
                    logger.info(f"✅ 本地资源清理完成: {key}")
                except Exception as e:
                    logger.warning(f"⚠️ 本地资源清理失败: {e}")
            
            # 第四步：验证清理结果
            cleanup_verified = False
            try:
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                    async with session.get("http://local.adspower.net:50325/api/v1/user/list") as response:
                        result = await response.json()
                        if result.get("code") == 0:
                            profiles = result.get("data", {}).get("list", [])
                            profile_exists = any(p.get("user_id") == profile_id for p in profiles)
                            if not profile_exists:
                                cleanup_verified = True
                                logger.info(f"✅ 清理验证成功：配置文件已从AdsPower列表中移除")
                            else:
                                logger.warning(f"⚠️ 清理验证失败：配置文件仍在AdsPower列表中")
            except Exception as e:
                logger.warning(f"⚠️ 清理验证失败: {e}")
            
            cleanup_result = {
                "profile_id": profile_id,
                "browser_stopped": stop_success,
                "profile_deleted": delete_success,
                "cleanup_verified": cleanup_verified,
                "full_success": delete_success and cleanup_verified,
                "timestamp": time.time()
            }
            
            if cleanup_result["full_success"]:
                logger.info(f"🎉 AdsPower资源强制清理完全成功: {profile_id}")
            else:
                logger.error(f"❌ AdsPower资源清理部分失败: {cleanup_result}")
            
            return cleanup_result
            
        except Exception as e:
            logger.error(f"❌ AdsPower资源强制清理异常: {e}")
            return {
                "profile_id": profile_id,
                "browser_stopped": False,
                "profile_deleted": False,
                "cleanup_verified": False,
                "full_success": False,
                "error": str(e),
                "timestamp": time.time()
            }

# 全局稳定性管理器实例
global_stability_manager = UltimateBrowserStabilityManager()

class EnhancedGRPCLLMWrapper:
    """增强型gRPC LLM包装器"""
    
    def __init__(self, llm):
        self.llm = llm
        self.retry_count = 3
        self.retry_delay = 1.0
    
    async def ainvoke(self, *args, **kwargs):
        """增强型异步调用"""
        async with global_stability_manager.safe_grpc_operation():
            for attempt in range(self.retry_count):
                try:
                    return await self.llm.ainvoke(*args, **kwargs)
                except Exception as e:
                    if "resource temporarily unavailable" in str(e).lower() and attempt < self.retry_count - 1:
                        logger.warning(f"🔄 gRPC重试 {attempt + 1}/{self.retry_count}: {e}")
                        await asyncio.sleep(self.retry_delay * (attempt + 1))
                        continue
                    else:
                        raise
    
    def __getattr__(self, name):
        """代理其他方法到原始LLM"""
        return getattr(self.llm, name)

def create_enhanced_llm(original_llm):
    """创建增强型LLM包装器"""
    return EnhancedGRPCLLMWrapper(original_llm) 