#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试AdsPower + WebUI集成
"""

import asyncio
import logging
from typing import Dict, Any

from src.integration.adspower_webui_core import AdsPowerWebUICore

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_integration():
    """测试集成功能"""
    logger = logging.getLogger(__name__)
    
    # 准备数字人信息
    digital_human_info = {
        'personality': '专业、认真、细心',
        'behavior_pattern': '思考后再行动',
        'response_style': '客观、理性'
    }
    
    try:
        # 初始化集成
        integration = AdsPowerWebUICore(digital_human_info)
        logger.info("✅ 集成初始化成功")
        
        # 准备数字人信息
        digital_human_ready = await integration.prepare_digital_human()
        if not digital_human_ready:
            logger.error("❌ 数字人信息准备失败")
            return
        logger.info("✅ 数字人信息准备完成")
        
        # 配置代理
        proxy_configured = await integration.configure_proxy()
        if not proxy_configured:
            logger.error("❌ 代理配置失败")
            return
        logger.info("✅ 代理配置完成")
        
        # 启动浏览器
        browser_started = await integration.start_browser()
        if not browser_started:
            logger.error("❌ 浏览器启动失败")
            return
        logger.info("✅ 浏览器启动成功")
        
        # 导航到测试页面
        test_url = "https://www.example.com"
        navigation_success = await integration.navigate_to_url(test_url)
        if not navigation_success:
            logger.error("❌ 导航失败")
            return
        logger.info("✅ 导航成功")
        
        # 等待一段时间
        await asyncio.sleep(5)
        
        # 清理资源
        await integration.cleanup()
        logger.info("✅ 资源清理完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        raise
        
if __name__ == "__main__":
    asyncio.run(test_integration()) 