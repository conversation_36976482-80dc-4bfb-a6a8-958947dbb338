#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Action拦截器补丁 - 实现100%智能化处理
确保所有click_element_by_index动作都经过智能决策
"""

import asyncio
import random
import logging
from typing import Dict, List, Optional
from browser_use.browser.context import BrowserContext
from browser_use.agent.views import ActionResult

logger = logging.getLogger(__name__)

class ActionInterceptorPatch:
    """Action拦截器补丁类"""
    
    def __init__(self, controller):
        self.controller = controller
        self.digital_human_info = getattr(controller, 'digital_human_info', {})
    
    async def execute_intelligent_click_with_full_engines(
        self, 
        index: int, 
        browser_context: BrowserContext, 
        digital_human_info: Dict
    ) -> ActionResult:
        """🔥 核心方法：使用全部智能引擎执行点击动作"""
        try:
            logger.info(f"🚀 启动全智能引擎处理点击动作 - index: {index}")
            
            # 🎯 第一步：获取页面和元素信息
            page = await browser_context.get_current_page()
            selector_map = await browser_context.get_selector_map()
            
            if index not in selector_map:
                return ActionResult(error=f"Element index {index} not found in selector map")
            
            dom_element = selector_map[index]
            element_text = getattr(dom_element, 'text', '') or ''
            element_tag = getattr(dom_element, 'tag_name', '')
            
            logger.info(f"🔍 目标元素分析: text='{element_text[:100]}...', tag='{element_tag}'")
            
            # 🎯 第二步：智能题型检测和处理
            processing_result = await self._intelligent_question_type_detection_and_processing(
                index, element_text, element_tag, page, browser_context, digital_human_info
            )
            
            if processing_result.get('success'):
                return ActionResult(
                    extracted_content=processing_result.get('message', '智能处理成功'),
                    include_in_memory=True
                )
            
            # 🎯 第三步：如果智能处理未成功，使用安全回退
            logger.info(f"🛡️ 智能处理未成功，使用安全回退点击")
            return await self._safe_fallback_click(browser_context, index, element_text)
            
        except Exception as e:
            logger.error(f"❌ 全智能引擎处理失败: {e}")
            # 最后的安全回退
            return await self._safe_fallback_click(browser_context, index, "未知元素")

    async def _intelligent_question_type_detection_and_processing(
        self, 
        index: int,
        element_text: str, 
        element_tag: str, 
        page, 
        browser_context: BrowserContext, 
        digital_human_info: Dict
    ) -> Dict:
        """🧠 智能题型检测和处理 - 核心智能决策引擎"""
        try:
            logger.info(f"🧠 启动智能题型检测...")
            
            # 🗺️ 【最高优先级】：国家/地区选择题型
            if self._is_country_selection_element(element_text):
                logger.info(f"🗺️ 检测到国家选择题型 - 启用最高优先级处理")
                return await self._handle_country_selection_with_comprehensive_engines(
                    index, element_text, page, browser_context, digital_human_info
                )
            
            # 🎯 【高优先级】：语言选择题型
            if self._is_language_selection_element(element_text):
                logger.info(f"🗣️ 检测到语言选择题型")
                return await self._handle_language_selection_intelligently(
                    index, element_text, page, browser_context, digital_human_info
                )
            
            # 👤 【中优先级】：个人信息题型（性别、年龄、职业等）
            if self._is_personal_info_element(element_text):
                logger.info(f"👤 检测到个人信息题型")
                return await self._handle_personal_info_selection_intelligently(
                    index, element_text, page, browser_context, digital_human_info
                )
            
            # 📊 【中优先级】：态度偏好题型（满意度、频率等）
            if self._is_attitude_preference_element(element_text):
                logger.info(f"📊 检测到态度偏好题型")
                return await self._handle_attitude_preference_intelligently(
                    index, element_text, page, browser_context, digital_human_info
                )
            
            # 🔄 【低优先级】：通用选择题型
            logger.info(f"🔄 使用通用智能选择处理")
            return await self._handle_general_selection_intelligently(
                index, element_text, page, browser_context, digital_human_info
            )
            
        except Exception as e:
            logger.error(f"❌ 智能题型检测失败: {e}")
            return {"success": False, "error": str(e)}

    def _is_country_selection_element(self, element_text: str) -> bool:
        """🗺️ 检测国家选择元素"""
        country_keywords = [
            # 中文关键词
            "中国", "中华", "大陆", "内地", "华人", "汉族",
            "菲律宾", "美国", "日本", "韩国", "澳大利亚", "加拿大", "英国", "法国", "德国",
            "国家", "国籍", "地区", "区域", "居住地", "出生地",
            # 英文关键词
            "china", "chinese", "mainland", "philippines", "usa", "america", "japan", "korea",
            "australia", "canada", "uk", "france", "germany", "country", "nationality", 
            "region", "residence", "birthplace", "homeland"
        ]
        
        text_lower = element_text.lower()
        return any(keyword in text_lower for keyword in country_keywords)

    def _is_language_selection_element(self, element_text: str) -> bool:
        """🗣️ 检测语言选择元素"""
        language_keywords = [
            "english", "中文", "chinese", "简体", "繁体", "traditional", "simplified",
            "bahasa", "indonesia", "filipino", "tagalog", "한국어", "korean", "日本語", "japanese",
            "language", "语言", "lang", "locale", "dialect"
        ]
        text_lower = element_text.lower()
        return any(keyword in text_lower for keyword in language_keywords)

    def _is_personal_info_element(self, element_text: str) -> bool:
        """👤 检测个人信息元素"""
        personal_keywords = [
            "男", "女", "male", "female", "gender", "性别",
            "age", "年龄", "岁", "old", "young",
            "job", "work", "profession", "职业", "工作", "occupation",
            "education", "学历", "degree", "university", "college",
            "income", "salary", "收入", "工资", "薪水", "earnings"
        ]
        text_lower = element_text.lower()
        return any(keyword in text_lower for keyword in personal_keywords)

    def _is_attitude_preference_element(self, element_text: str) -> bool:
        """📊 检测态度偏好元素"""
        attitude_keywords = [
            "满意", "满意度", "satisfied", "satisfaction", "happy",
            "同意", "agree", "disagree", "不同意",
            "喜欢", "like", "dislike", "不喜欢", "prefer", "偏好",
            "经常", "often", "sometimes", "rarely", "never", "总是", "从不",
            "很好", "好", "一般", "不好", "excellent", "good", "fair", "poor",
            "重要", "important", "unimportant", "关键", "crucial"
        ]
        text_lower = element_text.lower()
        return any(keyword in text_lower for keyword in attitude_keywords)

    async def _handle_country_selection_with_comprehensive_engines(
        self, index: int, element_text: str, page, browser_context: BrowserContext, digital_human_info: Dict
    ) -> Dict:
        """🗺️ 使用综合引擎处理国家选择"""
        try:
            logger.info(f"🗺️ 启动综合国家选择引擎...")
            
            # 🎯 确定目标国籍
            target_nationality = self._determine_target_nationality(digital_human_info)
            logger.info(f"🎯 目标国籍: {target_nationality}")
            
            # 🔍 使用智能选项发现引擎
            if hasattr(self.controller, 'intelligent_option_discovery_engine'):
                discovery_result = await self.controller.intelligent_option_discovery_engine(
                    page=page,
                    persona_info=digital_human_info,
                    target_question_context="country_nationality_selection",
                    search_scope="country_language"
                )
                
                if discovery_result.get('success') and discovery_result.get('recommended_option'):
                    recommended = discovery_result['recommended_option']
                    recommended_text = recommended.get('text', '')
                    recommended_index = recommended.get('index', index)
                    
                    logger.info(f"🎯 智能推荐选项: '{recommended_text}' (index: {recommended_index})")
                    
                    # 🎯 验证推荐选项是否符合目标国籍
                    if self._validate_country_recommendation(recommended_text, target_nationality):
                        # 执行推荐的点击
                        click_result = await self._execute_intelligent_click_by_index(
                            browser_context, recommended_index, recommended_text
                        )
                        
                        if click_result.get('success'):
                            return {
                                "success": True,
                                "message": f"✅ 智能国家选择成功: {recommended_text}",
                                "method": "intelligent_discovery",
                                "original_index": index,
                                "selected_index": recommended_index
                            }
            
            # 🔄 备选方案：直接搜索目标国籍
            fallback_result = await self._find_and_select_target_country(
                page, browser_context, target_nationality, digital_human_info
            )
            
            if fallback_result.get('success'):
                return fallback_result
            
            # 🛡️ 最后的安全选择：如果原始选择是合理的，就使用原始选择
            if self._is_acceptable_country_choice(element_text, target_nationality):
                click_result = await self._execute_intelligent_click_by_index(browser_context, index, element_text)
                if click_result.get('success'):
                    return {
                        "success": True,
                        "message": f"✅ 使用原始选择: {element_text}",
                        "method": "original_acceptable"
                    }
            
            return {"success": False, "error": "所有国家选择策略都失败"}
            
        except Exception as e:
            logger.error(f"❌ 综合国家选择引擎失败: {e}")
            return {"success": False, "error": str(e)}

    def _determine_target_nationality(self, digital_human_info: Dict) -> str:
        """确定目标国籍"""
        if digital_human_info:
            residence = digital_human_info.get('residence', '中国')
            location = digital_human_info.get('location', '北京')
            
            # 根据居住地确定国籍
            if any(keyword in residence for keyword in ['中国', '北京', '上海', '深圳', '广州']):
                return "中国"
            elif any(keyword in residence for keyword in ['美国', 'US', 'USA']):
                return "美国"
            elif any(keyword in residence for keyword in ['日本', 'Japan']):
                return "日本"
            
        return "中国"  # 默认国籍

    def _validate_country_recommendation(self, recommended_text: str, target_nationality: str) -> bool:
        """🎯 验证推荐的国家选项是否符合目标"""
        if not recommended_text or not target_nationality:
            return False
        
        text_lower = recommended_text.lower()
        target_lower = target_nationality.lower()
        
        # 直接匹配
        if target_lower in text_lower:
            return True
        
        # 同义词匹配
        country_synonyms = {
            "中国": ["china", "chinese", "mainland", "中华", "大陆"],
            "美国": ["usa", "america", "united states", "us"],
            "日本": ["japan", "japanese", "日本"],
            "韩国": ["korea", "korean", "south korea", "한국"]
        }
        
        if target_nationality in country_synonyms:
            synonyms = country_synonyms[target_nationality]
            return any(synonym in text_lower for synonym in synonyms)
        
        return False

    def _is_acceptable_country_choice(self, element_text: str, target_nationality: str) -> bool:
        """🛡️ 判断原始选择是否可接受"""
        # 如果是"不想回答"、"其他"等选项，通常不可接受
        unacceptable_keywords = ["不想回答", "prefer not", "其他", "other", "skip", "none"]
        text_lower = element_text.lower()
        
        if any(keyword in text_lower for keyword in unacceptable_keywords):
            return False
        
        # 如果包含目标国籍，则可接受
        return self._validate_country_recommendation(element_text, target_nationality)

    async def _find_and_select_target_country(
        self, page, browser_context: BrowserContext, target_nationality: str, digital_human_info: Dict
    ) -> Dict:
        """🔍 直接搜索并选择目标国籍"""
        try:
            logger.info(f"🔍 直接搜索目标国籍: {target_nationality}")
            
            selector_map = await browser_context.get_selector_map()
            
            # 搜索所有包含目标国籍的选项
            matching_options = []
            for idx, element in selector_map.items():
                element_text = getattr(element, 'text', '') or ''
                if self._validate_country_recommendation(element_text, target_nationality):
                    matching_options.append({
                        'index': idx,
                        'text': element_text,
                        'score': self._calculate_country_match_score(element_text, target_nationality)
                    })
            
            if matching_options:
                # 选择得分最高的选项
                best_option = max(matching_options, key=lambda x: x['score'])
                logger.info(f"🎯 找到最佳匹配: '{best_option['text']}' (得分: {best_option['score']})")
                
                click_result = await self._execute_intelligent_click_by_index(
                    browser_context, best_option['index'], best_option['text']
                )
                
                if click_result.get('success'):
                    return {
                        "success": True,
                        "message": f"✅ 直接搜索成功: {best_option['text']}",
                        "method": "direct_search"
                    }
            
            return {"success": False, "error": "未找到匹配的国籍选项"}
            
        except Exception as e:
            logger.error(f"❌ 直接搜索失败: {e}")
            return {"success": False, "error": str(e)}

    def _calculate_country_match_score(self, element_text: str, target_nationality: str) -> float:
        """🎯 计算国家匹配得分"""
        if not element_text or not target_nationality:
            return 0.0
        
        text_lower = element_text.lower()
        target_lower = target_nationality.lower()
        
        # 完全匹配得分最高
        if target_lower == text_lower:
            return 1.0
        
        # 包含匹配
        if target_lower in text_lower:
            return 0.8
        
        # 同义词匹配
        score = 0.0
        country_synonyms = {
            "中国": ["china", "chinese", "mainland", "中华", "大陆", "prc"],
            "美国": ["usa", "america", "united states", "us"],
            "日本": ["japan", "japanese", "日本"],
            "韩国": ["korea", "korean", "south korea", "한국"]
        }
        
        if target_nationality in country_synonyms:
            synonyms = country_synonyms[target_nationality]
            for synonym in synonyms:
                if synonym in text_lower:
                    score = max(score, 0.6)
        
        return score

    async def _execute_intelligent_click_by_index(self, browser_context: BrowserContext, index: int, element_text: str) -> Dict:
        """🖱️ 通过索引执行智能点击"""
        try:
            logger.info(f"🖱️ 执行智能点击: index={index}, text='{element_text}'")
            
            # 获取页面和选择器映射
            page = await browser_context.get_current_page()
            selector_map = await browser_context.get_selector_map()
            
            if index not in selector_map:
                return {"success": False, "error": f"Index {index} not found in selector map"}
            
            dom_element = selector_map[index]
            xpath = '//' + dom_element.xpath
            
            # 🛡️ 反作弊策略：使用Playwright原生点击
            element_locator = page.locator(xpath)
            
            # 确保元素可见
            await element_locator.scroll_into_view_if_needed()
            await asyncio.sleep(random.uniform(0.2, 0.5))  # 人类化延迟
            
            await element_locator.click()
            
            logger.info(f"✅ 智能点击成功: {element_text}")
            return {"success": True, "method": "playwright_click"}
            
        except Exception as e:
            logger.error(f"❌ 智能点击失败: {e}")
            return {"success": False, "error": str(e)}

    async def _handle_language_selection_intelligently(
        self, index: int, element_text: str, page, browser_context: BrowserContext, digital_human_info: Dict
    ) -> Dict:
        """🗣️ 智能处理语言选择"""
        try:
            # 根据数字人信息确定语言偏好
            location = digital_human_info.get('location', '北京')
            residence = digital_human_info.get('residence', '中国')
            
            # 确定目标语言
            if any(keyword in str(location + residence) for keyword in ['中国', '北京', '上海', '深圳']):
                target_language = "中文"
            else:
                target_language = "English"
            
            logger.info(f"🗣️ 目标语言: {target_language}")
            
            # 检查当前选择是否符合目标语言
            if self._validate_language_choice(element_text, target_language):
                click_result = await self._execute_intelligent_click_by_index(browser_context, index, element_text)
                if click_result.get('success'):
                    return {
                        "success": True,
                        "message": f"✅ 语言选择正确: {element_text}",
                        "method": "original_correct"
                    }
            
            # 寻找正确的语言选项
            correct_option = await self._find_correct_language_option(
                browser_context, target_language
            )
            
            if correct_option:
                click_result = await self._execute_intelligent_click_by_index(
                    browser_context, correct_option['index'], correct_option['text']
                )
                if click_result.get('success'):
                    return {
                        "success": True,
                        "message": f"✅ 智能语言选择: {correct_option['text']}",
                        "method": "intelligent_language"
                    }
            
            return {"success": False, "error": "未找到合适的语言选项"}
            
        except Exception as e:
            logger.error(f"❌ 智能语言选择失败: {e}")
            return {"success": False, "error": str(e)}

    def _validate_language_choice(self, element_text: str, target_language: str) -> bool:
        """验证语言选择"""
        text_lower = element_text.lower()
        target_lower = target_language.lower()
        
        if target_lower in text_lower:
            return True
        
        # 语言同义词
        language_synonyms = {
            "中文": ["chinese", "simplified", "traditional", "中文", "简体", "繁体"],
            "english": ["english", "英文", "英语"]
        }
        
        if target_language in language_synonyms:
            synonyms = language_synonyms[target_language]
            return any(synonym in text_lower for synonym in synonyms)
        
        return False

    async def _find_correct_language_option(self, browser_context: BrowserContext, target_language: str) -> Dict:
        """查找正确的语言选项"""
        try:
            selector_map = await browser_context.get_selector_map()
            
            for index, element in selector_map.items():
                element_text = getattr(element, 'text', '') or ''
                if self._validate_language_choice(element_text, target_language):
                    return {
                        'index': index,
                        'text': element_text
                    }
            
            return None
            
        except Exception as e:
            logger.error(f"❌ 查找语言选项失败: {e}")
            return None

    async def _handle_personal_info_selection_intelligently(
        self, index: int, element_text: str, page, browser_context: BrowserContext, digital_human_info: Dict
    ) -> Dict:
        """👤 智能处理个人信息选择"""
        try:
            logger.info(f"👤 智能处理个人信息: {element_text}")
            
            # 根据数字人信息进行智能匹配
            if self._validate_personal_info_choice(element_text, digital_human_info):
                click_result = await self._execute_intelligent_click_by_index(browser_context, index, element_text)
                if click_result.get('success'):
                    return {
                        "success": True,
                        "message": f"✅ 个人信息选择正确: {element_text}",
                        "method": "personal_info_correct"
                    }
            
            # 如果没有找到更好的选项，使用原始选择
            click_result = await self._execute_intelligent_click_by_index(browser_context, index, element_text)
            if click_result.get('success'):
                return {
                    "success": True,
                    "message": f"✅ 使用原始个人信息选择: {element_text}",
                    "method": "original_fallback"
                }
            
            return {"success": False, "error": "个人信息选择失败"}
            
        except Exception as e:
            logger.error(f"❌ 智能个人信息选择失败: {e}")
            return {"success": False, "error": str(e)}

    def _validate_personal_info_choice(self, element_text: str, digital_human_info: Dict) -> bool:
        """验证个人信息选择"""
        text_lower = element_text.lower()
        
        # 性别验证
        gender = digital_human_info.get('gender', '').lower()
        if gender:
            if ('female' in gender or '女' in gender) and ('女' in text_lower or 'female' in text_lower):
                return True
            if ('male' in gender or '男' in gender) and ('男' in text_lower or 'male' in text_lower):
                return True
        
        # 年龄验证
        age = digital_human_info.get('age', '')
        if age:
            try:
                age_num = int(str(age))
                if age_num < 25 and any(keyword in text_lower for keyword in ['18-24', '20-25', 'young']):
                    return True
                elif 25 <= age_num < 35 and any(keyword in text_lower for keyword in ['25-34', '25-35']):
                    return True
                elif 35 <= age_num < 45 and any(keyword in text_lower for keyword in ['35-44', '35-45']):
                    return True
            except:
                pass
        
        # 职业验证
        profession = digital_human_info.get('profession', '').lower()
        if profession and profession in text_lower:
            return True
        
        return False

    async def _handle_attitude_preference_intelligently(
        self, index: int, element_text: str, page, browser_context: BrowserContext, digital_human_info: Dict
    ) -> Dict:
        """📊 智能处理态度偏好选择"""
        try:
            logger.info(f"📊 智能处理态度偏好: {element_text}")
            
            # 生成智能的态度偏好答案
            intelligent_choice = self._generate_intelligent_attitude_choice(element_text, digital_human_info)
            
            if intelligent_choice.get('use_original'):
                click_result = await self._execute_intelligent_click_by_index(browser_context, index, element_text)
                if click_result.get('success'):
                    return {
                        "success": True,
                        "message": f"✅ 态度偏好选择: {element_text}",
                        "method": "attitude_original"
                    }
            
            # 回退到原始选择
            click_result = await self._execute_intelligent_click_by_index(browser_context, index, element_text)
            if click_result.get('success'):
                return {
                    "success": True,
                    "message": f"✅ 态度偏好回退选择: {element_text}",
                    "method": "attitude_fallback"
                }
            
            return {"success": False, "error": "态度偏好选择失败"}
            
        except Exception as e:
            logger.error(f"❌ 智能态度偏好选择失败: {e}")
            return {"success": False, "error": str(e)}

    def _generate_intelligent_attitude_choice(self, element_text: str, digital_human_info: Dict) -> Dict:
        """生成智能的态度偏好选择"""
        text_lower = element_text.lower()
        
        # 根据数字人的特征生成态度倾向
        age = digital_human_info.get('age', 30)
        try:
            age_num = int(str(age))
        except:
            age_num = 30
        
        # 年轻人更积极，年长者更保守
        if age_num < 30:
            attitude_tendency = "positive"  # 积极
        elif age_num < 50:
            attitude_tendency = "neutral"   # 中性
        else:
            attitude_tendency = "conservative"  # 保守
        
        # 检查当前选择是否符合态度倾向
        if attitude_tendency == "positive":
            positive_keywords = ["很好", "满意", "喜欢", "同意", "经常", "重要", "excellent", "good", "satisfied", "agree", "often", "important"]
            if any(keyword in text_lower for keyword in positive_keywords):
                return {"use_original": True, "preferred_attitude": "positive"}
        
        elif attitude_tendency == "neutral":
            neutral_keywords = ["一般", "还可以", "有时", "偶尔", "fair", "okay", "sometimes", "occasionally"]
            if any(keyword in text_lower for keyword in neutral_keywords):
                return {"use_original": True, "preferred_attitude": "neutral"}
        
        elif attitude_tendency == "conservative":
            conservative_keywords = ["不确定", "很少", "谨慎", "传统", "rarely", "uncertain", "traditional", "careful"]
            if any(keyword in text_lower for keyword in conservative_keywords):
                return {"use_original": True, "preferred_attitude": "conservative"}
        
        return {"use_original": False, "preferred_attitude": attitude_tendency}

    async def _handle_general_selection_intelligently(
        self, index: int, element_text: str, page, browser_context: BrowserContext, digital_human_info: Dict
    ) -> Dict:
        """🔄 智能处理通用选择"""
        try:
            logger.info(f"🔄 智能处理通用选择: {element_text}")
            
            # 使用现有的智能选择决策逻辑
            if digital_human_info and hasattr(self.controller, '_make_intelligent_selection_decision'):
                decision_result = await self.controller._make_intelligent_selection_decision(
                    element_text, index, browser_context, digital_human_info
                )
                
                if decision_result.get("should_override"):
                    logger.info(f"🎯 智能决策推荐: {decision_result.get('recommended_choice', '未知')}")
                    
                    if hasattr(self.controller, '_find_and_click_correct_option'):
                        correct_choice_result = await self.controller._find_and_click_correct_option(
                            decision_result['recommended_choice'], browser_context
                        )
                        
                        if correct_choice_result.get("success"):
                            return {
                                "success": True,
                                "message": f"✅ 智能通用选择: {decision_result['recommended_choice']}",
                                "method": "intelligent_general"
                            }
            
            # 回退到原始选择
            click_result = await self._execute_intelligent_click_by_index(browser_context, index, element_text)
            if click_result.get('success'):
                return {
                    "success": True,
                    "message": f"✅ 通用选择: {element_text}",
                    "method": "general_original"
                }
            
            return {"success": False, "error": "通用选择失败"}
            
        except Exception as e:
            logger.error(f"❌ 智能通用选择失败: {e}")
            return {"success": False, "error": str(e)}

    async def _safe_fallback_click(self, browser_context: BrowserContext, index: int, element_text: str = "") -> ActionResult:
        """🛡️ 安全回退点击"""
        try:
            page = await browser_context.get_current_page()
            selector_map = await browser_context.get_selector_map()
            
            if index in selector_map:
                dom_element = selector_map[index]
                xpath = '//' + dom_element.xpath
                element_locator = page.locator(xpath)
                
                # 确保元素可见
                await element_locator.scroll_into_view_if_needed()
                await asyncio.sleep(random.uniform(0.2, 0.5))
                
                await element_locator.click()
                
                if not element_text:
                    element_text = getattr(dom_element, 'text', '') or ''
                
                return ActionResult(
                    extracted_content=f"✅ 安全回退点击成功: {element_text}",
                    include_in_memory=True
                )
            else:
                return ActionResult(error=f"Element index {index} not found")
        
        except Exception as e:
            logger.error(f"❌ 安全回退点击失败: {e}")
            return ActionResult(error=f"安全回退点击失败: {e}")


def apply_action_interceptor_patch(controller):
    """应用Action拦截器补丁到CustomController"""
    try:
        logger.info("🔥 开始应用Action拦截器补丁...")
        
        # 创建补丁实例
        patch = ActionInterceptorPatch(controller)
        
        # 保存原始的act方法
        original_act = controller.act
        
        # 定义新的act方法
        async def patched_act(
            action,
            browser_context=None,
            page_extraction_llm=None,
            sensitive_data=None,
            available_file_paths=None,
            context=None,
        ):
            """🔥 【核心Action拦截器】- 实现100%智能化处理"""
            try:
                # 🔥 第一步：参数完整性检查和修复
                browser_context = await controller._ensure_browser_context(browser_context)
                digital_human_info = controller._get_digital_human_info_safely()
                
                # 🎯 【核心拦截器】：所有click_element_by_index动作100%智能化处理
                if hasattr(action, 'action') and action.action == 'click_element_by_index':
                    logger.info(f"🎯 Action拦截器启动 - 强制智能化处理click_element_by_index")
                    
                    # 🔥 核心：直接调用我们的智能处理引擎，绕过browser-use原生逻辑
                    return await patch.execute_intelligent_click_with_full_engines(
                        action.index, browser_context, digital_human_info
                    )
                
                # 🎯 【智能拦截】：其他需要智能处理的动作
                elif (hasattr(action, 'action') and action.action in ['input_text', 'select_option'] and 
                      digital_human_info):
                    logger.info(f"🧠 检测到智能输入动作: {action.action}")
                    
                    # 应用智能预处理
                    if hasattr(controller, '_apply_intelligent_processing'):
                        action = await controller._apply_intelligent_processing(action, browser_context, digital_human_info)
                
                # 🔥 第二步：执行原始动作（非点击类动作）
                return await controller._execute_action_safely(action, browser_context, 
                                                       page_extraction_llm=page_extraction_llm,
                                                       sensitive_data=sensitive_data,
                                                       available_file_paths=available_file_paths,
                                                       context=context)
                
            except Exception as e:
                # 🔥 第三步：统一错误处理和恢复
                logger.error(f"❌ Action拦截器执行失败: {e}")
                if hasattr(controller, '_handle_action_failure'):
                    return await controller._handle_action_failure(action, browser_context, e)
                else:
                    return ActionResult(error=f"Action拦截器执行失败: {e}")
        
        # 替换act方法
        controller.act = patched_act
        
        logger.info("✅ Action拦截器补丁应用成功！")
        return True
        
    except Exception as e:
        logger.error(f"❌ Action拦截器补丁应用失败: {e}")
        return False 