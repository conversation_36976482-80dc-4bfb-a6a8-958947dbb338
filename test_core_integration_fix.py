#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
核心集成修复验证测试
验证AdsPowerWebUIIntegration类是否能够正常工作
"""

import asyncio
import logging
import json
from typing import Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_core_integration():
    """测试核心集成功能"""
    print("🔥 开始测试核心AdsPowerWebUIIntegration功能...")
    
    try:
        # 测试导入
        from adspower_browser_use_integration import AdsPowerWebUIIntegration
        print("✅ AdsPowerWebUIIntegration 导入成功")
        
        # 测试实例化
        integration = AdsPowerWebUIIntegration()
        print("✅ AdsPowerWebUIIntegration 实例化成功")
        
        # 模拟数字人信息
        digital_human = {
            'id': 1001,
            'name': '张小雅',
            'age': 28,
            'gender': '女',
            'profession': '产品经理',
            'income': '12000',
            'education': '本科',
            'location': '北京市',
            'interests': ['科技', '阅读', '健身'],
            'personality': '理性消费者'
        }
        
        # LLM配置
        llm_config = {
            'provider': 'deepseek',
            'api_key': 'mock_key',
            'model': 'deepseek-chat'
        }
        
        print("✅ 测试配置准备完成")
        print(f"  数字人: {digital_human['name']}")
        print(f"  LLM: {llm_config['provider']}")
        
        # 验证关键方法存在
        assert hasattr(integration, 'run_questionnaire'), "❌ 缺少run_questionnaire方法"
        print("✅ 核心方法验证成功")
        
        print("🎉 核心集成修复验证通过！")
        return True
        
    except Exception as e:
        print(f"❌ 核心集成测试失败: {e}")
        return False

async def test_integration_system():
    """测试集成系统组件"""
    print("\n🔧 测试集成系统组件...")
    
    try:
        from adspower_browser_use_integration import FixedAdsPowerBrowserUseIntegration
        print("✅ FixedAdsPowerBrowserUseIntegration 导入成功")
        
        # 测试实例化（使用虚拟profile_id）
        fixed_integration = FixedAdsPowerBrowserUseIntegration("test_profile")
        print("✅ FixedAdsPowerBrowserUseIntegration 实例化成功")
        
        # 验证关键方法存在
        methods_to_check = [
            'start_browser_session',
            'initialize_browser', 
            'create_intelligent_agent',
            'run_intelligent_questionnaire',
            'cleanup_resources'
        ]
        
        for method in methods_to_check:
            assert hasattr(fixed_integration, method), f"❌ 缺少{method}方法"
        print("✅ 所有核心方法验证成功")
        
        print("🎉 集成系统组件验证通过！")
        return True
        
    except Exception as e:
        print(f"❌ 集成系统测试失败: {e}")
        return False

async def test_compatibility_functions():
    """测试兼容性函数"""
    print("\n🔗 测试兼容性函数...")
    
    try:
        from adspower_browser_use_integration import run_complete_questionnaire_workflow_with_existing_browser
        print("✅ 兼容性函数导入成功")
        
        # 验证函数可调用性
        assert callable(run_complete_questionnaire_workflow_with_existing_browser), "❌ 兼容性函数不可调用"
        print("✅ 兼容性函数验证成功")
        
        print("🎉 兼容性函数验证通过！")
        return True
        
    except Exception as e:
        print(f"❌ 兼容性函数测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 核心集成修复验证测试开始")
    print("=" * 60)
    
    results = []
    
    # 测试核心集成
    result1 = await test_core_integration()
    results.append(("核心集成功能", result1))
    
    # 测试集成系统
    result2 = await test_integration_system()
    results.append(("集成系统组件", result2))
    
    # 测试兼容性函数
    result3 = await test_compatibility_functions()
    results.append(("兼容性函数", result3))
    
    # 总结报告
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！核心集成修复成功！")
        print("🚀 AdsPowerWebUIIntegration现在应该能够正常工作")
        print("📝 主程序现在应该能够开始实际的问卷答题流程")
    else:
        print("❌ 部分测试失败，需要进一步修复")
    
    return all_passed

if __name__ == "__main__":
    asyncio.run(main()) 