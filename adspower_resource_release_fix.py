#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AdsPower资源释放修复脚本

问题解决总结：
1. ✅ 已确认API调用方式正确
2. ✅ 已成功清理所有测试配置文件
3. ✅ AdsPower环境列表已清空，浏览器额度已释放

现在修复代码中的问题，确保以后能正确释放资源
"""

import asyncio
import logging
import aiohttp
import requests
from typing import Dict, Optional

logger = logging.getLogger(__name__)

class FixedAdsPowerResourceManager:
    """修复版的AdsPower资源管理器
    
    确保正确实现两步释放流程：
    1. 停止浏览器实例 (browser/stop)
    2. 删除配置文件 (user/delete) - 这是关键步骤
    """
    
    def __init__(self, adspower_host: str = "http://local.adspower.net:50325"):
        self.adspower_host = adspower_host
        self.logger = logger
    
    async def complete_release_resources(self, profile_id: str, persona_name: str = "未知") -> Dict:
        """
        完全释放AdsPower资源 - 修复版
        
        确保配置文件从AdsPower应用环境列表中完全移除
        """
        try:
            self.logger.info(f"🚀 开始释放AdsPower资源: {persona_name} ({profile_id})")
            
            # 第一步：停止浏览器实例
            stop_result = await self._stop_browser_instance(profile_id)
            
            # 第二步：删除配置文件（关键步骤）
            delete_result = await self._delete_browser_profile(profile_id)
            
            # 汇总结果
            success = delete_result["success"]  # 以删除结果为准
            
            result = {
                "success": success,
                "profile_id": profile_id,
                "persona_name": persona_name,
                "browser_stopped": stop_result["success"],
                "profile_deleted": delete_result["success"],
                "fully_released": delete_result["success"]
            }
            
            if success:
                self.logger.info("🎉 AdsPower资源释放成功!")
                self.logger.info("✅ 配置文件已从AdsPower应用环境列表中移除")
                self.logger.info("🔄 浏览器额度已释放")
            else:
                self.logger.warning("⚠️ AdsPower资源释放失败")
                self.logger.warning("⚠️ 配置文件可能仍在AdsPower应用列表中")
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ AdsPower资源释放异常: {e}")
            return {
                "success": False,
                "profile_id": profile_id,
                "persona_name": persona_name,
                "error": str(e)
            }
    
    async def _stop_browser_instance(self, profile_id: str) -> Dict:
        """停止AdsPower浏览器实例"""
        try:
            url = f"{self.adspower_host}/api/v1/browser/stop"
            params = {"user_id": profile_id}
            
            self.logger.info(f"⏹️ 停止浏览器实例: {profile_id}")
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        result = await response.json()
                        
                        # 注意：如果浏览器已经关闭，API会返回错误，这是正常的
                        if result.get("code") == 0:
                            self.logger.info("✅ 浏览器实例停止成功")
                            return {"success": True, "message": "浏览器停止成功"}
                        else:
                            msg = result.get("msg", "未知错误")
                            if "not open" in msg.lower():
                                self.logger.info("ℹ️ 浏览器已经关闭")
                                return {"success": True, "message": "浏览器已关闭"}
                            else:
                                self.logger.warning(f"⚠️ 浏览器停止失败: {msg}")
                                return {"success": False, "message": f"浏览器停止失败: {msg}"}
                    else:
                        return {"success": False, "message": f"API请求失败: {response.status}"}
                        
        except Exception as e:
            self.logger.error(f"❌ 停止浏览器异常: {e}")
            return {"success": False, "message": f"停止浏览器异常: {str(e)}"}
    
    async def _delete_browser_profile(self, profile_id: str) -> Dict:
        """删除AdsPower配置文件（从环境列表中移除）- 这是关键步骤"""
        try:
            url = f"{self.adspower_host}/api/v1/user/delete"
            data = {"user_ids": [profile_id]}
            
            self.logger.info(f"🗑️ 删除AdsPower配置文件: {profile_id}")
            self.logger.info("🎯 这将从AdsPower应用环境列表中完全移除配置文件")
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                async with session.post(url, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        
                        if result.get("code") == 0:
                            self.logger.info("✅ 配置文件删除成功")
                            self.logger.info("🎯 配置文件已从AdsPower应用列表中移除")
                            return {"success": True, "message": "配置文件删除成功"}
                        else:
                            msg = result.get("msg", "未知错误")
                            self.logger.warning(f"⚠️ 配置文件删除失败: {msg}")
                            return {"success": False, "message": f"配置文件删除失败: {msg}"}
                    else:
                        response_text = await response.text()
                        self.logger.error(f"❌ API请求失败: {response.status}")
                        self.logger.error(f"❌ 响应内容: {response_text}")
                        return {"success": False, "message": f"API请求失败: {response.status}"}
                        
        except Exception as e:
            self.logger.error(f"❌ 删除配置文件异常: {e}")
            return {"success": False, "message": f"删除配置文件异常: {str(e)}"}
    
    def get_sync_version(self):
        """返回同步版本的资源管理器"""
        return SyncAdsPowerResourceManager(self.adspower_host)

class SyncAdsPowerResourceManager:
    """同步版本的AdsPower资源管理器（用于非async环境）"""
    
    def __init__(self, adspower_host: str = "http://local.adspower.net:50325"):
        self.adspower_host = adspower_host
        self.logger = logger
    
    def complete_release_resources(self, profile_id: str, persona_name: str = "未知") -> Dict:
        """同步版本的完全释放资源"""
        try:
            self.logger.info(f"🚀 开始释放AdsPower资源: {persona_name} ({profile_id})")
            
            # 第一步：停止浏览器实例
            stop_result = self._stop_browser_instance_sync(profile_id)
            
            # 第二步：删除配置文件
            delete_result = self._delete_browser_profile_sync(profile_id)
            
            # 汇总结果
            success = delete_result["success"]
            
            result = {
                "success": success,
                "profile_id": profile_id,
                "persona_name": persona_name,
                "browser_stopped": stop_result["success"],
                "profile_deleted": delete_result["success"],
                "fully_released": delete_result["success"]
            }
            
            if success:
                self.logger.info("🎉 AdsPower资源释放成功!")
                self.logger.info("✅ 配置文件已从AdsPower应用环境列表中移除")
            else:
                self.logger.warning("⚠️ AdsPower资源释放失败")
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ AdsPower资源释放异常: {e}")
            return {"success": False, "error": str(e)}
    
    def _stop_browser_instance_sync(self, profile_id: str) -> Dict:
        """同步停止浏览器实例"""
        try:
            url = f"{self.adspower_host}/api/v1/browser/stop"
            params = {"user_id": profile_id}
            
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    return {"success": True, "message": "浏览器停止成功"}
                else:
                    msg = result.get("msg", "未知错误")
                    if "not open" in msg.lower():
                        return {"success": True, "message": "浏览器已关闭"}
                    else:
                        return {"success": False, "message": f"浏览器停止失败: {msg}"}
            else:
                return {"success": False, "message": f"API请求失败: {response.status_code}"}
                
        except Exception as e:
            return {"success": False, "message": f"停止浏览器异常: {str(e)}"}
    
    def _delete_browser_profile_sync(self, profile_id: str) -> Dict:
        """同步删除配置文件"""
        try:
            url = f"{self.adspower_host}/api/v1/user/delete"
            data = {"user_ids": [profile_id]}
            
            self.logger.info(f"🗑️ 删除AdsPower配置文件: {profile_id}")
            
            response = requests.post(url, json=data, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    self.logger.info("✅ 配置文件删除成功")
                    return {"success": True, "message": "配置文件删除成功"}
                else:
                    msg = result.get("msg", "未知错误")
                    return {"success": False, "message": f"配置文件删除失败: {msg}"}
            else:
                return {"success": False, "message": f"API请求失败: {response.status_code}"}
                
        except Exception as e:
            return {"success": False, "message": f"删除配置文件异常: {str(e)}"}

# 创建全局实例
fixed_async_manager = FixedAdsPowerResourceManager()
fixed_sync_manager = SyncAdsPowerResourceManager()

async def release_adspower_resources_async(profile_id: str, persona_name: str = "未知") -> Dict:
    """异步版本的资源释放函数"""
    return await fixed_async_manager.complete_release_resources(profile_id, persona_name)

def release_adspower_resources_sync(profile_id: str, persona_name: str = "未知") -> Dict:
    """同步版本的资源释放函数"""
    return fixed_sync_manager.complete_release_resources(profile_id, persona_name)

def patch_adspower_integration(integration_instance):
    """
    为现有的AdsPower集成实例应用修复补丁
    
    使用方法：
    patch_adspower_integration(your_integration_instance)
    """
    try:
        # 替换删除配置文件的方法
        async def fixed_delete_profile(self, profile_id: str) -> bool:
            result = await release_adspower_resources_async(profile_id, "修复释放")
            return result["success"]
        
        # 应用补丁
        integration_instance._delete_profile = fixed_delete_profile.__get__(integration_instance)
        logger.info("✅ AdsPower集成修复补丁已应用")
        
    except Exception as e:
        logger.error(f"❌ 应用修复补丁失败: {e}")

if __name__ == "__main__":
    # 测试修复功能
    import sys
    
    def test_sync():
        logging.basicConfig(level=logging.INFO)
        
        # 测试同步版本
        test_profile_id = "test_profile"
        result = release_adspower_resources_sync(test_profile_id, "测试数字人")
        print(f"同步测试结果: {result}")
    
    async def test_async():
        logging.basicConfig(level=logging.INFO)
        
        # 测试异步版本
        test_profile_id = "test_profile"
        result = await release_adspower_resources_async(test_profile_id, "测试数字人")
        print(f"异步测试结果: {result}")
    
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        print("运行测试...")
        test_sync()
        asyncio.run(test_async()) 