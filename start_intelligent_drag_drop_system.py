#!/usr/bin/env python3
"""
🎯 智能拖拽排序系统启动脚本
完整集成WebUI和拖拽排序引擎

功能特性：
1. 自动检测拖拽排序题型
2. 智能评估选项重要性
3. 模拟人类拖拽行为
4. 绕过反作弊机制
5. 完美融合WebUI智能特性

启动方式：
python start_intelligent_drag_drop_system.py

作者: AI Assistant
日期: 2024
"""

import asyncio
import sys
import os
import logging
from pathlib import Path
import json
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'drag_drop_system_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)

logger = logging.getLogger(__name__)

class IntelligentDragDropSystem:
    """🎯 智能拖拽排序系统"""
    
    def __init__(self):
        self.system_status = {
            'initialized': False,
            'webui_ready': False,
            'drag_drop_engine_ready': False,
            'browser_ready': False,
            'digital_human_loaded': False
        }
        
        self.digital_human_info = {
            'name': '李明',
            'age': '28',
            'location': '北京',
            'profession': '产品经理',
            'education': '硕士',
            'income': '20000',
            'gender': '男',
            'interests': ['科技', '阅读', '运动'],
            'values': ['工作稳定', '家庭和睦', '个人成长']
        }
        
        self.test_scenarios = []
    
    async def initialize_system(self):
        """初始化系统"""
        logger.info("🚀 开始初始化智能拖拽排序系统...")
        
        try:
            # Step 1: 初始化拖拽排序引擎
            await self._initialize_drag_drop_engine()
            
            # Step 2: 初始化WebUI控制器
            await self._initialize_webui_controller()
            
            # Step 3: 准备测试环境
            await self._prepare_test_environment()
            
            # Step 4: 系统自检
            await self._system_self_check()
            
            self.system_status['initialized'] = True
            logger.info("✅ 智能拖拽排序系统初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 系统初始化失败: {e}")
            raise
    
    async def _initialize_drag_drop_engine(self):
        """初始化拖拽排序引擎"""
        try:
            logger.info("🔧 初始化拖拽排序引擎...")
            
            from intelligent_drag_drop_ranking_engine import IntelligentDragDropRankingEngine
            self.drag_drop_engine = IntelligentDragDropRankingEngine()
            
            # 验证引擎功能
            assert hasattr(self.drag_drop_engine, 'handle_drag_drop_question'), "缺少主处理方法"
            assert hasattr(self.drag_drop_engine, 'ranking_strategies'), "缺少排序策略"
            assert hasattr(self.drag_drop_engine, 'drag_execution_strategies'), "缺少拖拽执行策略"
            
            self.system_status['drag_drop_engine_ready'] = True
            logger.info("✅ 拖拽排序引擎初始化成功")
            
        except Exception as e:
            logger.error(f"❌ 拖拽排序引擎初始化失败: {e}")
            raise
    
    async def _initialize_webui_controller(self):
        """初始化WebUI控制器"""
        try:
            logger.info("🔧 初始化WebUI控制器...")
            
            from src.controller.custom_controller import CustomController
            self.controller = CustomController()
            
            # 设置数字人信息
            self.controller.set_digital_human_info(self.digital_human_info)
            
            # 注册拖拽排序引擎
            self.controller.register_intelligent_drag_drop_ranking_engine()
            
            self.system_status['webui_ready'] = True
            self.system_status['digital_human_loaded'] = True
            logger.info("✅ WebUI控制器初始化成功")
            
        except Exception as e:
            logger.error(f"❌ WebUI控制器初始化失败: {e}")
            # 如果WebUI初始化失败，仍然可以测试引擎本身
            logger.warning("⚠️ 将使用独立模式测试拖拽排序引擎")
    
    async def _prepare_test_environment(self):
        """准备测试环境"""
        try:
            logger.info("🔧 准备测试环境...")
            
            # 定义测试场景
            self.test_scenarios = [
                {
                    'name': '重要性排序题',
                    'description': '按重要性对人生价值进行排序',
                    'question_text': '请按照对您人生的重要性程度排序',
                    'options': ['家庭和睦', '事业成功', '身体健康', '经济自由', '个人成长'],
                    'question_type': 'ranking_sort',
                    'ranking_context': 'importance'
                },
                {
                    'name': '偏好排序题',
                    'description': '按偏好对休闲活动进行排序',
                    'question_text': '请按照您的偏好程度排序以下休闲活动',
                    'options': ['阅读书籍', '运动健身', '看电影', '旅游', '社交聚会'],
                    'question_type': 'ranking_sort',
                    'ranking_context': 'preference'
                },
                {
                    'name': '穿梭框选择题',
                    'description': '选择适合的技能',
                    'question_text': '请选择您认为对产品经理最重要的技能',
                    'left_options': ['数据分析', '用户研究', '项目管理', '沟通协调', '产品设计', '市场洞察'],
                    'right_options': [],
                    'question_type': 'shuttle_transfer',
                    'ranking_context': 'professional_skills'
                },
                {
                    'name': '优先级排序题',
                    'description': '工作任务优先级排序',
                    'question_text': '请按照紧急程度排序以下工作任务',
                    'options': ['产品需求分析', '用户反馈处理', '项目进度跟踪', '团队会议', '数据报告'],
                    'question_type': 'priority_ordering',
                    'ranking_context': 'priority'
                }
            ]
            
            logger.info(f"✅ 准备了 {len(self.test_scenarios)} 个测试场景")
            
        except Exception as e:
            logger.error(f"❌ 测试环境准备失败: {e}")
            raise
    
    async def _system_self_check(self):
        """系统自检"""
        try:
            logger.info("🔍 执行系统自检...")
            
            # 检查拖拽排序引擎
            if self.system_status['drag_drop_engine_ready']:
                # 测试题型检测
                test_page_content = {
                    'draggable_elements': [{'text': '测试选项1'}, {'text': '测试选项2'}],
                    'drop_zones': [{'text': '目标区域'}],
                    'shuttle_components': [],
                    'sortable_lists': [],
                    'question_text': '测试排序题'
                }
                
                question_type = self.drag_drop_engine._determine_drag_drop_question_type(test_page_content)
                assert question_type != 'none', "题型检测失败"
                
                # 测试重要性评分
                score = await self.drag_drop_engine._calculate_importance_score(
                    '工作稳定', self.digital_human_info, 'importance', '测试问题'
                )
                assert 0 <= score <= 1, "重要性评分异常"
                
                logger.info("✅ 拖拽排序引擎自检通过")
            
            # 检查WebUI控制器
            if self.system_status['webui_ready']:
                # 验证数字人信息
                controller_info = self.controller._get_digital_human_info_safely()
                assert controller_info.get('name') == self.digital_human_info['name'], "数字人信息不匹配"
                
                logger.info("✅ WebUI控制器自检通过")
            
            logger.info("✅ 系统自检完成")
            
        except Exception as e:
            logger.error(f"❌ 系统自检失败: {e}")
            raise
    
    async def run_test_scenarios(self):
        """运行测试场景"""
        logger.info("🎯 开始运行测试场景...")
        
        results = []
        
        for i, scenario in enumerate(self.test_scenarios):
            try:
                logger.info(f"\n🔍 测试场景 {i+1}: {scenario['name']}")
                logger.info(f"📝 描述: {scenario['description']}")
                
                # 模拟页面内容
                page_content = self._create_mock_page_content(scenario)
                
                # 创建模拟页面对象
                mock_page = MockPage(page_content)
                
                # 执行拖拽排序处理
                result = await self.drag_drop_engine.handle_drag_drop_question(
                    mock_page, 
                    self.digital_human_info, 
                    scenario['ranking_context']
                )
                
                if result.get('success'):
                    logger.info(f"✅ 场景 {i+1} 处理成功: {result.get('method')}")
                    results.append({
                        'scenario': scenario['name'],
                        'status': 'SUCCESS',
                        'method': result.get('method'),
                        'details': result
                    })
                else:
                    logger.warning(f"⚠️ 场景 {i+1} 处理失败: {result.get('error')}")
                    results.append({
                        'scenario': scenario['name'],
                        'status': 'FAILED',
                        'error': result.get('error'),
                        'details': result
                    })
                
                # 添加延迟，模拟真实操作
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"❌ 场景 {i+1} 执行异常: {e}")
                results.append({
                    'scenario': scenario['name'],
                    'status': 'ERROR',
                    'error': str(e)
                })
        
        return results
    
    def _create_mock_page_content(self, scenario):
        """创建模拟页面内容"""
        if scenario['question_type'] == 'shuttle_transfer':
            return {
                'draggable_elements': [],
                'drop_zones': [],
                'shuttle_components': [{
                    'index': 0,
                    'leftList': {
                        'items': [{'text': option, 'selected': False} for option in scenario['left_options']]
                    },
                    'rightList': {
                        'items': [{'text': option} for option in scenario['right_options']]
                    },
                    'transferButtons': [
                        {'text': '→', 'direction': 'right'},
                        {'text': '←', 'direction': 'left'}
                    ]
                }],
                'sortable_lists': [],
                'question_text': scenario['question_text'],
                'page_structure': {
                    'has_sortable_js': False,
                    'has_jquery_ui': False,
                    'has_drag_events': True,
                    'total_interactive_elements': len(scenario['left_options']) + 2
                }
            }
        else:
            return {
                'draggable_elements': [
                    {'index': i, 'text': option, 'className': 'draggable-item'}
                    for i, option in enumerate(scenario['options'])
                ],
                'drop_zones': [
                    {'index': 0, 'text': '排序区域', 'className': 'drop-zone'}
                ],
                'shuttle_components': [],
                'sortable_lists': [{
                    'index': 0,
                    'items': [
                        {'index': i, 'text': option, 'className': 'sortable-item'}
                        for i, option in enumerate(scenario['options'])
                    ]
                }],
                'question_text': scenario['question_text'],
                'page_structure': {
                    'has_sortable_js': True,
                    'has_jquery_ui': True,
                    'has_drag_events': True,
                    'total_interactive_elements': len(scenario['options']) + 1
                }
            }
    
    def print_results_summary(self, results):
        """打印结果总结"""
        logger.info("\n" + "="*80)
        logger.info("🎯 智能拖拽排序系统测试结果总结")
        logger.info("="*80)
        
        success_count = sum(1 for r in results if r['status'] == 'SUCCESS')
        failed_count = sum(1 for r in results if r['status'] == 'FAILED')
        error_count = sum(1 for r in results if r['status'] == 'ERROR')
        
        logger.info(f"📊 测试统计:")
        logger.info(f"  ✅ 成功: {success_count}")
        logger.info(f"  ⚠️ 失败: {failed_count}")
        logger.info(f"  ❌ 错误: {error_count}")
        logger.info(f"  📝 总计: {len(results)}")
        
        logger.info(f"\n📋 详细结果:")
        for result in results:
            status_icon = "✅" if result['status'] == 'SUCCESS' else "⚠️" if result['status'] == 'FAILED' else "❌"
            logger.info(f"  {status_icon} {result['scenario']}: {result['status']}")
            
            if result['status'] == 'SUCCESS':
                logger.info(f"    🔧 处理方法: {result.get('method', 'N/A')}")
            elif 'error' in result:
                logger.info(f"    🔍 错误信息: {result['error']}")
        
        # 系统状态总结
        logger.info(f"\n🖥️ 系统状态:")
        for component, status in self.system_status.items():
            status_icon = "✅" if status else "❌"
            logger.info(f"  {status_icon} {component}: {'就绪' if status else '未就绪'}")
        
        # 数字人信息
        logger.info(f"\n👤 数字人信息:")
        logger.info(f"  姓名: {self.digital_human_info['name']}")
        logger.info(f"  年龄: {self.digital_human_info['age']}")
        logger.info(f"  职业: {self.digital_human_info['profession']}")
        logger.info(f"  地点: {self.digital_human_info['location']}")
        
        # 总体评估
        if error_count == 0 and failed_count == 0:
            logger.info(f"\n🎉 所有测试场景处理成功！智能拖拽排序系统运行完美")
        elif error_count == 0 and failed_count <= 1:
            logger.info(f"\n⚠️ 大部分测试场景成功，系统运行良好")
        else:
            logger.info(f"\n❌ 多个测试场景失败，需要检查系统配置")
        
        logger.info("="*80)
    
    async def start_interactive_mode(self):
        """启动交互模式"""
        logger.info("🎮 启动交互模式...")
        
        while True:
            try:
                print("\n" + "="*50)
                print("🎯 智能拖拽排序系统 - 交互模式")
                print("="*50)
                print("1. 运行所有测试场景")
                print("2. 运行单个测试场景")
                print("3. 查看系统状态")
                print("4. 更新数字人信息")
                print("5. 退出系统")
                print("="*50)
                
                choice = input("请选择操作 (1-5): ").strip()
                
                if choice == '1':
                    results = await self.run_test_scenarios()
                    self.print_results_summary(results)
                
                elif choice == '2':
                    print("\n可用测试场景:")
                    for i, scenario in enumerate(self.test_scenarios):
                        print(f"  {i+1}. {scenario['name']} - {scenario['description']}")
                    
                    try:
                        scenario_idx = int(input("请选择场景编号: ").strip()) - 1
                        if 0 <= scenario_idx < len(self.test_scenarios):
                            scenario = self.test_scenarios[scenario_idx]
                            page_content = self._create_mock_page_content(scenario)
                            mock_page = MockPage(page_content)
                            
                            result = await self.drag_drop_engine.handle_drag_drop_question(
                                mock_page, self.digital_human_info, scenario['ranking_context']
                            )
                            
                            print(f"\n结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
                        else:
                            print("❌ 无效的场景编号")
                    except ValueError:
                        print("❌ 请输入有效的数字")
                
                elif choice == '3':
                    print(f"\n🖥️ 系统状态:")
                    for component, status in self.system_status.items():
                        status_icon = "✅" if status else "❌"
                        print(f"  {status_icon} {component}: {'就绪' if status else '未就绪'}")
                
                elif choice == '4':
                    print(f"\n👤 当前数字人信息:")
                    for key, value in self.digital_human_info.items():
                        print(f"  {key}: {value}")
                    
                    key = input("\n请输入要修改的字段名 (回车跳过): ").strip()
                    if key and key in self.digital_human_info:
                        new_value = input(f"请输入新的 {key} 值: ").strip()
                        if new_value:
                            self.digital_human_info[key] = new_value
                            if hasattr(self, 'controller'):
                                self.controller.set_digital_human_info(self.digital_human_info)
                            print(f"✅ 已更新 {key} = {new_value}")
                
                elif choice == '5':
                    print("👋 感谢使用智能拖拽排序系统！")
                    break
                
                else:
                    print("❌ 无效选择，请重新输入")
                
            except KeyboardInterrupt:
                print("\n👋 用户中断，退出系统")
                break
            except Exception as e:
                print(f"❌ 操作失败: {e}")

class MockPage:
    """模拟页面对象"""
    
    def __init__(self, page_content):
        self.page_content = page_content
    
    async def evaluate(self, script, *args):
        """模拟页面脚本执行"""
        # 返回预设的页面内容
        return self.page_content

async def main():
    """主函数"""
    try:
        print("🚀 启动智能拖拽排序系统...")
        
        system = IntelligentDragDropSystem()
        
        # 初始化系统
        await system.initialize_system()
        
        # 运行测试场景
        results = await system.run_test_scenarios()
        
        # 打印结果总结
        system.print_results_summary(results)
        
        # 询问是否进入交互模式
        if input("\n是否进入交互模式？(y/n): ").strip().lower() == 'y':
            await system.start_interactive_mode()
        
    except KeyboardInterrupt:
        logger.info("🛑 系统被用户中断")
    except Exception as e:
        logger.error(f"❌ 系统运行失败: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main()) 