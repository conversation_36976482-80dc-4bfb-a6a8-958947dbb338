#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 系统架构集成测试脚本
验证WebUI CustomController与智能问卷系统的完美融合
"""

import asyncio
import logging
import sys
import os
from typing import Dict, Any
import traceback

# 设置路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('system_integration_test.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

async def test_system_architecture_integration():
    """🔍 测试系统架构集成完整性"""
    
    logger.info("🚀 开始系统架构集成测试...")
    
    test_results = {
        "total_tests": 0,
        "passed_tests": 0,
        "failed_tests": 0,
        "test_details": []
    }
    
    # 测试1: CustomController导入和创建
    logger.info("\n" + "="*60)
    logger.info("🧪 测试1: CustomController导入和创建")
    test_results["total_tests"] += 1
    
    try:
        from src.controller.custom_controller import CustomController
        
        # 创建CustomController
        controller = CustomController(exclude_actions=[])
        logger.info("✅ CustomController创建成功")
        
        # 测试数字人信息设置
        test_digital_human = {
            'name': '李小明',
            'age': 28,
            'gender': '男',
            'profession': '软件工程师',
            'location': '北京',
            'residence': '中国'
        }
        
        controller.set_digital_human_info(test_digital_human)
        logger.info("✅ 数字人信息设置成功")
        
        test_results["passed_tests"] += 1
        test_results["test_details"].append({
            "test": "CustomController创建和配置",
            "status": "PASS",
            "details": "成功创建并配置CustomController"
        })
        
    except Exception as e:
        logger.error(f"❌ CustomController测试失败: {e}")
        test_results["failed_tests"] += 1
        test_results["test_details"].append({
            "test": "CustomController创建和配置",
            "status": "FAIL",
            "error": str(e)
        })
    
    # 测试2: 智能引擎方法检查
    logger.info("\n" + "="*60)
    logger.info("🧪 测试2: 智能引擎方法检查")
    test_results["total_tests"] += 1
    
    try:
        required_methods = [
            'register_intelligent_nationality_region_engine',
            '_register_enhanced_intelligent_selection_system',
            '_is_country_selection_element',
            '_handle_country_selection_with_all_engines',
            '_determine_target_nationality',
            '_safe_fallback_click'
        ]
        
        missing_methods = []
        for method in required_methods:
            if not hasattr(controller, method):
                missing_methods.append(method)
        
        if missing_methods:
            raise Exception(f"缺少关键方法: {missing_methods}")
        
        logger.info("✅ 所有智能引擎方法都存在")
        
        # 测试智能引擎激活
        controller.register_intelligent_nationality_region_engine()
        logger.info("✅ 智能国籍引擎激活成功")
        
        test_results["passed_tests"] += 1
        test_results["test_details"].append({
            "test": "智能引擎方法检查",
            "status": "PASS",
            "details": f"所有{len(required_methods)}个关键方法都存在并可调用"
        })
        
    except Exception as e:
        logger.error(f"❌ 智能引擎方法测试失败: {e}")
        test_results["failed_tests"] += 1
        test_results["test_details"].append({
            "test": "智能引擎方法检查",
            "status": "FAIL",
            "error": str(e)
        })
    
    # 测试3: AdsPowerWebUIIntegration类检查
    logger.info("\n" + "="*60)
    logger.info("🧪 测试3: AdsPowerWebUIIntegration类检查")
    test_results["total_tests"] += 1
    
    try:
        from adspower_browser_use_integration import AdsPowerWebUIIntegration
        
        # 创建集成器
        integrator = AdsPowerWebUIIntegration()
        logger.info("✅ AdsPowerWebUIIntegration创建成功")
        
        # 检查关键方法
        required_integration_methods = [
            '_create_comprehensive_webui_task_prompt',
            '_force_activate_intelligent_engines',
            '_apply_dropdown_enhancement_patch',
            '_register_enhanced_click_handler'
        ]
        
        missing_integration_methods = []
        for method in required_integration_methods:
            if not hasattr(integrator, method):
                missing_integration_methods.append(method)
        
        if missing_integration_methods:
            raise Exception(f"AdsPowerWebUIIntegration缺少方法: {missing_integration_methods}")
        
        logger.info("✅ AdsPowerWebUIIntegration所有关键方法都存在")
        
        # 测试任务提示词创建
        task_prompt = integrator._create_comprehensive_webui_task_prompt(
            test_digital_human, 
            "https://example.com/questionnaire"
        )
        
        if len(task_prompt) > 100 and '李小明' in task_prompt:
            logger.info("✅ 任务提示词创建成功，包含数字人信息")
        else:
            raise Exception("任务提示词生成异常")
        
        test_results["passed_tests"] += 1
        test_results["test_details"].append({
            "test": "AdsPowerWebUIIntegration类检查",
            "status": "PASS",
            "details": f"所有{len(required_integration_methods)}个关键方法都存在"
        })
        
    except Exception as e:
        logger.error(f"❌ AdsPowerWebUIIntegration测试失败: {e}")
        test_results["failed_tests"] += 1
        test_results["test_details"].append({
            "test": "AdsPowerWebUIIntegration类检查",
            "status": "FAIL",
            "error": str(e)
        })
    
    # 测试4: BrowserUseAgent导入检查
    logger.info("\n" + "="*60)
    logger.info("🧪 测试4: BrowserUseAgent导入检查")
    test_results["total_tests"] += 1
    
    try:
        from src.agent.browser_use.browser_use_agent import BrowserUseAgent
        logger.info("✅ BrowserUseAgent导入成功")
        
        # 检查BrowserUseAgent是否有智能问卷相关属性
        test_agent_attrs = [
            'digital_human_info',
            'questionnaire_mode',
            'never_give_up_mode'
        ]
        
        # 注意：这里只是检查类定义，不实际创建Agent（需要浏览器环境）
        logger.info("✅ BrowserUseAgent类检查完成")
        
        test_results["passed_tests"] += 1
        test_results["test_details"].append({
            "test": "BrowserUseAgent导入检查",
            "status": "PASS",
            "details": "BrowserUseAgent成功导入"
        })
        
    except Exception as e:
        logger.error(f"❌ BrowserUseAgent测试失败: {e}")
        test_results["failed_tests"] += 1
        test_results["test_details"].append({
            "test": "BrowserUseAgent导入检查",
            "status": "FAIL",
            "error": str(e)
        })
    
    # 测试5: 智能引擎功能测试
    logger.info("\n" + "="*60)
    logger.info("🧪 测试5: 智能引擎功能测试")
    test_results["total_tests"] += 1
    
    try:
        # 测试国家选择检测
        test_country_elements = [
            "中国",
            "China",
            "选择您的国籍",
            "Select your country"
        ]
        
        detection_results = []
        for element_text in test_country_elements:
            # 模拟元素检测（实际需要页面环境）
            is_country = controller._is_country_selection_element({
                'text': element_text,
                'tag': 'select'
            })
            detection_results.append(is_country)
        
        logger.info(f"✅ 国家选择检测测试完成: {detection_results}")
        
        # 测试目标国籍确定
        target_nationality = controller._determine_target_nationality(test_digital_human)
        if target_nationality and '中国' in target_nationality:
            logger.info(f"✅ 目标国籍确定成功: {target_nationality}")
        else:
            raise Exception(f"目标国籍确定异常: {target_nationality}")
        
        test_results["passed_tests"] += 1
        test_results["test_details"].append({
            "test": "智能引擎功能测试",
            "status": "PASS",
            "details": f"国家选择检测和目标国籍确定功能正常"
        })
        
    except Exception as e:
        logger.error(f"❌ 智能引擎功能测试失败: {e}")
        test_results["failed_tests"] += 1
        test_results["test_details"].append({
            "test": "智能引擎功能测试",
            "status": "FAIL",
            "error": str(e)
        })
    
    # 生成测试报告
    logger.info("\n" + "="*60)
    logger.info("📊 系统架构集成测试报告")
    logger.info("="*60)
    
    success_rate = (test_results["passed_tests"] / test_results["total_tests"]) * 100
    
    logger.info(f"📈 总测试数: {test_results['total_tests']}")
    logger.info(f"✅ 通过测试: {test_results['passed_tests']}")
    logger.info(f"❌ 失败测试: {test_results['failed_tests']}")
    logger.info(f"🎯 成功率: {success_rate:.1f}%")
    
    logger.info("\n📋 详细测试结果:")
    for i, detail in enumerate(test_results["test_details"], 1):
        status_icon = "✅" if detail["status"] == "PASS" else "❌"
        logger.info(f"{i}. {status_icon} {detail['test']}: {detail['status']}")
        if detail["status"] == "FAIL":
            logger.info(f"   错误: {detail.get('error', '未知错误')}")
        else:
            logger.info(f"   详情: {detail.get('details', '测试通过')}")
    
    # 系统集成状态评估
    logger.info("\n" + "="*60)
    logger.info("🔍 系统集成状态评估")
    logger.info("="*60)
    
    if success_rate >= 100:
        logger.info("🎉 系统架构完美集成！所有组件都正常工作")
        integration_status = "PERFECT"
    elif success_rate >= 80:
        logger.info("✅ 系统架构基本集成成功，存在少量问题")
        integration_status = "GOOD"
    elif success_rate >= 60:
        logger.info("⚠️ 系统架构部分集成，需要修复一些问题")
        integration_status = "PARTIAL"
    else:
        logger.info("❌ 系统架构集成存在严重问题，需要重大修复")
        integration_status = "CRITICAL"
    
    # 给出具体建议
    if integration_status == "PERFECT":
        logger.info("\n🎯 建议:")
        logger.info("- 系统已完美集成，可以开始实际问卷测试")
        logger.info("- 建议创建端到端测试验证完整流程")
        logger.info("- 可以开始生产环境部署准备")
    elif integration_status == "GOOD":
        logger.info("\n🔧 建议:")
        logger.info("- 修复失败的测试项目")
        logger.info("- 进行更深入的集成测试")
        logger.info("- 验证边缘情况处理")
    else:
        logger.info("\n🚨 紧急建议:")
        logger.info("- 立即修复所有失败的测试")
        logger.info("- 重新检查系统架构设计")
        logger.info("- 确保所有依赖正确安装")
    
    return {
        "success_rate": success_rate,
        "integration_status": integration_status,
        "test_results": test_results
    }

async def main():
    """主函数"""
    try:
        logger.info("🚀 启动系统架构集成测试...")
        
        result = await test_system_architecture_integration()
        
        logger.info(f"\n🏁 测试完成！集成状态: {result['integration_status']}")
        logger.info(f"📊 成功率: {result['success_rate']:.1f}%")
        
        return result
        
    except Exception as e:
        logger.error(f"❌ 测试执行失败: {e}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        return {"success": False, "error": str(e)}

if __name__ == "__main__":
    # 运行测试
    result = asyncio.run(main())
    
    # 根据结果设置退出码
    if result.get("success_rate", 0) >= 80:
        sys.exit(0)  # 成功
    else:
        sys.exit(1)  # 失败 