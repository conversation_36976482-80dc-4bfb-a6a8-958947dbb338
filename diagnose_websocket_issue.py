#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔍 WebSocket连接问题诊断与修复工具
用于诊断和修复五层融合系统中的WebSocket连接问题
"""

import asyncio
import json
import logging
import requests
import time
import websockets
from typing import Dict, Any, Optional

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class WebSocketDiagnostic:
    """WebSocket连接诊断工具"""
    
    def __init__(self, adspower_host: str = "http://local.adspower.net:50325"):
        self.adspower_host = adspower_host
        
    async def diagnose_complete_flow(self, persona_id: int = 9999, persona_name: str = "DiagnosticTest"):
        """完整的WebSocket连接流程诊断"""
        logger.info("🔍 开始完整的WebSocket连接流程诊断")
        
        try:
            # 步骤1：创建AdsPower浏览器环境
            logger.info("📍 步骤1：创建AdsPower浏览器环境")
            
            from enhanced_adspower_lifecycle import AdsPowerLifecycleManager
            lifecycle_manager = AdsPowerLifecycleManager()
            
            # 检查服务状态
            if not await lifecycle_manager.check_service_status():
                logger.error("❌ AdsPower服务未运行")
                return False
            
            # 创建浏览器环境
            browser_env = await lifecycle_manager.create_complete_browser_environment(
                persona_id=persona_id,
                persona_name=persona_name
            )
            
            if not browser_env.get('success'):
                logger.error(f"❌ 浏览器环境创建失败: {browser_env.get('error')}")
                return False
            
            profile_id = browser_env['profile_id']
            debug_port = browser_env['debug_port']
            
            logger.info(f"✅ 浏览器环境创建成功")
            logger.info(f"   Profile ID: {profile_id}")
            logger.info(f"   Debug Port: {debug_port}")
            
            # 步骤2：测试Chrome DevTools Protocol
            logger.info("📍 步骤2：测试Chrome DevTools Protocol")
            
            if debug_port:
                # 测试HTTP端点
                await self._test_http_endpoints(debug_port)
                
                # 测试WebSocket连接
                await self._test_websocket_connections(debug_port)
                
                # 测试browser_use连接
                await self._test_browser_use_connection(debug_port)
            else:
                logger.error("❌ 未获取到debug_port")
            
            # 步骤3：清理资源
            logger.info("📍 步骤3：清理诊断资源")
            await lifecycle_manager.force_cleanup_browser(profile_id, "诊断完成清理")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 诊断过程异常: {e}")
            return False
    
    async def _test_http_endpoints(self, debug_port: str):
        """测试Chrome DevTools HTTP端点"""
        logger.info(f"🔍 测试Chrome DevTools HTTP端点: {debug_port}")
        
        base_url = f"http://127.0.0.1:{debug_port}"
        
        # 测试版本信息
        try:
            response = requests.get(f"{base_url}/json/version", timeout=5)
            if response.status_code == 200:
                version_info = response.json()
                logger.info(f"✅ 版本信息获取成功: {version_info.get('Browser', 'Unknown')}")
            else:
                logger.warning(f"⚠️ 版本信息请求失败: {response.status_code}")
        except Exception as e:
            logger.error(f"❌ 版本信息请求异常: {e}")
        
        # 测试页面列表
        try:
            response = requests.get(f"{base_url}/json", timeout=5)
            if response.status_code == 200:
                pages = response.json()
                logger.info(f"✅ 页面列表获取成功，共 {len(pages)} 个页面")
                
                for i, page in enumerate(pages[:3]):  # 只显示前3个
                    logger.info(f"   页面{i+1}: {page.get('title', 'No Title')[:50]}")
                    logger.info(f"           URL: {page.get('url', 'No URL')[:100]}")
                    logger.info(f"           WebSocket: {page.get('webSocketDebuggerUrl', 'None')}")
            else:
                logger.warning(f"⚠️ 页面列表请求失败: {response.status_code}")
        except Exception as e:
            logger.error(f"❌ 页面列表请求异常: {e}")
    
    async def _test_websocket_connections(self, debug_port: str):
        """测试WebSocket连接"""
        logger.info(f"🔍 测试WebSocket连接: {debug_port}")
        
        try:
            # 先获取页面列表找到可用的WebSocket URL
            response = requests.get(f"http://127.0.0.1:{debug_port}/json", timeout=5)
            if response.status_code != 200:
                logger.error("❌ 无法获取页面列表")
                return
            
            pages = response.json()
            if not pages:
                logger.error("❌ 没有可用页面")
                return
            
            # 选择第一个页面测试
            page = pages[0]
            ws_url = page.get('webSocketDebuggerUrl')
            
            if not ws_url:
                logger.error("❌ 页面没有WebSocket调试URL")
                return
            
            logger.info(f"🔗 测试WebSocket URL: {ws_url}")
            
            # 测试WebSocket连接
            try:
                async with websockets.connect(ws_url, timeout=10) as websocket:
                    logger.info("✅ WebSocket连接成功")
                    
                    # 发送简单的CDP命令测试
                    test_command = {
                        "id": 1,
                        "method": "Runtime.evaluate",
                        "params": {"expression": "1+1"}
                    }
                    
                    await websocket.send(json.dumps(test_command))
                    response = await asyncio.wait_for(websocket.recv(), timeout=5)
                    
                    response_data = json.loads(response)
                    if response_data.get('result', {}).get('result', {}).get('value') == 2:
                        logger.info("✅ WebSocket命令执行成功")
                    else:
                        logger.warning(f"⚠️ WebSocket命令响应异常: {response_data}")
                        
            except websockets.exceptions.ConnectionClosed as e:
                logger.error(f"❌ WebSocket连接被关闭: {e}")
            except asyncio.TimeoutError:
                logger.error("❌ WebSocket连接超时")
            except Exception as e:
                logger.error(f"❌ WebSocket连接异常: {e}")
                
        except Exception as e:
            logger.error(f"❌ WebSocket测试异常: {e}")
    
    async def _test_browser_use_connection(self, debug_port: str):
        """测试browser_use连接"""
        logger.info(f"🔍 测试browser_use连接: {debug_port}")
        
        try:
            from browser_use import Browser, BrowserConfig
            
            # 构造正确的WebSocket URL
            ws_endpoint = f"ws://127.0.0.1:{debug_port}"
            
            # 等待一段时间确保浏览器完全启动
            logger.info("⏳ 等待5秒确保浏览器完全启动...")
            await asyncio.sleep(5)
            
            # 再次检查页面列表
            try:
                response = requests.get(f"http://127.0.0.1:{debug_port}/json", timeout=5)
                if response.status_code == 200:
                    pages = response.json()
                    if pages:
                        # 使用第一个页面的具体WebSocket URL
                        ws_endpoint = pages[0].get('webSocketDebuggerUrl', ws_endpoint)
                        logger.info(f"🔗 使用具体的WebSocket URL: {ws_endpoint}")
                    else:
                        logger.warning("⚠️ 没有找到可用页面，使用通用WebSocket URL")
                else:
                    logger.warning(f"⚠️ 页面列表获取失败，使用通用WebSocket URL")
            except Exception as e:
                logger.warning(f"⚠️ 页面列表检查失败: {e}")
            
            # 创建browser_use配置
            browser_config = BrowserConfig(
                cdp_url=ws_endpoint,
                headless=False,
                disable_security=True
            )
            
            # 创建Browser实例
            browser = Browser(config=browser_config)
            
            logger.info("✅ browser_use Browser实例创建成功")
            
            # 测试基本操作
            try:
                # 这里可以添加一些基本的browser操作测试
                logger.info("🔍 测试browser_use基本操作...")
                
                # 关闭browser
                await browser.close()
                logger.info("✅ browser_use测试完成")
                
            except Exception as e:
                logger.error(f"❌ browser_use操作测试失败: {e}")
                try:
                    await browser.close()
                except:
                    pass
                
        except Exception as e:
            logger.error(f"❌ browser_use连接测试异常: {e}")

class WebSocketConnectionFixer:
    """WebSocket连接问题修复器"""
    
    def __init__(self):
        pass
    
    def create_enhanced_browser_config(self, ws_endpoint: str, retry_count: int = 3):
        """创建增强的browser配置，包含重试和错误处理"""
        try:
            from browser_use import BrowserConfig
            
            config = BrowserConfig(
                cdp_url=ws_endpoint,
                headless=False,
                disable_security=True,
                extra_browser_args=[
                    "--disable-blink-features=AutomationControlled",
                    "--disable-dev-shm-usage", 
                    "--no-sandbox",
                    "--disable-gpu",
                    "--disable-notifications",
                    "--disable-infobars"
                ]
            )
            
            return config
        except Exception as e:
            logger.error(f"❌ 创建browser配置失败: {e}")
            return None
    
    async def wait_for_websocket_ready(self, debug_port: str, max_wait_time: int = 30):
        """等待WebSocket端点准备就绪"""
        logger.info(f"⏳ 等待WebSocket端点准备就绪: {debug_port}")
        
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            try:
                # 检查HTTP端点
                response = requests.get(f"http://127.0.0.1:{debug_port}/json", timeout=3)
                if response.status_code == 200:
                    pages = response.json()
                    if pages and len(pages) > 0:
                        # 检查第一个页面的WebSocket URL
                        ws_url = pages[0].get('webSocketDebuggerUrl')
                        if ws_url:
                            logger.info(f"✅ WebSocket端点已准备就绪")
                            return ws_url
                
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.debug(f"等待WebSocket端点时的异常: {e}")
                await asyncio.sleep(1)
        
        logger.warning(f"⚠️ WebSocket端点在{max_wait_time}秒内未准备就绪")
        return None

async def main():
    """主函数"""
    print("🔍 WebSocket连接问题诊断工具")
    print("=" * 50)
    
    diagnostic = WebSocketDiagnostic()
    
    # 运行完整诊断
    success = await diagnostic.diagnose_complete_flow()
    
    if success:
        print("\n✅ 诊断完成，请查看日志了解详细信息")
    else:
        print("\n❌ 诊断过程中发现问题，请查看错误日志")

if __name__ == "__main__":
    asyncio.run(main())