"""
🎯 智能下拉框引擎 - 增强WebUI智能性
"""

import logging
from typing import Dict

logger = logging.getLogger(__name__)

class IntelligentDropdownEngine:
    def __init__(self):
        self.digital_human_info = {}
        
    def set_digital_human_info(self, digital_human_info: Dict):
        self.digital_human_info = digital_human_info
        
    def intelligent_option_matching(self, target_text: str) -> Dict:
        try:
            if "会计" in target_text or "财务" in target_text:
                return {
                    "type": "profession",
                    "matches": ["会计/财务", "财务会计", "会计师", "财务经理"],
                    "confidence": 0.95
                }
            
            if any(keyword in target_text for keyword in ["中国", "China", "国籍"]):
                return {
                    "type": "nationality", 
                    "matches": ["中国", "中国大陆", "中华人民共和国", "China"],
                    "confidence": 0.98
                }
            
            return {"type": "general", "matches": [target_text], "confidence": 0.7}
            
        except Exception as e:
            logger.error(f"智能选项匹配失败: {e}")
            return {"type": "fallback", "matches": [target_text], "confidence": 0.5}
