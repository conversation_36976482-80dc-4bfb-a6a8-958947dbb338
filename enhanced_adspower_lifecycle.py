#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
增强型AdsPower生命周期管理器
管理AdsPower浏览器的完整生命周期，包括创建、启动、关闭和资源清理
"""

import time
import logging
import requests
from enum import Enum
from typing import Dict, Optional, List, Any
from src.config.proxy_config import (
    get_proxy_config,
    validate_proxy_config
)
from qinguo_tunnel_proxy_manager import QinguoTunnelProxyManager
from qinguo_proxy_manager import QinguoProxyManager
import uuid

logger = logging.getLogger(__name__)

class BrowserStatus(Enum):
    """浏览器状态枚举"""
    READY = "ready"           # 准备就绪
    RUNNING = "running"       # 正在运行
    STOPPED = "stopped"       # 已停止
    ERROR = "error"          # 错误状态
    UNKNOWN = "unknown"      # 未知状态

    @classmethod
    def from_string(cls, status: str) -> 'BrowserStatus':
        """从字符串转换为状态枚举"""
        try:
            return cls(status.lower())
        except (ValueError, AttributeError):
            return cls.UNKNOWN

    def __str__(self) -> str:
        """转换为字符串"""
        return self.value

class EnhancedAdsPowerLifecycle:
    """增强型AdsPower生命周期管理器"""
    
    def __init__(self):
        """初始化AdsPower生命周期管理器"""
        # 从配置系统获取代理配置
        self.proxy_config = get_proxy_config()
        
        # 验证配置
        error = validate_proxy_config()
        if error:
            raise ValueError(f"代理配置错误: {error}")
        
        # 初始化代理管理器
        self.tunnel_proxy_manager = QinguoTunnelProxyManager()
        self.proxy_manager = QinguoProxyManager()
        
        # 浏览器管理
        self.active_browsers = {}  # 活跃的浏览器
        self.browser_proxies = {}  # 浏览器代理映射
        self.browser_profiles = {}  # 浏览器配置文件映射
        
        # AdsPower API基础URL
        self.api_base_url = "http://local.adspower.net:50325"
        
        # 日志记录器
        self.logger = logging.getLogger(__name__)
        
        logger.info("✅ AdsPower生命周期管理器初始化完成")
    
    async def check_service_status(self) -> bool:
        """检查AdsPower服务状态"""
        try:
            response = requests.get(f"{self.api_base_url}/status")
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0:
                    logger.info("✅ AdsPower服务正常运行")
                    return True
                else:
                    logger.warning(f"⚠️ AdsPower服务异常: {data.get('msg', '未知错误')}")
                    return False
            else:
                logger.error(f"❌ AdsPower服务请求失败: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"❌ AdsPower服务检查失败: {e}")
            return False
    
    async def get_existing_profiles(self) -> List[Dict]:
        """获取现有的配置文件列表"""
        try:
            response = requests.get(f"{self.api_base_url}/api/v1/user/list")
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0:
                    profiles = data.get("data", [])
                    logger.info(f"✅ 获取到 {len(profiles)} 个配置文件")
                    return profiles
                else:
                    logger.warning(f"⚠️ 获取配置文件失败: {data.get('msg', '未知错误')}")
                    return []
            else:
                logger.error(f"❌ 获取配置文件请求失败: {response.status_code}")
                return []
        except Exception as e:
            logger.error(f"❌ 获取配置文件异常: {e}")
            return []
    
    async def create_complete_browser_environment(self, profile_name: str = "", use_tunnel: bool = True) -> Dict:
        """创建完整的浏览器环境"""
        try:
            # 生成默认的配置文件名
            if not profile_name:
                profile_name = f"browser_{int(time.time())}_{uuid.uuid4().hex[:8]}"
            
            # 创建配置文件
            profile_config = self.create_browser_profile(profile_name, use_tunnel)
            if not profile_config.get("name"):
                raise Exception("创建浏览器配置文件失败")
            
            # 启动浏览器
            browser_config = self.start_browser(profile_name)
            if not browser_config.get("profile_name"):
                raise Exception("启动浏览器失败")
            
            return {
                "status": "success",
                "profile_config": profile_config,
                "browser_config": browser_config
            }
            
        except Exception as e:
            logger.error(f"创建浏览器环境失败: {e}")
            return {
                "status": "failed",
                "error": str(e),
                "profile_config": {
                    "name": profile_name,
                    "group": "auto_created",
                    "proxy": {
                        "proxy_soft": "no_proxy",
                        "proxy_type": "noproxy"
                    },
                    "fingerprint": {},
                    "browser": {}
                },
                "browser_config": {
                    "profile_name": profile_name,
                    "launch_args": []
                }
            }
    
    def delete_browser_profile(self, profile_name: str) -> bool:
        """删除浏览器配置文件"""
        try:
            # 检查浏览器状态
            if profile_name in self.active_browsers:
                logger.warning(f"⚠️ 浏览器 {profile_name} 仍在运行，先关闭浏览器")
                self.close_browser(profile_name)
            
            # 删除配置文件
            response = requests.post(
                f"{self.api_base_url}/api/v1/user/delete",
                json={"name": profile_name}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0:
                    logger.info(f"✅ 删除配置文件成功: {profile_name}")
                    return True
                else:
                    logger.warning(f"⚠️ 删除配置文件失败: {data.get('msg', '未知错误')}")
                    return False
            else:
                logger.error(f"❌ 删除配置文件请求失败: {response.status_code}")
                return False
            
        except Exception as e:
            logger.error(f"❌ 删除配置文件异常: {e}")
            return False
    
    def create_browser_profile(self, profile_name: str, use_tunnel: bool = True) -> Dict:
        """创建浏览器配置文件"""
        try:
            # 🔥 修复：尝试分配代理，失败时使用无代理模式
            proxy = None
            proxy_config = None
            proxy_type = "none"

            try:
                if use_tunnel:
                    proxy = self.tunnel_proxy_manager.allocate_tunnel_proxy_for_browser(profile_name)
                    if proxy:
                        proxy_config = self.tunnel_proxy_manager.generate_adspower_proxy_config(proxy)
                        proxy_type = "tunnel"
                else:
                    proxy = self.proxy_manager.allocate_proxy_for_browser(profile_name)
                    if proxy:
                        proxy_config = self.proxy_manager.generate_adspower_proxy_config(proxy)
                        proxy_type = "normal"

                if proxy:
                    logger.info(f"✅ 成功分配{proxy_type}代理: {profile_name}")
                else:
                    logger.warning(f"⚠️ 代理分配失败，使用无代理模式: {profile_name}")

            except Exception as proxy_error:
                logger.warning(f"⚠️ 代理分配异常，使用无代理模式: {proxy_error}")
                proxy = None
                proxy_config = None
                proxy_type = "none"

            # 🔥 关键修复：无论是否有代理都创建浏览器配置文件
            if proxy_config is None:
                # 无代理配置
                proxy_config = {
                    "proxy_soft": "no_proxy",
                    "proxy_type": "noproxy"
                }
                logger.info(f"🔄 使用无代理配置创建浏览器: {profile_name}")

            # 保存代理信息（包括无代理情况）
            self.browser_proxies[profile_name] = {
                "proxy": proxy,
                "type": proxy_type  # 使用实际的代理类型
            }
            
            # 创建配置文件
            profile_config = {
                "name": profile_name,
                "group": "auto_created",
                "proxy": proxy_config,
                "fingerprint": {
                    "webrtc": {
                        "mode": "disabled",  # 修复：使用有效的webrtc模式
                        "enabled": True
                    },
                    "canvas": {
                        "mode": "noise",
                        "enabled": True
                    },
                    "webgl": {
                        "mode": "noise",
                        "enabled": True
                    },
                    "clientRects": {
                        "mode": "noise",
                        "enabled": True
                    }
                },
                "browser": {
                    "language": ["en-US", "en"],
                    "timezone": {
                        "mode": "protect"
                    },
                    "resolution": "1920x1080",
                    "platform": "mac"
                }
            }
            
            # 🔥 关键修复：实际调用AdsPower API创建用户配置文件
            logger.info(f"🚀 调用AdsPower API创建用户配置文件: {profile_name}")

            create_url = f"{self.api_base_url}/api/v1/user/create"
            # 🔥 关键修复：添加强制桌面版指纹配置
            create_data = {
                "name": profile_name,
                "group_id": "0",  # 默认分组
                "user_proxy_config": proxy_config,
                # 🔥 强制桌面版配置
                "fingerprint_config": {
                    "ua": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                    "screen_resolution": "1280_1100",  # 强制桌面分辨率
                    "automatic_timezone": "1",  # 基于IP自动生成时区
                    "webrtc": "disabled",  # 禁用WebRTC
                    "language": ["en-US", "en", "zh-CN", "zh"],  # 多语言支持
                    "canvas": "1",  # 添加canvas噪音
                    "webgl": "3",  # 随机匹配webgl
                    "audio": "1",  # 添加音频噪音
                    "hardware_concurrency": "8",  # 8核CPU
                    "device_memory": "8",  # 8GB内存
                    "random_ua": {
                        "ua_browser": ["chrome"],
                        "ua_system_version": ["Mac OS X 10"]  # 强制Mac桌面系统
                    }
                }
            }

            try:
                response = requests.post(create_url, json=create_data, timeout=30)

                if response.status_code == 200:
                    result = response.json()
                    if result.get("code") == 0:
                        user_data = result.get("data", {})
                        user_id = user_data.get("id") or profile_name
                        logger.info(f"✅ AdsPower用户配置文件创建成功: {profile_name} (ID: {user_id})")

                        # 更新配置文件信息
                        profile_config["user_id"] = user_id
                        profile_config["created"] = True

                        # 🔥 保存配置文件信息
                        self.browser_profiles[profile_name] = profile_config

                        return profile_config
                    else:
                        logger.warning(f"⚠️ AdsPower用户创建响应: {result}")
                else:
                    logger.warning(f"⚠️ AdsPower用户创建请求失败: {response.status_code}")

            except Exception as api_error:
                logger.warning(f"⚠️ AdsPower API创建失败: {api_error}")

            # 如果API调用失败，返回本地配置
            logger.info(f"🔄 使用本地配置文件: {profile_name}")
            profile_config["user_id"] = profile_name
            profile_config["created"] = False

            # 🔥 保存配置文件信息
            self.browser_profiles[profile_name] = profile_config

            return profile_config
            
        except Exception as e:
            logger.error(f"创建浏览器配置文件失败: {e}")
            return {
                "name": profile_name,
                "group": "auto_created",
                "proxy": {
                    "proxy_soft": "no_proxy",
                    "proxy_type": "noproxy"
                },
                "fingerprint": {},
                "browser": {}
            }
    
    def start_browser(self, profile_name: str) -> Dict:
        """启动浏览器"""
        try:
            # 检查代理状态
            proxy_info = self.browser_proxies.get(profile_name)
            if not proxy_info:
                raise Exception("未找到浏览器代理配置")

            # 🔥 修复：测试代理连接（跳过无代理模式）
            if proxy_info["type"] == "tunnel" and proxy_info["proxy"]:
                if not self.tunnel_proxy_manager.test_tunnel_proxy(proxy_info["proxy"]):
                    logger.warning(f"⚠️ 隧道代理测试失败，继续启动浏览器: {profile_name}")
            elif proxy_info["type"] == "normal" and proxy_info["proxy"]:
                if not self.proxy_manager.test_proxy(proxy_info["proxy"]):
                    logger.warning(f"⚠️ 代理测试失败，继续启动浏览器: {profile_name}")
            elif proxy_info["type"] == "none":
                logger.info(f"🔄 无代理模式，直接启动浏览器: {profile_name}")
            else:
                logger.warning(f"⚠️ 未知代理类型，继续启动浏览器: {profile_name}")

            # 🔥 关键修复：实际调用AdsPower API启动浏览器窗口
            logger.info(f"🚀 调用AdsPower API启动浏览器: {profile_name}")

            # 🔥 关键修复：使用正确的AdsPower API端点
            start_url = f"{self.api_base_url}/api/v1/browser/start"

            # 🔥 关键修复：获取实际的用户ID
            browser_profile = self.browser_profiles.get(profile_name, {})
            actual_user_id = browser_profile.get("user_id", profile_name)

            # 🔥 修复：使用GET请求的查询参数而不是POST的JSON数据
            start_params = {
                "user_id": actual_user_id,  # 使用实际的用户ID
                "open_tabs": 1,  # 🔥 关键修复：1=关闭额外标签页，0=打开额外标签页（与直觉相反）
                "ip_tab": 0,     # 不打开IP检测页
                "headless": 0    # 不使用headless模式，显示浏览器窗口
            }

            # 🔥 修复：使用GET请求而不是POST
            response = requests.get(start_url, params=start_params, timeout=30)

            if response.status_code != 200:
                raise Exception(f"AdsPower API请求失败: {response.status_code}")

            result = response.json()

            if result.get("code") != 0:
                raise Exception(f"AdsPower启动失败: {result.get('msg', '未知错误')}")

            # 提取浏览器信息
            browser_data = result.get("data", {})
            ws_info = browser_data.get("ws", {})

            # 🔥 关键修复：正确提取调试端口
            debug_port = None

            # 优先从selenium字段获取端口
            if ws_info.get("selenium"):
                selenium_url = ws_info.get("selenium")
                # 从selenium URL中提取端口号：127.0.0.1:58148
                if ":" in selenium_url:
                    debug_port = selenium_url.split(":")[-1]

            # 备选：从puppeteer字段获取端口
            elif ws_info.get("puppeteer"):
                puppeteer_url = ws_info.get("puppeteer")
                if ":" in puppeteer_url:
                    debug_port = puppeteer_url.split(":")[-1]

            # 最后：从debug_port字段获取
            elif browser_data.get("debug_port"):
                raw_debug_port = browser_data.get("debug_port")
                # 如果已经包含IP地址，只取端口号
                if ":" in str(raw_debug_port):
                    debug_port = str(raw_debug_port).split(":")[-1]
                else:
                    debug_port = str(raw_debug_port)

            if not debug_port:
                raise Exception("无法获取浏览器调试端口")

            # 构建浏览器配置
            browser_config = {
                "profile_name": profile_name,
                "profile_id": profile_name,
                "debug_port": debug_port,
                "ws_info": ws_info,
                "browser_data": browser_data,
                "launch_args": [],  # AdsPower自动处理启动参数
                "proxy_info": proxy_info["proxy"],
                "proxy_type": proxy_info["type"]
            }

            self.active_browsers[profile_name] = {
                "started_at": time.time(),
                "proxy_info": proxy_info,
                "debug_port": debug_port,
                "browser_data": browser_data
            }

            logger.info(f"✅ AdsPower浏览器启动成功: {profile_name}")
            logger.info(f"   调试端口: {debug_port}")
            logger.info(f"   WebSocket信息: {ws_info}")

            return browser_config

        except Exception as e:
            logger.error(f"❌ 启动浏览器失败: {e}")
            return {
                "profile_name": profile_name,
                "profile_id": profile_name,
                "debug_port": None,
                "launch_args": [],
                "proxy_info": {},
                "proxy_type": "none",
                "error": str(e)
            }
    
    def close_browser(self, profile_name: str) -> bool:
        """关闭浏览器"""
        try:
            # 检查浏览器状态
            if profile_name not in self.active_browsers:
                logger.warning(f"浏览器未启动: {profile_name}")
                return True

            # 🔥 关键修复：调用AdsPower API关闭浏览器窗口
            logger.info(f"🔒 调用AdsPower API关闭浏览器: {profile_name}")

            close_url = f"{self.api_base_url}/api/v1/browser/stop"  # 修复：正确的API端点

            # 🔥 关键修复：获取实际的用户ID
            browser_profile = self.browser_profiles.get(profile_name, {})
            actual_user_id = browser_profile.get("user_id", profile_name)

            close_params = {"user_id": actual_user_id}

            try:
                response = requests.get(close_url, params=close_params, timeout=10)

                if response.status_code == 200:
                    result = response.json()
                    if result.get("code") == 0:
                        logger.info(f"✅ AdsPower浏览器已关闭: {profile_name}")
                    else:
                        logger.warning(f"⚠️ AdsPower关闭响应: {result}")
                else:
                    logger.warning(f"⚠️ AdsPower关闭请求失败: {response.status_code}")

            except Exception as api_error:
                logger.warning(f"⚠️ AdsPower API关闭失败: {api_error}")

            # 🔥 修复：释放代理（跳过无代理模式）
            proxy_info = self.browser_proxies.get(profile_name)
            if proxy_info and proxy_info["proxy"]:
                if proxy_info["type"] == "tunnel":
                    self.tunnel_proxy_manager.release_proxy(profile_name)
                elif proxy_info["type"] == "normal":
                    self.proxy_manager.release_proxy(profile_name)
                else:
                    logger.info(f"🔄 无代理模式，无需释放代理: {profile_name}")

            # 清理资源
            self.active_browsers.pop(profile_name, None)
            self.browser_proxies.pop(profile_name, None)

            logger.info(f"✅ 浏览器资源清理完成: {profile_name}")
            return True

        except Exception as e:
            logger.error(f"❌ 关闭浏览器失败: {e}")
            return False
    
    def get_browser_status(self, profile_name: str = "") -> Dict:
        """获取浏览器状态"""
        try:
            if profile_name:
                # 获取指定浏览器状态
                browser = self.active_browsers.get(profile_name)
                if not browser:
                    return {
                        "status": "not_found",
                        "error": "浏览器未启动",
                        "profile_name": profile_name,
                        "started_at": "",
                        "proxy_type": "",
                        "proxy_info": {}
                    }
                
                proxy_info = self.browser_proxies.get(profile_name)
                return {
                    "status": "running",
                    "profile_name": profile_name,
                    "started_at": time.strftime("%H:%M:%S", time.localtime(browser["started_at"])),
                    "proxy_type": proxy_info["type"] if proxy_info else "",
                    "proxy_info": proxy_info["proxy"] if proxy_info else {}
                }
            else:
                # 获取所有浏览器状态
                return {
                    "status": "success",
                    "active_count": len(self.active_browsers),
                    "browsers": [
                        {
                            "profile_name": name,
                            "started_at": time.strftime("%H:%M:%S", time.localtime(browser["started_at"])),
                            "proxy_type": self.browser_proxies[name]["type"],
                            "proxy_info": self.browser_proxies[name]["proxy"]
                        }
                        for name, browser in self.active_browsers.items()
                    ]
                }
            
        except Exception as e:
            logger.error(f"获取浏览器状态失败: {e}")
            return {
                "status": "error",
                "error": str(e),
                "profile_name": profile_name,
                "started_at": "",
                "proxy_type": "",
                "proxy_info": {}
            }
    
    async def cleanup_all_browsers(self):
        """清理所有浏览器资源"""
        logger.info("清理所有浏览器资源...")
        
        try:
            # 关闭所有浏览器
            for profile_name in list(self.active_browsers.keys()):
                self.close_browser(profile_name)
            
            # 清理代理资源
            await self.tunnel_proxy_manager.cleanup_all_proxies()
            await self.proxy_manager.cleanup_all_proxies()
            
            # 清理内部状态
            self.active_browsers.clear()
            self.browser_proxies.clear()
            
            logger.info("✅ 所有资源清理完成")
            
        except Exception as e:
            logger.error(f"❌ 资源清理失败: {e}")

# 测试函数
async def test_enhanced_adspower_lifecycle():
    """测试增强型AdsPower生命周期管理功能"""
    print("🧪 测试增强型AdsPower生命周期管理功能...")
    
    manager = EnhancedAdsPowerLifecycle()
    
    try:
        # 1. 创建浏览器配置文件
        print("\n1️⃣ 创建浏览器配置文件")
        
        # 使用隧道代理
        profile1 = manager.create_browser_profile("测试浏览器1", use_tunnel=True)
        print("✅ 创建隧道代理浏览器配置:")
        print(f"   名称: {profile1.get('name', '')}")
        print(f"   代理: {profile1.get('proxy', {})}")
        
        # 使用普通代理
        profile2 = manager.create_browser_profile("测试浏览器2", use_tunnel=False)
        print("\n✅ 创建普通代理浏览器配置:")
        print(f"   名称: {profile2.get('name', '')}")
        print(f"   代理: {profile2.get('proxy', {})}")
        
        # 2. 启动浏览器
        print("\n2️⃣ 启动浏览器")
        
        browser1 = manager.start_browser("测试浏览器1")
        print("✅ 启动隧道代理浏览器:")
        print(f"   配置: {browser1}")
        
        browser2 = manager.start_browser("测试浏览器2")
        print("\n✅ 启动普通代理浏览器:")
        print(f"   配置: {browser2}")
        
        # 3. 获取浏览器状态
        print("\n3️⃣ 获取浏览器状态")
        
        status1 = manager.get_browser_status("测试浏览器1")
        print("✅ 隧道代理浏览器状态:")
        print(f"   {status1}")
        
        status2 = manager.get_browser_status("测试浏览器2")
        print("\n✅ 普通代理浏览器状态:")
        print(f"   {status2}")
        
        # 4. 关闭浏览器
        print("\n4️⃣ 关闭浏览器")
        
        manager.close_browser("测试浏览器1")
        print("✅ 关闭隧道代理浏览器")
        
        manager.close_browser("测试浏览器2")
        print("✅ 关闭普通代理浏览器")
        
        # 5. 清理所有资源
        print("\n5️⃣ 清理所有资源")
        await manager.cleanup_all_browsers()
        print("✅ 资源清理完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

# 为了向后兼容，添加类名别名
AdsPowerLifecycleManager = EnhancedAdsPowerLifecycle

if __name__ == "__main__":
    import asyncio
    asyncio.run(test_enhanced_adspower_lifecycle()) 