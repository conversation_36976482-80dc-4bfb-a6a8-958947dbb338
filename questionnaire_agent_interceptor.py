
# 🚨 Agent行为拦截器 - 确保问卷过程中永不回退
import logging
logger = logging.getLogger(__name__)

class QuestionnaireAgentInterceptor:
    """问卷Agent行为拦截器"""
    
    @staticmethod
    def intercept_backtrack_actions(action_type, action_params):
        """拦截回退动作"""
        if action_type == 'go_to_url':
            url = action_params.get('url', '')
            if any(keyword in url.lower() for keyword in ['survey', 'questionnaire', 'wenjuan', '问卷']):
                logger.warning(f'🚨 拦截问卷回退操作: {url}')
                return False  # 禁止执行
        return True  # 允许执行
    
    @staticmethod 
    def enhance_dropdown_failure_handling():
        """增强下拉框失败处理"""
        return {
            'strategy': 'never_go_back',
            'alternatives': [
                'try_different_selectors',
                'wait_and_retry', 
                'skip_current_question',
                'continue_with_next_element'
            ]
        }

# 全局拦截器实例
QUESTIONNAIRE_INTERCEPTOR = QuestionnaireAgentInterceptor()
