#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔥 五层融合智能问卷系统 - 启动脚本
快速启动和配置五层融合架构
"""

import asyncio
import json
import logging
import sys
from typing import Dict, Any

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('five_layer_system.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

def get_default_config() -> Dict[str, Any]:
    """🎯 获取默认配置"""
    return {
        'profile_id': 'j9bljb2',  # 请替换为您的AdsPower配置文件ID
        'questionnaire_url': 'https://www.wjx.cn/vm/w4e8hc9.aspx',  # 使用真实问卷URL
        'digital_human_info': {
            'name': '王小明',
            'age': 28,
            'gender': '男',
            'profession': '软件工程师',
            'income': '月收入12000元',
            'location': '北京市海淀区',
            'education': '本科',
            'marital_status': '未婚',
            'interests': ['科技', '阅读', '运动', '旅游'],
            'lifestyle': '健康积极',
            'consumption_habits': '理性消费，注重性价比',
            'work_experience': '5年',
            'language': '中文',
            'nationality': '中国'
        },
        'llm_config': {
            'model': 'gpt-4',
            'api_key': 'sk-your-api-key',  # 请替换为您的API密钥
            'base_url': 'https://api.openai.com/v1',
            'temperature': 0.7,
            'max_tokens': 4000
        },
        'execution_config': {
            'max_execution_time': 3600,  # 1小时
            'max_steps': 500,
            'enable_five_layer_fusion': True,
            'enable_never_give_up': True,
            'enable_answer_consistency': True,
            'enable_resource_management': True,
            'enable_intelligent_stop': True
        },
        'task_description': """
        🎯 五层融合智能问卷任务：
        
        请完成这个在线问卷调查，严格按照五层融合架构执行：
        
        1. 🔥 智能停止决策：只有在页面明确显示"感谢参与"、"问卷完成"等提示时才停止
        2. 🧠 答题一致性：相同问题必须给出相同答案，保持逻辑一致性
        3. 🔧 资源管理：实时监控浏览器状态，优雅处理异常
        4. 🚀 永不放弃：克服所有技术困难，持续执行直到真正完成
        5. 🧠 深度集成：根据页面场景智能调整策略
        
        核心要求：
        - 根据数字人信息进行一致性答题
        - 处理所有页面的所有问题
        - 等待页面跳转并继续答题
        - 绝不因技术问题而过早停止
        - 确保答案符合数字人身份特征
        """
    }

def load_config_from_file(config_file: str = 'five_layer_config.json') -> Dict[str, Any]:
    """📄 从文件加载配置"""
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        logger.info(f"✅ 从文件加载配置: {config_file}")
        return config
    except FileNotFoundError:
        logger.info(f"📄 配置文件不存在，使用默认配置: {config_file}")
        return get_default_config()
    except Exception as e:
        logger.error(f"❌ 加载配置文件失败: {e}")
        return get_default_config()

def save_config_to_file(config: Dict[str, Any], config_file: str = 'five_layer_config.json'):
    """💾 保存配置到文件"""
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        logger.info(f"✅ 配置已保存到文件: {config_file}")
    except Exception as e:
        logger.error(f"❌ 保存配置文件失败: {e}")

def validate_config(config: Dict[str, Any]) -> bool:
    """✅ 验证配置完整性"""
    required_keys = [
        'profile_id', 'questionnaire_url', 'digital_human_info', 
        'llm_config', 'execution_config', 'task_description'
    ]
    
    for key in required_keys:
        if key not in config:
            logger.error(f"❌ 缺少必需配置项: {key}")
            return False
    
    # 验证数字人信息
    digital_human_required = ['name', 'age', 'gender', 'profession']
    for key in digital_human_required:
        if key not in config['digital_human_info']:
            logger.error(f"❌ 缺少数字人信息: {key}")
            return False
    
    # 验证LLM配置
    llm_required = ['model', 'api_key']
    for key in llm_required:
        if key not in config['llm_config']:
            logger.error(f"❌ 缺少LLM配置: {key}")
            return False
    
    logger.info("✅ 配置验证通过")
    return True

async def run_five_layer_fusion_questionnaire(config: Dict[str, Any]) -> Dict[str, Any]:
    """🔥 运行五层融合智能问卷系统"""
    
    logger.info("🔥 启动五层融合智能问卷系统")
    logger.info(f"👤 数字人: {config['digital_human_info']['name']}")
    logger.info(f"🎯 目标URL: {config['questionnaire_url']}")
    logger.info(f"⏰ 最大执行时间: {config['execution_config']['max_execution_time']}秒")
    
    try:
        # 导入五层融合集成系统
        from adspower_browser_use_integration import run_complete_questionnaire_workflow
        
        # 直接调用完整的问卷工作流
        result = await run_complete_questionnaire_workflow(
            persona_id=1,  # 使用默认ID
            persona_name=config['digital_human_info']['name'],
            digital_human_info=config['digital_human_info'],
            questionnaire_url=config['questionnaire_url'],
            prompt=config['task_description']
        )
        
        return result
        
    except ImportError as e:
        logger.error(f"❌ 导入五层融合系统失败: {e}")
        return {'success': False, 'error': f'导入失败: {e}'}
    
    except Exception as e:
        logger.error(f"❌ 五层融合系统执行异常: {e}")
        return {'success': False, 'error': f'执行异常: {e}'}

def display_execution_result(result: Dict[str, Any]):
    """📊 显示执行结果"""
    
    logger.info("📊 五层融合系统执行结果:")
    logger.info("=" * 50)
    
    if result.get('success'):
        logger.info("✅ 执行状态: 成功")
        logger.info(f"⏰ 执行时间: {result.get('execution_time', 0):.1f}秒")
        logger.info(f"📝 答题数量: {result.get('questions_answered', 0)}")
        logger.info(f"📄 页面数量: {result.get('pages_navigated', 0)}")
        logger.info(f"🎯 完成原因: {result.get('completion_reason', '未知')}")
        
        # 显示五层架构状态
        if result.get('five_layer_status'):
            logger.info("🔥 五层架构执行状态:")
            for layer, status in result['five_layer_status'].items():
                logger.info(f"  {layer}: {status}")
    else:
        logger.error("❌ 执行状态: 失败")
        logger.error(f"❌ 错误信息: {result.get('error', '未知错误')}")
    
    logger.info("=" * 50)

def interactive_config_setup() -> Dict[str, Any]:
    """🎛️ 交互式配置设置"""
    
    print("🎛️ 五层融合智能问卷系统 - 交互式配置")
    print("=" * 50)
    
    config = get_default_config()
    
    # 基本配置
    print("📋 基本配置:")
    profile_id = input(f"AdsPower配置文件ID [{config['profile_id']}]: ").strip()
    if profile_id:
        config['profile_id'] = profile_id
    
    questionnaire_url = input(f"问卷URL [{config['questionnaire_url']}]: ").strip()
    if questionnaire_url:
        config['questionnaire_url'] = questionnaire_url
    
    # 数字人配置
    print("\n👤 数字人配置:")
    name = input(f"姓名 [{config['digital_human_info']['name']}]: ").strip()
    if name:
        config['digital_human_info']['name'] = name
    
    age = input(f"年龄 [{config['digital_human_info']['age']}]: ").strip()
    if age and age.isdigit():
        config['digital_human_info']['age'] = int(age)
    
    profession = input(f"职业 [{config['digital_human_info']['profession']}]: ").strip()
    if profession:
        config['digital_human_info']['profession'] = profession
    
    # LLM配置
    print("\n🤖 LLM配置:")
    api_key = input(f"API密钥 [{'*' * 10}]: ").strip()
    if api_key:
        config['llm_config']['api_key'] = api_key
    
    model = input(f"模型 [{config['llm_config']['model']}]: ").strip()
    if model:
        config['llm_config']['model'] = model
    
    # 保存配置
    save_config = input("\n💾 是否保存配置到文件? (y/n) [y]: ").strip().lower()
    if save_config != 'n':
        save_config_to_file(config)
    
    return config

async def main():
    """🔥 主入口函数"""
    
    print("🔥 五层融合智能问卷系统")
    print("🎯 集成五层架构的AI问卷自动化解决方案")
    print("=" * 50)
    
    # 使用默认配置
    config = get_default_config()
    logger.info("📋 使用默认配置")
    
    # 显示配置信息
    print(f"\n🎯 即将执行五层融合智能问卷系统:")
    print(f"  👤 数字人: {config['digital_human_info']['name']}")
    print(f"  🎯 问卷URL: {config['questionnaire_url']}")
    print(f"  ⏰ 最大执行时间: {config['execution_config']['max_execution_time']}秒")
    
    # 执行五层融合系统
    result = await run_five_layer_fusion_questionnaire(config)
    
    # 显示结果
    display_execution_result(result)
    
    # 保存结果
    result_file = f"five_layer_result_{int(asyncio.get_event_loop().time())}.json"
    try:
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        logger.info(f"📄 执行结果已保存: {result_file}")
    except Exception as e:
        logger.error(f"❌ 保存结果失败: {e}")

if __name__ == "__main__":
    """🔥 五层融合智能问卷系统启动入口"""
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 用户中断执行")
    except Exception as e:
        logger.error(f"❌ 系统异常: {e}")
        print(f"❌ 系统执行异常: {e}")
        print("💡 请检查日志文件获取详细信息") 