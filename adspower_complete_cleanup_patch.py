#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AdsPower完全清理补丁
实现选择A方案：关闭浏览器后，立即从AdsPower列表中完全消失，释放所有资源

使用方法：
1. 导入这个模块
2. 替换原有的cleanup_session方法
3. 或者直接调用complete_cleanup_adspower_profile函数
"""

import asyncio
import logging
import requests
from typing import Dict, Optional

logger = logging.getLogger(__name__)

class AdsPowerCompleteCleanup:
    """AdsPower完全清理工具类"""
    
    def __init__(self, adspower_host: str = "http://local.adspower.net:50325"):
        self.adspower_host = adspower_host
    
    async def complete_cleanup_adspower_profile(self, profile_id: str, persona_name: str = "未知") -> Dict:
        """
        完全清理AdsPower配置文件
        
        参数:
            profile_id: AdsPower配置文件ID
            persona_name: 数字人名称（用于日志）
        
        返回:
            Dict: 清理结果
        """
        try:
            logger.info(f"🚀 开始完全清理AdsPower资源: {persona_name} ({profile_id})")
            
            # 1. 停止浏览器实例
            stop_success = await self._stop_browser_instance(profile_id)
            if stop_success:
                logger.info(f"✅ 浏览器实例已停止: {profile_id}")
                # 等待停止完成
                await asyncio.sleep(2)
            else:
                logger.warning(f"⚠️ 浏览器停止失败，继续尝试删除配置文件")
            
            # 2. 删除配置文件（完全清理）
            delete_success = await self._delete_browser_profile(profile_id)
            
            result = {
                "success": stop_success and delete_success,
                "browser_stopped": stop_success,
                "profile_deleted": delete_success,
                "profile_id": profile_id,
                "persona_name": persona_name
            }
            
            if result["success"]:
                logger.info(f"✅ {persona_name} AdsPower资源完全清理成功")
                logger.info(f"🎯 配置文件已从AdsPower应用列表中完全移除")
            else:
                logger.warning(f"⚠️ {persona_name} AdsPower资源清理部分失败")
                
            return result
            
        except Exception as e:
            logger.error(f"❌ 完全清理AdsPower资源失败: {e}")
            return {
                "success": False,
                "browser_stopped": False,
                "profile_deleted": False,
                "profile_id": profile_id,
                "persona_name": persona_name,
                "error": str(e)
            }
    
    async def _stop_browser_instance(self, profile_id: str) -> bool:
        """停止AdsPower浏览器实例"""
        try:
            url = f"{self.adspower_host}/api/v1/browser/stop"
            params = {"user_id": profile_id}
            
            logger.info(f"⏹️ 停止浏览器实例: {profile_id}")
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            
            if result.get("code") == 0:
                logger.info("✅ 浏览器实例停止成功")
                return True
            else:
                logger.warning(f"⚠️ 浏览器停止失败: {result.get('msg', '未知错误')}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 停止浏览器实例异常: {e}")
            return False
    
    async def _delete_browser_profile(self, profile_id: str) -> bool:
        """删除AdsPower配置文件（从列表中完全移除）"""
        try:
            url = f"{self.adspower_host}/api/v1/user/delete"
            data = {"user_ids": [profile_id]}
            
            logger.info(f"🗑️ 删除AdsPower配置文件: {profile_id}")
            
            response = requests.post(url, json=data, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            
            if result.get("code") == 0:
                logger.info("✅ AdsPower配置文件删除成功")
                logger.info("🎯 配置文件已从AdsPower应用列表中完全移除")
                return True
            else:
                logger.warning(f"⚠️ 配置文件删除失败: {result.get('msg', '未知错误')}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 删除配置文件异常: {e}")
            return False

# 全局实例
adspower_cleanup = AdsPowerCompleteCleanup()

async def patch_cleanup_session_method(webui_integration_instance, session_id: str) -> bool:
    """
    补丁方法：替换原有的cleanup_session方法
    实现完全清理模式
    
    参数:
        webui_integration_instance: AdsPowerWebUIIntegration实例
        session_id: 会话ID
    
    返回:
        bool: 是否成功清理
    """
    try:
        if session_id not in webui_integration_instance.active_sessions:
            logger.warning(f"⚠️ 会话不存在: {session_id}")
            return False
        
        session_info = webui_integration_instance.active_sessions[session_id]
        persona_name = session_info["persona_name"]
        profile_id = session_info.get("profile_id")
        
        logger.info(f"🧹 开始完全释放数字人 {persona_name} 的'新电脑'资源...")
        
        # 从活动会话中移除
        del webui_integration_instance.active_sessions[session_id]
        logger.info(f"🧹 清理会话资源: {persona_name}")
        
        # 完全清理AdsPower资源
        if profile_id:
            cleanup_result = await adspower_cleanup.complete_cleanup_adspower_profile(
                profile_id, persona_name
            )
            
            if cleanup_result["success"]:
                logger.info(f"✅ 数字人 {persona_name} 会话已完全清理（配置文件已删除）")
                return True
            else:
                logger.warning(f"⚠️ 数字人 {persona_name} 会话清理部分失败")
                return False
        else:
            logger.warning(f"⚠️ 未找到profile_id，无法清理AdsPower资源")
            return False
            
    except Exception as e:
        logger.error(f"❌ 清理会话失败: {e}")
        return False

# 浏览器关闭检测增强
async def detect_browser_manual_close_and_cleanup(agent, profile_id: str, persona_name: str = "未知") -> bool:
    """
    检测浏览器是否被手动关闭，如果是则自动完全清理资源
    
    参数:
        agent: browser_use agent实例
        profile_id: AdsPower配置文件ID
        persona_name: 数字人名称
    
    返回:
        bool: 是否检测到浏览器关闭并完成清理
    """
    try:
        # 检测浏览器是否被手动关闭
        browser_closed = await _detect_browser_manual_close(agent)
        
        if browser_closed:
            logger.warning(f"🚨 检测到浏览器被手动关闭: {persona_name}")
            
            # 自动完全清理AdsPower资源
            cleanup_result = await adspower_cleanup.complete_cleanup_adspower_profile(
                profile_id, persona_name
            )
            
            if cleanup_result["success"]:
                logger.info(f"✅ 检测到浏览器关闭，AdsPower资源已自动完全清理")
                return True
            else:
                logger.warning(f"⚠️ 检测到浏览器关闭，但资源清理部分失败")
                return False
        
        return False
        
    except Exception as e:
        logger.error(f"❌ 浏览器关闭检测失败: {e}")
        return False

async def _detect_browser_manual_close(agent) -> bool:
    """
    智能检测浏览器是否被用户手动关闭
    """
    try:
        if hasattr(agent, 'browser_context') and agent.browser_context:
            try:
                page = await agent.browser_context.get_current_page()
                if page:
                    await page.title()  # 轻量连接测试
                    return False  # 浏览器仍然可用
            except Exception as page_error:
                error_msg = str(page_error)
                browser_close_signals = [
                    "Target page, context or browser has been closed",
                    "No browser window available",
                    "Target closed",
                    "Browser closed",
                    "Connection closed",
                    "Session closed"
                ]
                
                if any(signal in error_msg for signal in browser_close_signals):
                    return True  # 确认浏览器已关闭
        
        return False  # 默认认为浏览器仍在运行
        
    except Exception as e:
        # 检测异常中的浏览器关闭信号
        error_msg = str(e)
        if any(signal in error_msg for signal in [
            "Target page, context or browser has been closed",
            "No browser window available", 
            "Target closed",
            "Browser closed"
        ]):
            return True
        
        return False  # 其他异常不认为是浏览器关闭

if __name__ == "__main__":
    # 测试代码
    async def test_cleanup():
        cleanup_tool = AdsPowerCompleteCleanup()
        result = await cleanup_tool.complete_cleanup_adspower_profile("test_profile_id", "测试数字人")
        print(f"清理结果: {result}")
    
    # asyncio.run(test_cleanup())
    print("AdsPower完全清理补丁已加载")
    print("使用方法：")
    print("1. 导入: from adspower_complete_cleanup_patch import patch_cleanup_session_method")
    print("2. 替换: webui_integration.cleanup_session = lambda session_id: patch_cleanup_session_method(webui_integration, session_id)") 