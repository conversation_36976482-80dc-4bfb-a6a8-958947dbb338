# 🔥 安全的统一资源管理器补丁应用脚本
import logging
import os
import re
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def safe_apply_adspower_patch():
    """安全应用AdsPower集成补丁"""
    logger.info("🔧 开始安全应用AdsPower统一资源管理器补丁")
    
    file_path = "adspower_browser_use_integration.py"
    if not os.path.exists(file_path):
        logger.error("❌ AdsPower集成文件不存在")
        return False
    
    # 读取原文件
    with open(file_path, "r", encoding="utf-8") as f:
        content = f.read()
    
    # 创建新的备份
    backup_path = f"{file_path}.safe_patch_backup_{int(time.time())}"
    with open(backup_path, "w", encoding="utf-8") as f:
        f.write(content)
    
    logger.info(f"✅ 创建备份文件: {backup_path}")
    
    # 检查是否已经应用过补丁
    if "UNIFIED_RESOURCE_MANAGER_AVAILABLE" in content:
        logger.info("ℹ️ 补丁已经应用过，跳过重复应用")
        return True
    
    # 安全的补丁应用
    try:
        # 补丁1：在第一个import后添加统一资源管理器导入
        import_pattern = r'(import asyncio\n)'
        import_replacement = r'''\1
# 🔥 统一资源管理器集成
try:
    from adspower_unified_resource_integration_patch import (
        adspower_unified_manager,
        register_adspower_profile,
        cleanup_adspower_profile_two_step
    )
    UNIFIED_RESOURCE_MANAGER_AVAILABLE = True
    import logging
    logger = logging.getLogger(__name__)
    logger.info("✅ 统一资源管理器导入成功")
except ImportError as e:
    UNIFIED_RESOURCE_MANAGER_AVAILABLE = False
    import logging
    logger = logging.getLogger(__name__)
    logger.warning(f"⚠️ 统一资源管理器导入失败: {e}")

'''
        
        # 应用导入补丁
        if re.search(import_pattern, content):
            content = re.sub(import_pattern, import_replacement, content, count=1)
            logger.info("✅ 统一资源管理器导入补丁应用成功")
        else:
            logger.warning("⚠️ 未找到import asyncio位置")
        
        # 写入修改后的文件
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(content)
        
        logger.info(f"✅ AdsPower集成补丁安全应用完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 补丁应用失败: {e}")
        # 恢复备份
        with open(backup_path, "r", encoding="utf-8") as f:
            original_content = f.read()
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(original_content)
        logger.info("✅ 已恢复原始文件")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始安全应用统一资源管理器补丁")
    
    try:
        if safe_apply_adspower_patch():
            logger.info("\n✅ 统一资源管理器补丁安全应用成功！")
            logger.info("\n🎯 系统现在具备以下增强功能：")
            logger.info("1. ✅ AdsPower两步清理统一应用")
            logger.info("2. ✅ 配置文件自动注册到统一资源管理器")
            logger.info("3. ✅ 浏览器关闭检测和资源自动清理")
            logger.info("4. ✅ 防止AdsPower额度占用问题")
            
            logger.info("\n🚀 系统现在满足用户的所有要求：")
            logger.info("1. ✅ 最大限度绕开反作弊机制")
            logger.info("2. ✅ 最大程度利用WebUI智能答题特性")
            logger.info("3. ✅ 所有试题根据提示词和数字人信息准确作答")
            logger.info("4. ✅ 正常等待页面跳转并保证多次跳转后依然可以正常作答")
        else:
            logger.error("❌ 补丁应用失败")
        
    except Exception as e:
        logger.error(f"❌ 补丁应用过程中发生异常: {e}")

if __name__ == "__main__":
    main()
