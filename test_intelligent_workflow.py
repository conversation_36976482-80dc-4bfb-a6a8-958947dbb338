#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试智能作答流程是否正常工作
"""

import asyncio
import logging
from intelligent_three_stage_core import ThreeStageIntelligentCore

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_intelligent_workflow():
    """测试智能作答流程"""
    try:
        logger.info("🚀 开始测试智能作答流程...")
        
        # 创建三阶段智能核心系统
        core = ThreeStageIntelligentCore()
        
        # 测试问卷URL
        questionnaire_url = "https://www.wjx.cn/vm/ml5AbmN.aspx"
        
        # 执行三阶段工作流
        result = await core.execute_complete_three_stage_workflow(
            questionnaire_url=questionnaire_url,
            scout_count=1,  # 只用1个敢死队成员测试
            target_count=1   # 只用1个大部队成员测试
        )
        
        logger.info("✅ 测试结果:")
        logger.info(f"   状态: {result.get('status')}")
        logger.info(f"   敢死队成员数: {len(result.get('scout_experiences', []))}")
        logger.info(f"   大部队成员数: {len(result.get('target_results', []))}")
        
        # 检查是否调用了正确的智能作答流程
        if result.get('status') == 'completed':
            logger.info("🎉 智能作答流程测试成功！")
        else:
            logger.warning("⚠️ 智能作答流程可能存在问题")
            
        return result
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return {"status": "failed", "error": str(e)}

if __name__ == "__main__":
    asyncio.run(test_intelligent_workflow()) 