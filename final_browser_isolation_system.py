#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
最终版浏览器隔离系统
集成AdsPower和青果代理，为每个浏览器提供独立的运行环境
"""

import time
import logging
import asyncio
from typing import Dict, Optional, List
from src.config.proxy_config import (
    get_proxy_config,
    validate_proxy_config
)
from enhanced_adspower_lifecycle import EnhancedAdsPowerLifecycle

logger = logging.getLogger(__name__)

class BrowserIsolationSystem:
    """浏览器隔离系统"""
    
    def __init__(self):
        # 从配置系统获取代理配置
        self.proxy_config = get_proxy_config()
        
        # 验证配置
        error = validate_proxy_config()
        if error:
            raise ValueError(f"代理配置错误: {error}")
        
        # 初始化AdsPower生命周期管理器
        self.lifecycle_manager = EnhancedAdsPowerLifecycle()
        
        # 浏览器环境管理
        self.browser_environments = {}  # 浏览器环境映射
    
    async def create_isolated_browser(self, browser_name: str, use_tunnel: bool = True) -> Dict:
        """创建隔离的浏览器环境"""
        try:
            logger.info(f"创建隔离浏览器环境: {browser_name}")
            
            # 1. 创建浏览器配置文件
            profile = self.lifecycle_manager.create_browser_profile(browser_name, use_tunnel)
            if not profile:
                raise Exception("创建浏览器配置文件失败")
            
            # 2. 启动浏览器
            browser = self.lifecycle_manager.start_browser(browser_name)
            if not browser:
                raise Exception("启动浏览器失败")
            
            # 3. 保存环境信息
            self.browser_environments[browser_name] = {
                "profile": profile,
                "browser": browser,
                "created_at": time.time(),
                "status": "running"
            }
            
            logger.info(f"浏览器环境创建成功: {browser_name}")
            return {
                "browser_name": browser_name,
                "profile": profile,
                "browser": browser
            }
            
        except Exception as e:
            logger.error(f"创建隔离浏览器环境失败: {e}")
            # 清理失败的资源
            await self.cleanup_browser(browser_name)
            return {}
    
    async def cleanup_browser(self, browser_name: str) -> bool:
        """清理浏览器环境"""
        try:
            logger.info(f"清理浏览器环境: {browser_name}")
            
            # 关闭浏览器
            if not self.lifecycle_manager.close_browser(browser_name):
                logger.warning(f"关闭浏览器失败: {browser_name}")
            
            # 清理环境信息
            self.browser_environments.pop(browser_name, None)
            
            logger.info(f"浏览器环境清理完成: {browser_name}")
            return True
            
        except Exception as e:
            logger.error(f"清理浏览器环境失败: {e}")
            return False
    
    def get_browser_status(self, browser_name: str = None) -> Dict:
        """获取浏览器状态"""
        try:
            if browser_name:
                # 获取指定浏览器状态
                env = self.browser_environments.get(browser_name)
                if not env:
                    return {"error": "浏览器环境不存在"}
                
                return {
                    "browser_name": browser_name,
                    "status": env["status"],
                    "created_at": time.strftime("%H:%M:%S", time.localtime(env["created_at"])),
                    "profile": env["profile"],
                    "browser": env["browser"]
                }
            else:
                # 获取所有浏览器状态
                return {
                    "total_count": len(self.browser_environments),
                    "browsers": [
                        {
                            "browser_name": name,
                            "status": env["status"],
                            "created_at": time.strftime("%H:%M:%S", time.localtime(env["created_at"]))
                        }
                        for name, env in self.browser_environments.items()
                    ]
                }
            
        except Exception as e:
            logger.error(f"获取浏览器状态失败: {e}")
            return {"error": str(e)}
    
    async def cleanup_all_browsers(self):
        """清理所有浏览器环境"""
        logger.info("清理所有浏览器环境...")
        
        # 清理每个浏览器
        for browser_name in list(self.browser_environments.keys()):
            await self.cleanup_browser(browser_name)
        
        # 清理生命周期管理器
        self.lifecycle_manager.cleanup_all_browsers()
        
        # 清理内部状态
        self.browser_environments.clear()

# 测试函数
async def test_browser_isolation():
    """测试浏览器隔离系统"""
    print("🧪 测试浏览器隔离系统...")
    
    system = BrowserIsolationSystem()
    
    try:
        # 1. 创建隔离浏览器
        print("\n1️⃣ 创建隔离浏览器")
        
        # 使用隧道代理
        browser1 = await system.create_isolated_browser("测试浏览器1", use_tunnel=True)
        print("✅ 创建隧道代理浏览器:")
        print(f"   名称: {browser1['browser_name']}")
        print(f"   配置: {browser1['profile']}")
        print(f"   浏览器: {browser1['browser']}")
        
        # 使用普通代理
        browser2 = await system.create_isolated_browser("测试浏览器2", use_tunnel=False)
        print("\n✅ 创建普通代理浏览器:")
        print(f"   名称: {browser2['browser_name']}")
        print(f"   配置: {browser2['profile']}")
        print(f"   浏览器: {browser2['browser']}")
        
        # 2. 获取浏览器状态
        print("\n2️⃣ 获取浏览器状态")
        
        # 单个浏览器状态
        status1 = system.get_browser_status("测试浏览器1")
        print("✅ 隧道代理浏览器状态:")
        print(f"   {status1}")
        
        # 所有浏览器状态
        all_status = system.get_browser_status()
        print("\n✅ 所有浏览器状态:")
        print(f"   总数量: {all_status['total_count']}")
        for browser in all_status['browsers']:
            print(f"   - {browser['browser_name']}: {browser['status']} (创建时间: {browser['created_at']})")
        
        # 3. 清理浏览器
        print("\n3️⃣ 清理浏览器")
        
        if await system.cleanup_browser("测试浏览器1"):
            print("✅ 清理隧道代理浏览器成功")
        else:
            print("❌ 清理隧道代理浏览器失败")
        
        if await system.cleanup_browser("测试浏览器2"):
            print("✅ 清理普通代理浏览器成功")
        else:
            print("❌ 清理普通代理浏览器失败")
        
        # 4. 清理所有资源
        print("\n4️⃣ 清理所有资源")
        await system.cleanup_all_browsers()
        print("✅ 资源清理完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        await system.cleanup_all_browsers()

if __name__ == "__main__":
    asyncio.run(test_browser_isolation()) 