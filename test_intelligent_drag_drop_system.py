#!/usr/bin/env python3
"""
🎯 智能拖拽排序系统测试
验证拖拽排序引擎的各项功能

测试内容：
1. 题型检测准确性
2. 智能排序评估
3. 拖拽执行策略
4. WebUI集成测试

作者: AI Assistant
日期: 2024
"""

import asyncio
import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_drag_drop_system.log')
    ]
)

logger = logging.getLogger(__name__)

class DragDropSystemTester:
    """🎯 拖拽排序系统测试器"""
    
    def __init__(self):
        self.test_results = []
        self.digital_human_info = {
            'name': '张伟',
            'age': '32',
            'location': '北京',
            'profession': '软件工程师',
            'education': '本科',
            'income': '15000',
            'gender': '男'
        }
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始智能拖拽排序系统全面测试")
        
        test_methods = [
            self.test_drag_drop_engine_import,
            self.test_question_type_detection,
            self.test_importance_scoring,
            self.test_ranking_evaluation,
            self.test_webui_integration,
            self.test_comprehensive_scenarios
        ]
        
        for test_method in test_methods:
            try:
                await test_method()
            except Exception as e:
                logger.error(f"❌ 测试失败 {test_method.__name__}: {e}")
                self.test_results.append({
                    'test': test_method.__name__,
                    'status': 'FAILED',
                    'error': str(e)
                })
        
        self.print_test_summary()
    
    async def test_drag_drop_engine_import(self):
        """测试拖拽排序引擎导入"""
        logger.info("🔍 测试拖拽排序引擎导入...")
        
        try:
            from intelligent_drag_drop_ranking_engine import IntelligentDragDropRankingEngine
            engine = IntelligentDragDropRankingEngine()
            
            # 验证引擎属性
            assert hasattr(engine, 'ranking_strategies'), "缺少排序策略属性"
            assert hasattr(engine, 'drag_execution_strategies'), "缺少拖拽执行策略属性"
            assert hasattr(engine, 'handle_drag_drop_question'), "缺少主处理方法"
            
            logger.info("✅ 拖拽排序引擎导入成功")
            self.test_results.append({
                'test': 'drag_drop_engine_import',
                'status': 'PASSED',
                'details': '引擎导入和初始化成功'
            })
            
        except Exception as e:
            logger.error(f"❌ 拖拽排序引擎导入失败: {e}")
            raise
    
    async def test_question_type_detection(self):
        """测试题型检测功能"""
        logger.info("🔍 测试题型检测功能...")
        
        try:
            from intelligent_drag_drop_ranking_engine import IntelligentDragDropRankingEngine
            engine = IntelligentDragDropRankingEngine()
            
            # 测试题型确定逻辑
            test_cases = [
                {
                    'page_content': {
                        'draggable_elements': [{'text': '选项1'}, {'text': '选项2'}],
                        'drop_zones': [{'text': '目标区域'}],
                        'shuttle_components': [],
                        'sortable_lists': [],
                        'question_text': '请按重要性排序以下选项'
                    },
                    'expected_type': 'ranking_sort'
                },
                {
                    'page_content': {
                        'draggable_elements': [],
                        'drop_zones': [],
                        'shuttle_components': [{'leftList': {'items': [{'text': '选项1'}]}}],
                        'sortable_lists': [],
                        'question_text': '请选择相关选项'
                    },
                    'expected_type': 'shuttle_transfer'
                },
                {
                    'page_content': {
                        'draggable_elements': [],
                        'drop_zones': [],
                        'shuttle_components': [],
                        'sortable_lists': [{'items': [{'text': '选项1'}, {'text': '选项2'}]}],
                        'question_text': '请拖拽排序'
                    },
                    'expected_type': 'priority_ordering'
                }
            ]
            
            passed_tests = 0
            for i, test_case in enumerate(test_cases):
                detected_type = engine._determine_drag_drop_question_type(test_case['page_content'])
                if detected_type == test_case['expected_type']:
                    logger.info(f"✅ 题型检测测试 {i+1} 通过: {detected_type}")
                    passed_tests += 1
                else:
                    logger.warning(f"⚠️ 题型检测测试 {i+1} 失败: 期望 {test_case['expected_type']}, 实际 {detected_type}")
            
            self.test_results.append({
                'test': 'question_type_detection',
                'status': 'PASSED' if passed_tests == len(test_cases) else 'PARTIAL',
                'details': f'通过 {passed_tests}/{len(test_cases)} 个测试'
            })
            
        except Exception as e:
            logger.error(f"❌ 题型检测测试失败: {e}")
            raise
    
    async def test_importance_scoring(self):
        """测试重要性评分功能"""
        logger.info("🔍 测试重要性评分功能...")
        
        try:
            from intelligent_drag_drop_ranking_engine import IntelligentDragDropRankingEngine
            engine = IntelligentDragDropRankingEngine()
            
            test_options = [
                {'text': '工作稳定性', 'expected_high_score': True},
                {'text': '北京户口', 'expected_high_score': True},
                {'text': '软件开发技能', 'expected_high_score': True},
                {'text': '其他无关选项', 'expected_high_score': False}
            ]
            
            passed_tests = 0
            for option in test_options:
                score = await engine._calculate_importance_score(
                    option['text'], 
                    self.digital_human_info, 
                    'importance', 
                    '请按重要性排序'
                )
                
                is_high_score = score >= 0.7
                if is_high_score == option['expected_high_score']:
                    logger.info(f"✅ 评分测试通过: '{option['text']}' 得分 {score:.3f}")
                    passed_tests += 1
                else:
                    logger.warning(f"⚠️ 评分测试失败: '{option['text']}' 得分 {score:.3f}, 期望高分: {option['expected_high_score']}")
            
            self.test_results.append({
                'test': 'importance_scoring',
                'status': 'PASSED' if passed_tests == len(test_options) else 'PARTIAL',
                'details': f'通过 {passed_tests}/{len(test_options)} 个评分测试'
            })
            
        except Exception as e:
            logger.error(f"❌ 重要性评分测试失败: {e}")
            raise
    
    async def test_ranking_evaluation(self):
        """测试排序评估功能"""
        logger.info("🔍 测试排序评估功能...")
        
        try:
            from intelligent_drag_drop_ranking_engine import IntelligentDragDropRankingEngine
            engine = IntelligentDragDropRankingEngine()
            
            test_options = [
                {'text': '工作稳定性', 'type': 'draggable', 'element_info': {}, 'original_index': 0},
                {'text': '薪资水平', 'type': 'draggable', 'element_info': {}, 'original_index': 1},
                {'text': '北京户口', 'type': 'draggable', 'element_info': {}, 'original_index': 2},
                {'text': '软件开发技能', 'type': 'draggable', 'element_info': {}, 'original_index': 3},
                {'text': '其他选项', 'type': 'draggable', 'element_info': {}, 'original_index': 4}
            ]
            
            ranked_options = await engine._intelligent_ranking_evaluation(
                test_options, 
                self.digital_human_info, 
                'importance', 
                '请按重要性排序以下选项'
            )
            
            # 验证排序结果
            assert len(ranked_options) == len(test_options), "排序后选项数量不匹配"
            assert all('importance_score' in option for option in ranked_options), "缺少重要性得分"
            assert all('target_position' in option for option in ranked_options), "缺少目标位置"
            assert all('ranking_reason' in option for option in ranked_options), "缺少排序理由"
            
            # 验证排序顺序（得分递减）
            scores = [option['importance_score'] for option in ranked_options]
            assert scores == sorted(scores, reverse=True), "排序顺序不正确"
            
            logger.info("✅ 排序评估测试通过")
            logger.info("📊 排序结果:")
            for i, option in enumerate(ranked_options[:3]):  # 显示前3名
                logger.info(f"  {i+1}. {option['text']} (得分: {option['importance_score']:.3f})")
            
            self.test_results.append({
                'test': 'ranking_evaluation',
                'status': 'PASSED',
                'details': f'成功排序 {len(ranked_options)} 个选项'
            })
            
        except Exception as e:
            logger.error(f"❌ 排序评估测试失败: {e}")
            raise
    
    async def test_webui_integration(self):
        """测试WebUI集成"""
        logger.info("🔍 测试WebUI集成...")
        
        try:
            # 测试控制器集成
            from src.controller.custom_controller import CustomController
            
            controller = CustomController()
            
            # 验证拖拽排序引擎注册
            assert hasattr(controller, 'register_intelligent_drag_drop_ranking_engine'), "缺少拖拽排序引擎注册方法"
            
            # 尝试注册引擎
            controller.register_intelligent_drag_drop_ranking_engine()
            
            # 验证引擎实例
            if hasattr(controller, 'drag_drop_engine'):
                logger.info("✅ 拖拽排序引擎已成功集成到控制器")
            else:
                logger.warning("⚠️ 拖拽排序引擎未能成功集成")
            
            self.test_results.append({
                'test': 'webui_integration',
                'status': 'PASSED',
                'details': '拖拽排序引擎成功集成到WebUI控制器'
            })
            
        except Exception as e:
            logger.error(f"❌ WebUI集成测试失败: {e}")
            # 这个测试失败不影响其他测试
            self.test_results.append({
                'test': 'webui_integration',
                'status': 'FAILED',
                'error': str(e)
            })
    
    async def test_comprehensive_scenarios(self):
        """测试综合场景"""
        logger.info("🔍 测试综合场景...")
        
        try:
            from intelligent_drag_drop_ranking_engine import IntelligentDragDropRankingEngine
            engine = IntelligentDragDropRankingEngine()
            
            # 模拟复杂页面内容
            complex_page_content = {
                'draggable_elements': [
                    {'index': 0, 'text': '家庭和睦', 'className': 'draggable-item'},
                    {'index': 1, 'text': '事业成功', 'className': 'draggable-item'},
                    {'index': 2, 'text': '身体健康', 'className': 'draggable-item'},
                    {'index': 3, 'text': '经济自由', 'className': 'draggable-item'},
                    {'index': 4, 'text': '个人成长', 'className': 'draggable-item'}
                ],
                'drop_zones': [
                    {'index': 0, 'text': '排序区域', 'className': 'drop-zone'}
                ],
                'shuttle_components': [],
                'sortable_lists': [],
                'question_text': '请按照对您人生的重要性程度，将以下选项从最重要到最不重要进行排序',
                'page_structure': {
                    'has_sortable_js': True,
                    'has_jquery_ui': False,
                    'has_drag_events': True,
                    'total_interactive_elements': 6
                }
            }
            
            # 测试题型检测
            question_type = engine._determine_drag_drop_question_type(complex_page_content)
            assert question_type == 'ranking_sort', f"题型检测错误: {question_type}"
            
            # 测试复杂度评估
            complexity = engine._assess_question_complexity(complex_page_content)
            assert complexity in ['simple', 'medium', 'complex'], f"复杂度评估错误: {complexity}"
            
            # 测试选项提取
            question_analysis = {
                'page_content': complex_page_content,
                'question_type': question_type,
                'complexity_level': complexity
            }
            
            options = await engine._extract_sortable_options(None, question_analysis)
            assert len(options) == 5, f"选项提取数量错误: {len(options)}"
            
            # 测试智能排序
            ranked_options = await engine._intelligent_ranking_evaluation(
                options, 
                self.digital_human_info, 
                'importance', 
                complex_page_content['question_text']
            )
            
            assert len(ranked_options) == 5, "排序后选项数量错误"
            assert all('importance_score' in option for option in ranked_options), "缺少重要性得分"
            
            logger.info("✅ 综合场景测试通过")
            logger.info("📊 最终排序结果:")
            for i, option in enumerate(ranked_options):
                logger.info(f"  {i+1}. {option['text']} (得分: {option['importance_score']:.3f}) - {option['ranking_reason']}")
            
            self.test_results.append({
                'test': 'comprehensive_scenarios',
                'status': 'PASSED',
                'details': f'成功处理包含{len(options)}个选项的复杂排序场景'
            })
            
        except Exception as e:
            logger.error(f"❌ 综合场景测试失败: {e}")
            raise
    
    def print_test_summary(self):
        """打印测试总结"""
        logger.info("\n" + "="*60)
        logger.info("🎯 智能拖拽排序系统测试总结")
        logger.info("="*60)
        
        passed_count = sum(1 for result in self.test_results if result['status'] == 'PASSED')
        failed_count = sum(1 for result in self.test_results if result['status'] == 'FAILED')
        partial_count = sum(1 for result in self.test_results if result['status'] == 'PARTIAL')
        
        logger.info(f"📊 测试统计:")
        logger.info(f"  ✅ 通过: {passed_count}")
        logger.info(f"  ❌ 失败: {failed_count}")
        logger.info(f"  ⚠️ 部分通过: {partial_count}")
        logger.info(f"  📝 总计: {len(self.test_results)}")
        
        logger.info(f"\n📋 详细结果:")
        for result in self.test_results:
            status_icon = "✅" if result['status'] == 'PASSED' else "❌" if result['status'] == 'FAILED' else "⚠️"
            logger.info(f"  {status_icon} {result['test']}: {result['status']}")
            if 'details' in result:
                logger.info(f"    📝 {result['details']}")
            if 'error' in result:
                logger.info(f"    🔍 错误: {result['error']}")
        
        # 总体评估
        if failed_count == 0:
            logger.info(f"\n🎉 所有测试通过！智能拖拽排序系统运行正常")
        elif failed_count <= 2:
            logger.info(f"\n⚠️ 大部分测试通过，有少量问题需要修复")
        else:
            logger.info(f"\n❌ 多个测试失败，需要重点检查系统")
        
        logger.info("="*60)

async def main():
    """主函数"""
    try:
        tester = DragDropSystemTester()
        await tester.run_all_tests()
        
    except KeyboardInterrupt:
        logger.info("🛑 测试被用户中断")
    except Exception as e:
        logger.error(f"❌ 测试执行失败: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main()) 