#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试browser-use Agent的正确使用方式
"""

import asyncio
import logging
from langchain_google_genai import ChatGoogleGenerativeAI

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_browser_use_agent():
    """测试browser-use Agent"""
    try:
        print("🔧 测试browser-use Agent...")

        # 导入组件
        from browser_use.agent.service import Agent
        from browser_use.browser.browser import Browser

        print("✅ 成功导入browser-use组件")

        # 创建LLM
        llm = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash",
            temperature=0.1,
            google_api_key="AIzaSyDJKqkOKKKKKKKKKKKKKKKKKKKKKKKKKKK"  # 替换为实际的API密钥
        )
        print("✅ LLM创建成功")

        # 创建Browser配置（连接到AdsPower）
        debug_port = "127.0.0.1:65043"  # 使用之前测试中的端口
        browser_config = {
            "headless": False,
            "cdp_url": f"http://{debug_port}",
        }

        print(f"🚀 创建Browser连接到AdsPower: {debug_port}")
        browser = Browser(config=browser_config)
        print("✅ Browser创建成功")

        # 创建Agent
        print("🚀 创建Agent...")
        agent_config = {
            "llm": llm,
            "browser": browser
        }
        agent = Agent(config=agent_config)
        print("✅ Agent创建成功")

        # 测试导航
        print("🚀 测试导航到百度...")
        result = await agent.act("请导航到 https://www.baidu.com")
        print(f"✅ 导航结果: {result}")

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 browser-use Agent测试")
    print("=" * 50)

    success = asyncio.run(test_browser_use_agent())

    print("\n" + "=" * 50)
    print(f"📊 测试结果: {'✅ 成功' if success else '❌ 失败'}")