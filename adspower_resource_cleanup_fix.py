#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AdsPower资源清理修复补丁
确保AdsPower配置文件从应用列表中完全移除，真正释放浏览器额度

问题根本原因分析：
1. 当前代码只调用了 browser/stop API - 这只是停止浏览器
2. 没有正确调用 user/delete API - 这才是删除配置文件的关键
3. 从日志看："⚠️ AdsPower关闭请求失败: 404" - API调用有问题

解决方案：
1. 正确的释放流程：先stop浏览器，再delete配置文件
2. 使用正确的API调用方式
3. 确保从AdsPower应用的环境管理列表中完全移除记录
"""

import asyncio
import logging
import aiohttp
from typing import Dict, Optional

logger = logging.getLogger(__name__)

class AdsPowerResourceCleanupFix:
    """AdsPower资源清理修复类"""
    
    def __init__(self, adspower_host: str = "http://local.adspower.net:50325"):
        self.adspower_host = adspower_host
    
    async def complete_release_adspower_resources(
        self, 
        profile_id: str, 
        persona_name: str = "未知"
    ) -> Dict:
        """
        完全释放AdsPower资源 - 确保从应用列表中移除
        
        这是正确的AdsPower资源释放标准流程：
        1. 调用 /api/v1/browser/stop - 停止浏览器实例
        2. 调用 /api/v1/user/delete - 删除配置文件（从列表中移除）
        
        参数:
            profile_id: AdsPower配置文件ID
            persona_name: 数字人名称
        
        返回:
            Dict: 详细的释放结果
        """
        try:
            logger.info(f"🚀 开始完全释放AdsPower资源: {persona_name} ({profile_id})")
            
            # 第一步：停止浏览器实例
            logger.info("📍 第一步：停止浏览器实例...")
            stop_result = await self._stop_browser_instance(profile_id)
            
            if stop_result["success"]:
                logger.info("✅ 浏览器实例停止成功")
                # 等待停止完成
                await asyncio.sleep(3)
            else:
                logger.warning("⚠️ 浏览器停止失败，但继续尝试删除配置文件")
            
            # 第二步：删除配置文件（这是关键步骤）
            logger.info("📍 第二步：删除配置文件（从环境列表中移除）...")
            delete_result = await self._delete_browser_profile(profile_id)
            
            # 汇总结果
            final_result = {
                "success": stop_result["success"] and delete_result["success"],
                "profile_id": profile_id,
                "persona_name": persona_name,
                "browser_stopped": stop_result["success"],
                "profile_deleted": delete_result["success"],
                "resource_fully_released": delete_result["success"]
            }
            
            # 结果评估
            if final_result["success"]:
                logger.info("🎉 AdsPower资源完全释放成功!")
                logger.info("✅ 配置文件已从AdsPower应用环境列表中完全移除")
                logger.info("🔄 浏览器额度已释放，可用于新的数字人")
            elif final_result["profile_deleted"]:
                logger.info("✅ 配置文件删除成功（主要目标达成）")
                logger.info("✅ 资源已从AdsPower应用列表中移除")
            else:
                logger.error("❌ AdsPower资源释放失败")
                logger.error("⚠️ 配置文件可能仍在AdsPower应用列表中")
                logger.error("⚠️ 浏览器额度可能未释放")
            
            return final_result
            
        except Exception as e:
            logger.error(f"❌ AdsPower资源释放过程异常: {e}")
            return {
                "success": False,
                "profile_id": profile_id,
                "persona_name": persona_name,
                "browser_stopped": False,
                "profile_deleted": False,
                "resource_fully_released": False,
                "error": str(e)
            }
    
    async def _stop_browser_instance(self, profile_id: str) -> Dict:
        """停止AdsPower浏览器实例"""
        try:
            # 根据AdsPower API文档，使用GET请求停止浏览器
            url = f"{self.adspower_host}/api/v1/browser/stop"
            params = {"user_id": profile_id}
            
            logger.info(f"⏹️ 调用AdsPower停止浏览器API")
            logger.info(f"   URL: {url}")
            logger.info(f"   参数: {params}")
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=15)) as session:
                async with session.get(url, params=params) as response:
                    status_code = response.status
                    logger.info(f"   响应状态: {status_code}")
                    
                    if status_code == 200:
                        result = await response.json()
                        logger.info(f"   响应内容: {result}")
                        
                        if result.get("code") == 0:
                            return {
                                "success": True,
                                "message": "浏览器停止成功",
                                "api_response": result
                            }
                        else:
                            return {
                                "success": False,
                                "message": f"浏览器停止失败: {result.get('msg', '未知错误')}",
                                "api_response": result
                            }
                    else:
                        response_text = await response.text()
                        logger.error(f"   错误响应: {response_text}")
                        return {
                            "success": False,
                            "message": f"API请求失败，状态码: {status_code}",
                            "error_response": response_text
                        }
                        
        except Exception as e:
            logger.error(f"❌ 停止浏览器实例异常: {e}")
            return {
                "success": False,
                "message": f"停止浏览器异常: {str(e)}",
                "error": str(e)
            }
    
    async def _delete_browser_profile(self, profile_id: str) -> Dict:
        """删除AdsPower配置文件（从环境列表中完全移除）- 这是关键步骤"""
        try:
            # 根据AdsPower API文档，使用POST请求删除配置文件
            url = f"{self.adspower_host}/api/v1/user/delete"
            data = {"user_ids": [profile_id]}
            
            logger.info(f"🗑️ 调用AdsPower删除配置文件API")
            logger.info(f"   URL: {url}")
            logger.info(f"   数据: {data}")
            logger.info(f"   说明: 这将从AdsPower应用环境列表中完全移除配置文件")
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=15)) as session:
                async with session.post(url, json=data) as response:
                    status_code = response.status
                    logger.info(f"   响应状态: {status_code}")
                    
                    if status_code == 200:
                        result = await response.json()
                        logger.info(f"   响应内容: {result}")
                        
                        if result.get("code") == 0:
                            return {
                                "success": True,
                                "message": "配置文件删除成功，已从环境列表中移除",
                                "api_response": result
                            }
                        else:
                            return {
                                "success": False,
                                "message": f"配置文件删除失败: {result.get('msg', '未知错误')}",
                                "api_response": result
                            }
                    else:
                        response_text = await response.text()
                        logger.error(f"   错误响应: {response_text}")
                        return {
                            "success": False,
                            "message": f"API请求失败，状态码: {status_code}",
                            "error_response": response_text
                        }
                        
        except Exception as e:
            logger.error(f"❌ 删除配置文件异常: {e}")
            return {
                "success": False,
                "message": f"删除配置文件异常: {str(e)}",
                "error": str(e)
            }

# 全局修复实例
adspower_cleanup_fix = AdsPowerResourceCleanupFix()

async def apply_fixed_adspower_cleanup(
    profile_id: str, 
    persona_name: str = "未知"
) -> Dict:
    """
    应用修复版的AdsPower资源清理
    
    这个函数可以直接替换原有的清理逻辑
    确保AdsPower配置文件从应用列表中完全移除
    
    使用方法：
    result = await apply_fixed_adspower_cleanup("profile_id", "数字人名称")
    if result["success"]:
        print("资源已完全释放")
    """
    return await adspower_cleanup_fix.complete_release_adspower_resources(
        profile_id, persona_name
    )

def patch_existing_cleanup_method(integration_instance):
    """
    为现有的集成实例应用清理方法补丁
    
    使用方法：
    patch_existing_cleanup_method(your_integration_instance)
    """
    try:
        # 替换原有的清理方法
        async def patched_cleanup_method(profile_id: str, persona_name: str = "未知"):
            logger.info("🔧 使用修复版AdsPower资源清理")
            return await apply_fixed_adspower_cleanup(profile_id, persona_name)
        
        # 应用补丁
        integration_instance._delete_profile = patched_cleanup_method
        logger.info("✅ AdsPower清理方法补丁已应用")
        
    except Exception as e:
        logger.error(f"❌ 应用清理方法补丁失败: {e}")

if __name__ == "__main__":
    # 测试修复功能
    async def test_cleanup():
        logging.basicConfig(level=logging.INFO)
        
        # 测试清理功能
        test_profile_id = "k10lf6si"  # 使用实际的profile_id
        result = await apply_fixed_adspower_cleanup(test_profile_id, "测试数字人")
        
        print("\n" + "="*60)
        print("测试结果:")
        print(f"清理成功: {result['success']}")
        print(f"浏览器停止: {result['browser_stopped']}")
        print(f"配置文件删除: {result['profile_deleted']}")
        print(f"资源完全释放: {result['resource_fully_released']}")
        print("="*60)
    
    # 取消注释下行来运行测试
    # asyncio.run(test_cleanup()) 