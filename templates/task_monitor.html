<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务监控 - 智能问卷自动填写系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 2em;
            font-weight: 300;
        }
        
        .header .task-info {
            text-align: right;
        }
        
        .header .task-id {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .header .task-status {
            font-size: 1.3em;
            font-weight: 600;
            margin-top: 5px;
        }
        
        .main-content {
            padding: 40px;
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
        }
        
        .left-panel {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }
        
        .right-panel {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.4em;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .progress-container {
            margin-bottom: 30px;
        }
        
        .progress-steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            position: relative;
        }
        
        .progress-steps::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 0;
            right: 0;
            height: 2px;
            background: #e9ecef;
            z-index: 1;
        }
        
        .progress-line {
            position: absolute;
            top: 20px;
            left: 0;
            height: 2px;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            transition: width 0.5s ease;
            z-index: 2;
        }
        
        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 3;
        }
        
        .step-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #666;
            transition: all 0.3s ease;
        }
        
        .step.active .step-circle {
            background: linear-gradient(135deg, #4facfe, #00f2fe);
            color: white;
            transform: scale(1.1);
        }
        
        .step.completed .step-circle {
            background: #28a745;
            color: white;
        }
        
        .step-label {
            margin-top: 10px;
            font-size: 0.9em;
            text-align: center;
            color: #666;
            font-weight: 500;
        }
        
        .step.active .step-label {
            color: #4facfe;
            font-weight: 600;
        }
        
        .assignments-container {
            display: flex;
            gap: 20px;
        }
        
        .assignment-tabs {
            display: flex;
            margin-bottom: 20px;
            background: #e9ecef;
            border-radius: 8px;
            padding: 4px;
        }
        
        .tab-button {
            flex: 1;
            padding: 12px 20px;
            border: none;
            background: transparent;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .tab-button.active {
            background: white;
            color: #4facfe;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .assignment-list {
            display: grid;
            gap: 15px;
        }
        
        .assignment-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #4facfe;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }
        
        .assignment-item:hover {
            transform: translateY(-2px);
        }
        
        .assignment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .assignment-name {
            font-weight: 600;
            color: #333;
            font-size: 1.1em;
        }
        
        .assignment-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: 600;
        }
        
        .status-ready { background: #e3f2fd; color: #1976d2; }
        .status-running { background: #fff3e0; color: #f57c00; }
        .status-success { background: #e8f5e8; color: #2e7d32; }
        .status-failed { background: #ffebee; color: #c62828; }
        
        .assignment-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        
        .detail-item {
            display: flex;
            flex-direction: column;
        }
        
        .detail-label {
            font-size: 0.8em;
            color: #666;
            margin-bottom: 2px;
        }
        
        .detail-value {
            font-weight: 600;
            color: #333;
        }
        
        .results-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .result-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .result-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .result-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        .danger { color: #dc3545; }
        .info { color: #17a2b8; }
        
        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #4facfe, #00f2fe);
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .cost-panel {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .cost-header {
            text-align: center;
            margin-bottom: 15px;
        }
        
        .cost-total {
            font-size: 2em;
            font-weight: bold;
            color: #d63384;
        }
        
        .cost-label {
            color: #6f42c1;
            font-weight: 600;
        }
        
        .cost-breakdown {
            display: grid;
            gap: 10px;
        }
        
        .cost-item {
            background: rgba(255,255,255,0.8);
            padding: 12px;
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .cost-item-name {
            font-weight: 600;
            color: #333;
        }
        
        .cost-item-value {
            font-weight: bold;
            color: #d63384;
        }
        
        .optimization-panel {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 15px;
            padding: 20px;
        }
        
        .optimization-panel h3 {
            color: #0066cc;
            margin-bottom: 15px;
        }
        
        .optimization-tips {
            list-style: none;
            padding: 0;
        }
        
        .optimization-tips li {
            padding: 8px 0;
            border-bottom: 1px solid #cce7ff;
            color: #333;
        }
        
        .optimization-tips li:last-child {
            border-bottom: none;
        }
        
        .optimization-tips li::before {
            content: "💡";
            margin-right: 8px;
        }
        
        .error-message {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                padding: 20px;
            }
            
            .header {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }
            
            .progress-steps {
                flex-direction: column;
                gap: 20px;
            }
            
            .progress-steps::before {
                display: none;
            }
            
            .assignments-container {
                flex-direction: column;
            }
            
            .results-summary {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 任务监控</h1>
            <div class="task-info">
                <div class="task-id">任务ID: {{ task.task_id }}</div>
                <div class="task-status">{{ task.phase }}</div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="left-panel">
                <!-- 进度监控 -->
                <div class="section">
                    <h2>🚀 执行进度</h2>
                    <div class="progress-container">
                        <div class="progress-steps">
                            <div class="progress-line" style="width: {{ (task.progress.current_phase - 1) * 33.33 }}%"></div>
                            
                            <div class="step {% if task.progress.current_phase >= 1 %}active{% endif %} {% if task.progress.phase1_complete %}completed{% endif %}">
                                <div class="step-circle">1</div>
                                <div class="step-label">基础设施<br>准备</div>
                            </div>
                            
                            <div class="step {% if task.progress.current_phase >= 2 %}active{% endif %} {% if task.progress.phase2_complete %}completed{% endif %}">
                                <div class="step-circle">2</div>
                                <div class="step-label">敢死队<br>试探</div>
                            </div>
                            
                            <div class="step {% if task.progress.current_phase >= 3 %}active{% endif %} {% if task.progress.phase3_complete %}completed{% endif %}">
                                <div class="step-circle">3</div>
                                <div class="step-label">知识库<br>分析</div>
                            </div>
                            
                            <div class="step {% if task.progress.current_phase >= 4 %}active{% endif %} {% if task.progress.phase4_complete %}completed{% endif %}">
                                <div class="step-circle">4</div>
                                <div class="step-label">大规模<br>自动化</div>
                            </div>
                        </div>
                    </div>
                    
                    {% if task.error_message %}
                    <div class="error-message">
                        <strong>错误信息：</strong>{{ task.error_message }}
                    </div>
                    {% endif %}
                </div>
                
                <!-- 数字人分配 -->
                <div class="section">
                    <h2>👥 数字人分配</h2>
                    <div class="assignment-tabs">
                        <button class="tab-button active" onclick="switchTab('scout')">
                            🔍 敢死队 ({{ task.scout_assignments|length }})
                        </button>
                        <button class="tab-button" onclick="switchTab('target')">
                            🎯 大部队 ({{ task.target_assignments|length }})
                        </button>
                    </div>
                    
                    <div id="scout-tab" class="tab-content active">
                        <div class="assignment-list">
                            {% for assignment in task.scout_assignments %}
                            <div class="assignment-item">
                                <div class="assignment-header">
                                    <div class="assignment-name">{{ assignment.persona_name }}</div>
                                    <div class="assignment-status status-{{ assignment.status|lower|replace('成功', 'success')|replace('失败', 'failed')|replace('准备就绪', 'ready')|replace('执行中', 'running') }}">
                                        {{ assignment.status }}
                                    </div>
                                </div>
                                <div class="assignment-details">
                                    <div class="detail-item">
                                        <div class="detail-label">人物ID</div>
                                        <div class="detail-value">{{ assignment.persona_id }}</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="detail-label">浏览器配置</div>
                                        <div class="detail-value">{{ assignment.browser_profile }}</div>
                                    </div>
                                    {% if assignment.answers_count is defined %}
                                    <div class="detail-item">
                                        <div class="detail-label">答题数量</div>
                                        <div class="detail-value">{{ assignment.answers_count }}题</div>
                                    </div>
                                    {% endif %}
                                </div>
                                {% if assignment.error_message %}
                                <div style="margin-top: 10px; color: #dc3545; font-size: 0.9em;">
                                    错误: {{ assignment.error_message }}
                                </div>
                                {% endif %}
                            </div>
                            {% endfor %}
                            {% if not task.scout_assignments %}
                            <div style="text-align: center; color: #666; padding: 20px;">
                                敢死队尚未分配
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div id="target-tab" class="tab-content">
                        <div class="assignment-list">
                            {% for assignment in task.target_assignments %}
                            <div class="assignment-item">
                                <div class="assignment-header">
                                    <div class="assignment-name">{{ assignment.persona_name }}</div>
                                    <div class="assignment-status status-{{ assignment.status|lower|replace('成功', 'success')|replace('失败', 'failed')|replace('已分配', 'ready')|replace('执行中', 'running') }}">
                                        {{ assignment.status }}
                                    </div>
                                </div>
                                <div class="assignment-details">
                                    <div class="detail-item">
                                        <div class="detail-label">匹配度</div>
                                        <div class="detail-value">{{ assignment.match_score }}%</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="detail-label">预测成功率</div>
                                        <div class="detail-value">{{ assignment.predicted_success_rate }}%</div>
                                    </div>
                                    {% if assignment.answers_count is defined %}
                                    <div class="detail-item">
                                        <div class="detail-label">答题数量</div>
                                        <div class="detail-value">{{ assignment.answers_count }}题</div>
                                    </div>
                                    {% endif %}
                                </div>
                                {% if assignment.match_reasons %}
                                <div style="margin-top: 10px; font-size: 0.9em; color: #666;">
                                    匹配原因: {{ assignment.match_reasons|join(', ') }}
                                </div>
                                {% endif %}
                                {% if assignment.error_message %}
                                <div style="margin-top: 10px; color: #dc3545; font-size: 0.9em;">
                                    错误: {{ assignment.error_message }}
                                </div>
                                {% endif %}
                            </div>
                            {% endfor %}
                            {% if not task.target_assignments %}
                            <div style="text-align: center; color: #666; padding: 20px;">
                                大部队尚未分配
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="right-panel">
                <!-- 资源消耗 -->
                <div class="cost-panel">
                    <div class="cost-header">
                        <div class="cost-total">¥{{ "%.4f"|format(task.resource_consumption.total_cost) }}</div>
                        <div class="cost-label">当前任务消耗</div>
                    </div>
                    <div class="cost-breakdown">
                        {% for resource in task.resource_consumption.resources %}
                        <div class="cost-item">
                            <div class="cost-item-name">
                                {% if resource.type == 'adspower_browser' %}
                                    🌐 AdsPower
                                {% elif resource.type == 'qinguo_proxy' %}
                                    🔗 青果代理
                                {% elif resource.type == 'xiaoshe_query' %}
                                    👥 小社会查询
                                {% else %}
                                    🔧 {{ resource.type }}
                                {% endif %}
                            </div>
                            <div class="cost-item-value">¥{{ "%.4f"|format(resource.cost) }}</div>
                        </div>
                        {% endfor %}
                        {% if not task.resource_consumption.resources %}
                        <div style="text-align: center; color: #666; padding: 10px;">
                            暂无资源消耗记录
                        </div>
                        {% endif %}
                    </div>
                </div>
                
                <!-- 成本优化建议 -->
                {% if task.optimization_plan %}
                <div class="optimization-panel">
                    <h3>💡 成本优化建议</h3>
                    <ul class="optimization-tips">
                        {% for tip in task.optimization_plan.optimization_tips %}
                        <li>{{ tip }}</li>
                        {% endfor %}
                    </ul>
                </div>
                {% endif %}
                
                <!-- 执行结果 -->
                <div class="section">
                    <h2>📈 执行结果</h2>
                    <div class="results-summary">
                        <div class="result-card">
                            <div class="result-number success">{{ task.results.success_count }}</div>
                            <div class="result-label">成功</div>
                        </div>
                        <div class="result-card">
                            <div class="result-number danger">{{ task.results.failure_count }}</div>
                            <div class="result-label">失败</div>
                        </div>
                        <div class="result-card">
                            <div class="result-number info">{{ task.results.total_answers }}</div>
                            <div class="result-label">总答题</div>
                        </div>
                        {% if task.results.success_count + task.results.failure_count > 0 %}
                        <div class="result-card">
                            <div class="result-number warning">{{ "%.1f"|format((task.results.success_count / (task.results.success_count + task.results.failure_count) * 100)) }}%</div>
                            <div class="result-label">成功率</div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                
                <!-- 控制按钮 -->
                <div class="controls">
                    <button class="btn btn-primary" onclick="refreshTask()">
                        🔄 刷新状态
                    </button>
                    <a href="/" class="btn btn-secondary">
                        🏠 返回主页
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        function switchTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 移除所有按钮的活跃状态
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 显示选中的标签内容
            document.getElementById(tabName + '-tab').classList.add('active');
            
            // 激活对应的按钮
            event.target.classList.add('active');
        }
        
        async function refreshTask() {
            try {
                const taskId = '{{ task.task_id }}';
                const response = await fetch(`/refresh_task/${taskId}`);
                const result = await response.json();
                
                if (result.success) {
                    // 刷新页面以显示最新状态
                    location.reload();
                } else {
                    alert('刷新失败: ' + result.error);
                }
            } catch (error) {
                alert('网络错误: ' + error.message);
            }
        }
        
        // 自动刷新（每30秒）
        setInterval(refreshTask, 30000);
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 如果任务已完成，停止自动刷新
            const taskStatus = '{{ task.status }}';
            if (taskStatus === 'completed' || taskStatus === 'failed') {
                // 可以在这里添加完成状态的特殊处理
                console.log('任务已完成，状态:', taskStatus);
            }
        });
    </script>
</body>
</html> 