# 🔥 统一资源生命周期管理器
# 解决所有资源管理不统一、清理触发点不一致、AdsPower两步清理未完全统一的问题

import asyncio
import logging
import time
import weakref
import aiohttp
from typing import Dict, Any, Optional, List, Set
from contextlib import asynccontextmanager
from dataclasses import dataclass, field
from enum import Enum

logger = logging.getLogger(__name__)

class ResourceType(Enum):
    """资源类型枚举"""
    BROWSER_CONTEXT = "browser_context"
    BROWSER_INSTANCE = "browser_instance"
    ADSPOWER_PROFILE = "adspower_profile"
    HTTP_SESSION = "http_session"
    ASYNC_TASK = "async_task"
    WEBUI_COMPONENT = "webui_component"
    AGENT_INSTANCE = "agent_instance"

class ResourceState(Enum):
    """资源状态枚举"""
    CREATED = "created"
    ACTIVE = "active"
    CLEANING = "cleaning"
    CLEANED = "cleaned"
    ERROR = "error"

@dataclass
class ResourceInfo:
    """资源信息数据类"""
    resource_id: str
    resource_type: ResourceType
    resource_object: Any
    state: ResourceState = ResourceState.CREATED
    created_at: float = field(default_factory=time.time)
    parent_id: Optional[str] = None
    children_ids: Set[str] = field(default_factory=set)
    cleanup_callbacks: List[callable] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

class UnifiedResourceLifecycleManager:
    """🔥 统一资源生命周期管理器 - 解决所有资源管理问题"""
    
    _instance = None
    _lock = asyncio.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        # 🔧 统一资源注册表
        self.resources: Dict[str, ResourceInfo] = {}
        self.resource_locks: Dict[str, asyncio.Lock] = {}
        
        # 🔧 弱引用管理（防止内存泄漏）
        self.weak_references = weakref.WeakValueDictionary()
        
        # 🔧 异步任务管理
        self.active_tasks: Set[asyncio.Task] = set()
        self.task_cleanup_callbacks: Dict[str, callable] = {}
        
        # 🔧 HTTP连接池管理
        self.http_sessions: Dict[str, aiohttp.ClientSession] = {}
        self.session_semaphore = asyncio.Semaphore(10)  # 限制并发连接数
        
        # 🔧 AdsPower专用管理
        self.adspower_profiles: Dict[str, Dict[str, Any]] = {}
        self.adspower_cleanup_queue: asyncio.Queue = asyncio.Queue()
        
        # 🔧 清理策略配置
        self.cleanup_strategies = {
            ResourceType.BROWSER_CONTEXT: self._cleanup_browser_context,
            ResourceType.BROWSER_INSTANCE: self._cleanup_browser_instance,
            ResourceType.ADSPOWER_PROFILE: self._cleanup_adspower_profile,
            ResourceType.HTTP_SESSION: self._cleanup_http_session,
            ResourceType.ASYNC_TASK: self._cleanup_async_task,
            ResourceType.WEBUI_COMPONENT: self._cleanup_webui_component,
            ResourceType.AGENT_INSTANCE: self._cleanup_agent_instance,
        }
        
        # 🔧 状态监控
        self.monitoring_active = False
        self.monitoring_task: Optional[asyncio.Task] = None
        
        self._initialized = True
        logger.info("🔥 统一资源生命周期管理器已初始化")
    
    async def register_resource(
        self, 
        resource_id: str, 
        resource_type: ResourceType, 
        resource_object: Any,
        parent_id: Optional[str] = None,
        cleanup_callbacks: List[callable] = None,
        metadata: Dict[str, Any] = None
    ) -> str:
        """🔧 注册资源到统一管理器"""
        try:
            if resource_id not in self.resource_locks:
                self.resource_locks[resource_id] = asyncio.Lock()
            
            async with self.resource_locks[resource_id]:
                resource_info = ResourceInfo(
                    resource_id=resource_id,
                    resource_type=resource_type,
                    resource_object=resource_object,
                    state=ResourceState.CREATED,
                    parent_id=parent_id,
                    cleanup_callbacks=cleanup_callbacks or [],
                    metadata=metadata or {}
                )
                
                self.resources[resource_id] = resource_info
                
                # 建立父子关系
                if parent_id and parent_id in self.resources:
                    self.resources[parent_id].children_ids.add(resource_id)
                
                # 弱引用管理
                try:
                    self.weak_references[resource_id] = resource_object
                except TypeError:
                    # 某些对象不支持弱引用，跳过
                    pass
                
                # 特殊类型处理
                if resource_type == ResourceType.ADSPOWER_PROFILE:
                    self.adspower_profiles[resource_id] = metadata or {}
                
                resource_info.state = ResourceState.ACTIVE
                logger.info(f"✅ 资源已注册: {resource_id} ({resource_type.value})")
                return resource_id
                
        except Exception as e:
            logger.error(f"❌ 资源注册失败: {resource_id} - {e}")
            raise
    
    @asynccontextmanager
    async def managed_http_session(self, session_id: str = None, **kwargs):
        """🔧 管理HTTP会话的上下文管理器"""
        session_id = session_id or f"http_session_{int(time.time())}"
        session = None
        
        try:
            async with self.session_semaphore:
                session = aiohttp.ClientSession(**kwargs)
                await self.register_resource(
                    session_id, 
                    ResourceType.HTTP_SESSION, 
                    session,
                    metadata={"created_by": "managed_http_session"}
                )
                
                logger.info(f"✅ HTTP会话已创建: {session_id}")
                yield session
                
        except Exception as e:
            logger.error(f"❌ HTTP会话异常: {session_id} - {e}")
            raise
        finally:
            if session:
                await self._cleanup_resource(session_id)
    
    async def register_async_task(self, task: asyncio.Task, task_id: str = None) -> str:
        """🔧 注册异步任务"""
        task_id = task_id or f"task_{id(task)}"
        
        await self.register_resource(
            task_id,
            ResourceType.ASYNC_TASK,
            task,
            metadata={"task_name": getattr(task, '_name', 'unknown')}
        )
        
        self.active_tasks.add(task)
        
        # 添加任务完成回调
        def task_done_callback(finished_task):
            self.active_tasks.discard(finished_task)
            asyncio.create_task(self._cleanup_resource(task_id))
        
        task.add_done_callback(task_done_callback)
        return task_id
    
    async def register_adspower_profile(
        self, 
        profile_id: str, 
        debug_port: int, 
        persona_name: str = "未知",
        metadata: Dict[str, Any] = None
    ) -> str:
        """🔧 注册AdsPower配置文件"""
        resource_id = f"adspower_{profile_id}"
        
        profile_metadata = {
            "profile_id": profile_id,
            "debug_port": debug_port,
            "persona_name": persona_name,
            "adspower_host": "http://local.adspower.net:50325",
            **(metadata or {})
        }
        
        # 创建资源信息
        resource_info = ResourceInfo(
            resource_id=resource_id,
            resource_type=ResourceType.ADSPOWER_PROFILE,
            resource_object={"profile_id": profile_id, "debug_port": debug_port},
            state=ResourceState.ACTIVE,
            metadata=profile_metadata
        )
        
        self.resources[resource_id] = resource_info
        self.adspower_profiles[resource_id] = profile_metadata
        
        logger.info(f"✅ AdsPower配置文件已注册: {profile_id}")
        return resource_id
    
    async def cleanup_resource(self, resource_id: str, force: bool = False) -> bool:
        """🔧 清理指定资源"""
        return await self._cleanup_resource(resource_id, force)
    
    async def _cleanup_resource(self, resource_id: str, force: bool = False) -> bool:
        """🔧 内部资源清理方法"""
        if resource_id not in self.resources:
            logger.warning(f"⚠️ 资源不存在: {resource_id}")
            return False
        
        resource_info = self.resources[resource_id]
        
        if resource_info.state == ResourceState.CLEANING and not force:
            logger.info(f"⏳ 资源正在清理中: {resource_id}")
            return False
        
        if resource_info.state == ResourceState.CLEANED and not force:
            logger.info(f"✅ 资源已清理: {resource_id}")
            return True
        
        try:
            async with self.resource_locks.get(resource_id, asyncio.Lock()):
                resource_info.state = ResourceState.CLEANING
                logger.info(f"🧹 开始清理资源: {resource_id} ({resource_info.resource_type.value})")
                
                # 先清理子资源
                for child_id in list(resource_info.children_ids):
                    await self._cleanup_resource(child_id, force)
                
                # 执行自定义清理回调
                for callback in resource_info.cleanup_callbacks:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            await callback(resource_info.resource_object)
                        else:
                            callback(resource_info.resource_object)
                    except Exception as e:
                        logger.warning(f"⚠️ 清理回调失败: {e}")
                
                # 执行类型特定清理
                cleanup_strategy = self.cleanup_strategies.get(resource_info.resource_type)
                if cleanup_strategy:
                    success = await cleanup_strategy(resource_info)
                    if not success:
                        resource_info.state = ResourceState.ERROR
                        return False
                
                # 清理完成
                resource_info.state = ResourceState.CLEANED
                
                # 从父资源中移除
                if resource_info.parent_id and resource_info.parent_id in self.resources:
                    self.resources[resource_info.parent_id].children_ids.discard(resource_id)
                
                logger.info(f"✅ 资源清理完成: {resource_id}")
                return True
                
        except Exception as e:
            logger.error(f"❌ 资源清理失败: {resource_id} - {e}")
            if resource_id in self.resources:
                self.resources[resource_id].state = ResourceState.ERROR
            return False
    
    # 🔧 类型特定清理策略
    async def _cleanup_browser_context(self, resource_info: ResourceInfo) -> bool:
        """清理浏览器上下文"""
        try:
            browser_context = resource_info.resource_object
            if hasattr(browser_context, 'close'):
                await browser_context.close()
            return True
        except Exception as e:
            logger.error(f"❌ 浏览器上下文清理失败: {e}")
            return False
    
    async def _cleanup_browser_instance(self, resource_info: ResourceInfo) -> bool:
        """清理浏览器实例"""
        try:
            browser = resource_info.resource_object
            if hasattr(browser, 'close'):
                await browser.close()
            return True
        except Exception as e:
            logger.error(f"❌ 浏览器实例清理失败: {e}")
            return False
    
    async def _cleanup_adspower_profile(self, resource_info: ResourceInfo) -> bool:
        """🔥 AdsPower配置文件两步清理（关键修复）"""
        if resource_info.resource_type != ResourceType.ADSPOWER_PROFILE:
            logger.warning(f"⚠️ 资源类型不匹配: {resource_info.resource_type.value}")
            return False
        
        try:
            metadata = resource_info.metadata
            profile_id = metadata.get("profile_id")
            adspower_host = metadata.get("adspower_host", "http://local.adspower.net:50325")
            
            if not profile_id:
                logger.error("❌ AdsPower配置文件ID缺失")
                return False
            
            # 🔥 第一步：停止浏览器实例
            stop_success = await self._adspower_stop_browser(profile_id, adspower_host)
            if stop_success:
                logger.info(f"✅ AdsPower浏览器已停止: {profile_id}")
            else:
                logger.warning(f"⚠️ AdsPower浏览器停止失败，继续删除配置文件")
            
            # 🔥 第二步：删除配置文件（关键步骤）
            delete_success = await self._adspower_delete_profile(profile_id, adspower_host)
            if delete_success:
                logger.info(f"✅ AdsPower配置文件已删除: {profile_id}")
                resource_info.state = ResourceState.CLEANED
                return True
            else:
                logger.error(f"❌ AdsPower配置文件删除失败: {profile_id}")
                resource_info.state = ResourceState.ERROR
                return False
                
        except Exception as e:
            logger.error(f"❌ AdsPower配置文件清理失败: {e}")
            resource_info.state = ResourceState.ERROR
            return False
    
    async def _adspower_stop_browser(self, profile_id: str, adspower_host: str) -> bool:
        """AdsPower停止浏览器"""
        try:
            async with self.managed_http_session() as session:
                url = f"{adspower_host}/api/v1/browser/stop"
                data = {"user_id": profile_id}
                
                async with session.post(url, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result.get("code") == 0
                    return False
        except Exception as e:
            logger.error(f"❌ AdsPower停止浏览器失败: {e}")
            return False
    
    async def _adspower_delete_profile(self, profile_id: str, adspower_host: str) -> bool:
        """AdsPower删除配置文件"""
        try:
            async with self.managed_http_session() as session:
                url = f"{adspower_host}/api/v1/user/delete"
                data = {"user_ids": [profile_id]}
                
                async with session.post(url, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result.get("code") == 0
                    return False
        except Exception as e:
            logger.error(f"❌ AdsPower删除配置文件失败: {e}")
            return False
    
    async def _cleanup_http_session(self, resource_info: ResourceInfo) -> bool:
        """清理HTTP会话"""
        try:
            session = resource_info.resource_object
            if hasattr(session, 'close'):
                await session.close()
            return True
        except Exception as e:
            logger.error(f"❌ HTTP会话清理失败: {e}")
            return False
    
    async def _cleanup_async_task(self, resource_info: ResourceInfo) -> bool:
        """清理异步任务"""
        try:
            task = resource_info.resource_object
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
            self.active_tasks.discard(task)
            return True
        except Exception as e:
            logger.error(f"❌ 异步任务清理失败: {e}")
            return False
    
    async def _cleanup_webui_component(self, resource_info: ResourceInfo) -> bool:
        """清理WebUI组件"""
        try:
            component = resource_info.resource_object
            # WebUI组件通常不需要特殊清理
            return True
        except Exception as e:
            logger.error(f"❌ WebUI组件清理失败: {e}")
            return False
    
    async def _cleanup_agent_instance(self, resource_info: ResourceInfo) -> bool:
        """清理Agent实例"""
        try:
            agent = resource_info.resource_object
            if hasattr(agent, 'close'):
                await agent.close()
            elif hasattr(agent, 'cleanup'):
                await agent.cleanup()
            return True
        except Exception as e:
            logger.error(f"❌ Agent实例清理失败: {e}")
            return False
    
    async def cleanup_all_resources(self, resource_type: Optional[ResourceType] = None) -> Dict[str, bool]:
        """🔧 清理所有资源"""
        logger.info("🧹 开始清理所有资源...")
        
        cleanup_results = {}
        resources_to_cleanup = []
        
        # 筛选需要清理的资源
        for resource_id, resource_info in self.resources.items():
            if resource_type is None or resource_info.resource_type == resource_type:
                if resource_info.state in [ResourceState.ACTIVE, ResourceState.ERROR]:
                    resources_to_cleanup.append(resource_id)
        
        # 按依赖关系排序（子资源优先）
        resources_to_cleanup.sort(key=lambda rid: len(self.resources[rid].children_ids))
        
        # 并发清理
        cleanup_tasks = []
        for resource_id in resources_to_cleanup:
            task = asyncio.create_task(self._cleanup_resource(resource_id))
            cleanup_tasks.append((resource_id, task))
        
        # 等待所有清理完成
        for resource_id, task in cleanup_tasks:
            try:
                success = await task
                cleanup_results[resource_id] = success
            except Exception as e:
                logger.error(f"❌ 资源清理异常: {resource_id} - {e}")
                cleanup_results[resource_id] = False
        
        success_count = sum(cleanup_results.values())
        total_count = len(cleanup_results)
        
        logger.info(f"✅ 资源清理完成: {success_count}/{total_count}")
        return cleanup_results
    
    async def get_resource_status(self) -> Dict[str, Any]:
        """🔧 获取资源状态报告"""
        status = {
            "total_resources": len(self.resources),
            "active_resources": len([r for r in self.resources.values() if r.state == ResourceState.ACTIVE]),
            "cleaning_resources": len([r for r in self.resources.values() if r.state == ResourceState.CLEANING]),
            "cleaned_resources": len([r for r in self.resources.values() if r.state == ResourceState.CLEANED]),
            "error_resources": len([r for r in self.resources.values() if r.state == ResourceState.ERROR]),
            "active_tasks": len(self.active_tasks),
            "http_sessions": len(self.http_sessions),
            "adspower_profiles": len(self.adspower_profiles),
            "by_type": {}
        }
        
        # 按类型统计
        for resource_type in ResourceType:
            type_resources = [r for r in self.resources.values() if r.resource_type == resource_type]
            status["by_type"][resource_type.value] = {
                "total": len(type_resources),
                "active": len([r for r in type_resources if r.state == ResourceState.ACTIVE]),
                "cleaned": len([r for r in type_resources if r.state == ResourceState.CLEANED]),
                "error": len([r for r in type_resources if r.state == ResourceState.ERROR])
            }
        
        return status
    
    async def start_monitoring(self):
        """🔧 启动资源监控"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitoring_task = asyncio.create_task(self._monitor_resources())
        await self.register_async_task(self.monitoring_task, "resource_monitor")
        logger.info("🔧 资源监控已启动")
    
    async def _monitor_resources(self):
        """🔧 资源监控循环"""
        while self.monitoring_active:
            try:
                # 检查死亡的弱引用
                dead_references = []
                for resource_id, weak_ref in list(self.weak_references.items()):
                    if weak_ref is None:  # 对象已被垃圾回收
                        dead_references.append(resource_id)
                
                # 清理死亡引用对应的资源
                for resource_id in dead_references:
                    if resource_id in self.resources:
                        logger.info(f"🔍 检测到资源对象已被垃圾回收，清理资源记录: {resource_id}")
                        await self._cleanup_resource(resource_id)
                
                # 检查异常状态的资源
                error_resources = [
                    resource_id for resource_id, resource_info in self.resources.items()
                    if resource_info.state == ResourceState.ERROR
                ]
                
                if error_resources:
                    logger.warning(f"⚠️ 发现异常状态资源: {len(error_resources)} 个")
                
                # 定期状态报告
                if int(time.time()) % 300 == 0:  # 每5分钟
                    status = await self.get_resource_status()
                    logger.info(f"📊 资源状态报告: {status}")
                
                await asyncio.sleep(30)  # 30秒检查一次
                
            except Exception as e:
                logger.error(f"❌ 资源监控异常: {e}")
                await asyncio.sleep(60)  # 异常时等待更长时间
    
    async def stop_monitoring(self):
        """🔧 停止资源监控"""
        self.monitoring_active = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        logger.info("🔧 资源监控已停止")
    
    async def emergency_cleanup(self):
        """🚨 紧急清理所有资源"""
        logger.warning("🚨 执行紧急资源清理...")
        
        # 停止监控
        await self.stop_monitoring()
        
        # 取消所有活跃任务
        for task in list(self.active_tasks):
            if not task.done():
                task.cancel()
        
        # 清理所有资源
        await self.cleanup_all_resources()
        
        # 清理内部状态
        self.resources.clear()
        self.resource_locks.clear()
        self.weak_references.clear()
        self.active_tasks.clear()
        self.http_sessions.clear()
        self.adspower_profiles.clear()
        
        logger.warning("🚨 紧急清理完成")

# 🔥 全局单例实例
unified_resource_manager = UnifiedResourceLifecycleManager()

# 🔧 便捷函数
async def register_resource(resource_id: str, resource_type: ResourceType, resource_object: Any, **kwargs):
    """便捷的资源注册函数"""
    return await unified_resource_manager.register_resource(resource_id, resource_type, resource_object, **kwargs)

async def cleanup_resource(resource_id: str, force: bool = False):
    """便捷的资源清理函数"""
    return await unified_resource_manager.cleanup_resource(resource_id, force)

async def cleanup_all_resources(resource_type: Optional[ResourceType] = None):
    """便捷的全部资源清理函数"""
    return await unified_resource_manager.cleanup_all_resources(resource_type)

async def get_resource_status():
    """便捷的资源状态查询函数"""
    return await unified_resource_manager.get_resource_status()

@asynccontextmanager
async def managed_http_session(session_id: str = None, **kwargs):
    """便捷的HTTP会话管理函数"""
    async with unified_resource_manager.managed_http_session(session_id, **kwargs) as session:
        yield session 