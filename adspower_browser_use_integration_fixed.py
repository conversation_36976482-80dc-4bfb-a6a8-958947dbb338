#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔥 AdsPower浏览器与BrowserUse智能集成系统 - 修复版本
修复了WebSocket连接问题，确保使用正确的Chrome DevTools Protocol端点
"""

import asyncio
import json
import logging
import sys
import time
import requests
from typing import Dict, Any, Optional

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('adspower_integration_fixed.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# 尝试导入browser_use，如果失败则提供兼容性处理
try:
    from browser_use import Agent, Browser, BrowserConfig
    browser_use_available = True
    logger.info("✅ browser_use 导入成功")
except ImportError as e:
    logger.warning(f"⚠️ browser_use 未安装，使用兼容性模式: {e}")
    browser_use_available = False
    class Agent: pass
    class Browser: pass

# 尝试导入langchain_openai，如果失败则提供兼容性处理
try:
    from langchain_openai import ChatOpenAI
    langchain_available = True
    logger.info("✅ langchain_openai 导入成功")
except ImportError as e:
    logger.warning(f"⚠️ langchain_openai 未安装，使用兼容性模式: {e}")
    langchain_available = False
    class ChatOpenAI: pass

# 导入配置
try:
    from config import get_config
    config_available = True
except ImportError:
    logger.warning("⚠️ config.py 不可用，使用默认配置")
    config_available = False
    def get_config(name):
        return {
            'llm': {
                'model': 'gemini-2.0-flash',
                'api_key': 'AIzaSyAfmaTObVEiq6R_c62T4jeEpyf6yp4WCP8',
                'temperature': 0.5,
                'max_tokens': 4096
            }
        }.get(name, {})

class FixedAdsPowerBrowserUseIntegration:
    """🔥 修复版AdsPower与BrowserUse五层融合集成系统"""
    
    def __init__(self, profile_id: str, adspower_host: str = "http://local.adspower.net:50325"):
        self.profile_id = profile_id
        self.adspower_host = adspower_host
        self.browser = None
        self.agent = None
        self.session_start_time = time.time()
        self.debug_port = None
        self.websocket_url = None
        
        # 🎯 五层架构配置
        self.five_layer_config = {
            'intelligent_stop_decision': True,
            'answer_consistency_guarantee': True, 
            'resource_management': True,
            'never_give_up_execution': True,
            'webui_native_integration': True
        }
        
        logger.info("🔥 修复版AdsPower五层融合集成系统初始化")
    
    async def start_browser_session(self) -> Optional[str]:
        """🚀 启动AdsPower浏览器会话并获取正确的WebSocket URL"""
        try:
            logger.info(f"🚀 启动AdsPower浏览器配置文件: {self.profile_id}")
            
            # 调用AdsPower启动API
            start_url = f"{self.adspower_host}/api/v1/browser/start"
            start_data = {
                "user_id": self.profile_id,
                "open_tabs": 1,
                "launch_args": [
                    "--disable-blink-features=AutomationControlled",
                    "--disable-dev-shm-usage",
                    "--no-sandbox",
                    "--disable-gpu",
                    "--remote-debugging-port=0"
                ]
            }
            
            response = requests.post(start_url, json=start_data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    data = result.get("data", {})
                    debug_port = data.get("debug_port")
                    
                    if debug_port:
                        self.debug_port = debug_port
                        logger.info(f"✅ AdsPower浏览器启动成功")
                        logger.info(f"🔗 Debug端口: {debug_port}")
                        
                        # 🔑 关键修复：获取正确的WebSocket URL
                        correct_ws_url = await self._get_correct_websocket_url(debug_port)
                        if correct_ws_url:
                            self.websocket_url = correct_ws_url
                            logger.info(f"✅ 获取到正确的WebSocket URL: {correct_ws_url}")
                            return correct_ws_url
                        else:
                            logger.error("❌ 无法获取正确的WebSocket URL")
                            return None
                    else:
                        logger.error("❌ 未获取到Debug端口")
                        return None
                else:
                    logger.error(f"❌ AdsPower启动失败: {result.get('msg', '未知错误')}")
                    return None
            else:
                logger.error(f"❌ AdsPower API请求失败: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 启动AdsPower浏览器失败: {e}")
            return None
    
    async def _get_correct_websocket_url(self, debug_port: str, max_wait_time: int = 30) -> Optional[str]:
        """🔑 获取正确的Chrome DevTools Protocol WebSocket URL"""
        logger.info(f"🔍 获取正确的WebSocket URL，端口: {debug_port}")
        
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            try:
                # 检查Chrome DevTools Protocol端点
                response = requests.get(f"http://127.0.0.1:{debug_port}/json", timeout=5)
                
                if response.status_code == 200:
                    pages = response.json()
                    
                    if pages and len(pages) > 0:
                        # 寻找可用的页面（优先选择about:blank或主页面）
                        target_page = None
                        
                        # 优先选择about:blank页面
                        for page in pages:
                            if page.get('url') == 'about:blank' and 'webSocketDebuggerUrl' in page:
                                target_page = page
                                break
                        
                        # 如果没有about:blank，选择第一个有WebSocket URL的页面
                        if not target_page:
                            for page in pages:
                                if 'webSocketDebuggerUrl' in page and page.get('type') == 'page':
                                    target_page = page
                                    break
                        
                        # 最后选择任何有WebSocket URL的页面
                        if not target_page and pages:
                            for page in pages:
                                if 'webSocketDebuggerUrl' in page:
                                    target_page = page
                                    break
                        
                        if target_page:
                            ws_url = target_page.get('webSocketDebuggerUrl')
                            page_title = target_page.get('title', 'Unknown')
                            page_url = target_page.get('url', 'Unknown')
                            
                            logger.info(f"✅ 找到目标页面: {page_title}")
                            logger.info(f"   页面URL: {page_url}")
                            logger.info(f"   WebSocket URL: {ws_url}")
                            
                            return ws_url
                    
                logger.debug(f"等待页面准备就绪... ({int(time.time() - start_time)}s)")
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.debug(f"获取WebSocket URL时的异常: {e}")
                await asyncio.sleep(1)
        
        logger.warning(f"⚠️ 在{max_wait_time}秒内未获取到有效的WebSocket URL")
        return None
    
    async def initialize_browser(self, ws_endpoint: str) -> bool:
        """🔧 使用正确的WebSocket URL初始化浏览器连接"""
        try:
            logger.info("🔧 初始化浏览器连接...")
            logger.info(f"🔗 WebSocket端点: {ws_endpoint}")
            
            if not browser_use_available:
                logger.error("❌ browser_use 不可用")
                return False
            
            # 导入BrowserConfig
            from browser_use import BrowserConfig
            
            # 🔑 关键修复：直接使用完整的WebSocket URL
            browser_config = BrowserConfig(
                cdp_url=ws_endpoint,  # 直接使用完整的WebSocket URL
                headless=False,
                disable_security=True,
                extra_browser_args=[
                    "--disable-blink-features=AutomationControlled",
                    "--disable-dev-shm-usage",
                    "--no-sandbox",
                    "--disable-notifications",
                    "--disable-infobars"
                ]
            )
            
            # 创建Browser实例
            self.browser = Browser(config=browser_config)
            
            logger.info("✅ 浏览器连接初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 浏览器初始化失败: {e}")
            return False
    
    async def create_intelligent_agent(self, digital_human_info: Dict[str, Any], 
                                     task_description: str, 
                                     llm_config: Optional[Dict[str, Any]] = None) -> bool:
        """🧠 创建五层融合智能Agent"""
        try:
            if not self.browser:
                logger.error("❌ 浏览器未初始化")
                return False
            
            # 🔥 获取LLM配置
            if not llm_config:
                llm_config = get_config('llm')
                logger.info(f"✅ 从配置文件获取LLM配置: {llm_config.get('model', '未知模型')}")
            
            # 🧠 构建五层融合系统提示词
            enhanced_system_prompt = self._build_five_layer_system_prompt(digital_human_info, task_description)
            
            # 🔥 创建LLM实例 - 根据模型类型选择
            llm = None
            model = llm_config.get('model', 'gpt-4')
            
            if 'gemini' in model.lower():
                # 使用Gemini模型
                try:
                    from langchain_google_genai import ChatGoogleGenerativeAI
                    llm = ChatGoogleGenerativeAI(
                        model=model,
                        google_api_key=llm_config.get('api_key'),
                        temperature=llm_config.get('temperature', 0.7),
                        max_output_tokens=llm_config.get('max_tokens', 4000)
                    )
                    logger.info(f"✅ 使用Gemini LLM: {model}")
                except ImportError:
                    logger.warning("⚠️ langchain_google_genai 不可用，尝试其他方案")
                    llm = None
            
            if llm is None and langchain_available:
                # 回退到OpenAI兼容配置
                try:
                    llm = ChatOpenAI(
                        model=model,
                        openai_api_key=llm_config.get('api_key'),
                        openai_api_base=llm_config.get('base_url'),
                        temperature=llm_config.get('temperature', 0.7),
                        max_tokens=llm_config.get('max_tokens', 4000)
                    )
                    logger.info(f"✅ 使用OpenAI兼容LLM: {model}")
                except Exception as e:
                    logger.warning(f"⚠️ OpenAI兼容LLM创建失败: {e}")
                    llm = None
            
            if llm is None:
                logger.warning("⚠️ 无法创建LLM，使用模拟模式")
                # 这里可以添加一个模拟LLM类
                from types import SimpleNamespace
                llm = SimpleNamespace()
                llm.invoke = lambda x: SimpleNamespace(content="模拟LLM响应")
                
            # 🔥 创建BrowserUseAgent实例（五层架构）
            try:
                from src.agent.browser_use.browser_use_agent import BrowserUseAgent
                
                self.agent = BrowserUseAgent(
                    task=enhanced_system_prompt,
                    llm=llm,
                    browser=self.browser,
                    use_vision=True,
                    save_conversation_path='questionnaire_conversation.json',
                    max_failures=20,  # 增加容错次数
                    # 注意：digital_human_info和task_description会通过enhanced_system_prompt传递
                )
                
                logger.info("✅ 五层融合智能Agent创建成功")
                return True
                
            except Exception as e:
                logger.error(f"❌ 创建BrowserUseAgent失败: {e}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 创建智能Agent失败: {e}")
            return False
    
    def _build_five_layer_system_prompt(self, digital_human_info: Dict[str, Any], task_description: str) -> str:
        """🧠 构建五层融合系统提示词"""
        
        persona_context = f"""
🎭 数字人身份信息：
- 姓名：{digital_human_info.get('name', '未知')}
- 年龄：{digital_human_info.get('age', '未知')}
- 性别：{digital_human_info.get('gender', '未知')}
- 职业：{digital_human_info.get('profession', '未知')}
- 收入：{digital_human_info.get('income', '未知')}
- 教育：{digital_human_info.get('education', '未知')}
- 地区：{digital_human_info.get('location', '未知')}
- 兴趣：{', '.join(digital_human_info.get('interests', []))}
- 性格：{digital_human_info.get('personality', '未知')}
"""
        
        five_layer_prompt = f"""
🔥 五层融合智能问卷系统 - 您是一个高级AI问卷填写助手

{persona_context}

🎯 核心任务：{task_description}

🔥 五层融合架构指令：

📍 第1层 - 智能停止决策引擎：
- 收集多维度证据：页面结构分析、文本内容识别、URL变化监控、用户界面状态检测
- 需要积累60+置信度点才能判断真正完成
- 严格禁止因技术问题提前停止，必须确保问卷真正提交成功
- 识别完成标志：感谢页面、提交确认、成功消息、重定向到结果页

📍 第2层 - 答题一致性保障系统：
- 基于数字人特征进行逻辑一致的答题
- 相同或相似问题必须保持答案一致性
- 根据年龄、职业、收入、地区等信息智能选择合适选项
- 建立问题-答案记忆库，确保前后一致

📍 第3层 - AdsPower资源管理：
- 实时监控浏览器状态，每5秒检查一次
- 检测手动关闭行为，自动清理资源
- 使用官方API确保资源正确释放
- 处理异常情况，确保环境清洁

📍 第4层 - 永不放弃执行引擎：
- 最大容错：500步执行，20次失败容忍
- 智能恢复策略：页面刷新、重新导航、元素重新定位
- 拦截done动作：当系统想要停止时，先进行完成度验证
- 持续执行直到真正完成

📍 第5层 - WebUI原生深度集成：
- 场景识别：国家选择页面/问卷页面/一般页面
- 直接LLM上下文注入，提供智能填写指导
- 多方式注入：消息管理器/系统提示/设置配置
- 原生智能填写，而非简单的浏览器自动化

⚠️ 重要约束：
1. 绝对不要因为技术问题（元素未找到、页面加载等）就停止任务
2. 如果遇到困难，使用恢复策略继续尝试
3. 只有在确认问卷真正提交成功后才能停止
4. 保持答题的逻辑一致性，符合数字人特征
5. 优先使用智能填写方式，减少明显的自动化痕迹

开始执行任务！
"""
        
        return five_layer_prompt
    
    async def run_intelligent_questionnaire(self, url: str, max_execution_time: int = 3600) -> Dict[str, Any]:
        """🔥 运行五层融合智能问卷系统"""
        
        if not self.agent:
            return {"success": False, "error": "Agent未初始化"}
        
        start_time = time.time()
        result = {
            "success": False,
            "execution_time": 0,
            "questionnaire_url": url,
            "answers_count": 0,
            "pages_visited": 0,
            "completion_status": "未开始",
            "error_message": ""
        }
        
        try:
            logger.info("🔥 启动五层融合智能问卷执行")
            logger.info(f"🎯 目标URL: {url}")
            
            # 构建任务指令
            task_instruction = f"访问问卷链接 {url} 并完成所有问题的填写和提交"
            
            # 执行任务 - 使用永不放弃模式
            logger.info("🚀 开始永不放弃执行模式...")
            
            # 运行Agent，使用高步数限制确保完成
            agent_result = await self.agent.run(max_steps=500)
            
            # 分析执行结果
            if agent_result and len(agent_result) > 0:
                last_step = agent_result[-1]
                
                result.update({
                    "success": True,
                    "execution_time": time.time() - start_time,
                    "answers_count": len([step for step in agent_result if 'input' in str(step).lower()]),
                    "pages_visited": len(set([step.url for step in agent_result if hasattr(step, 'url')])),
                    "completion_status": "五层融合智能问卷系统执行完成"
                })
                
                logger.info("🔥 五层融合智能问卷执行完成")
                logger.info(f"⏰ 执行时间: {result['execution_time']:.0f}秒")
                logger.info(f"📝 答题数量: {result['answers_count']}")
                logger.info(f"📄 页面数量: {result['pages_visited']}")
                logger.info(f"✅ 完成状态: {result['completion_status']}")
            else:
                result["error_message"] = "Agent执行未返回结果"
                logger.warning("⚠️ Agent执行未返回有效结果")
            
            return result
            
        except Exception as e:
            result["error_message"] = str(e)
            result["execution_time"] = time.time() - start_time
            logger.error(f"❌ 五层融合智能问卷执行异常: {e}")
            return result
    
    async def cleanup_resources(self):
        """🔧 清理所有资源"""
        try:
            logger.info("🔧 开始清理资源...")
            
            # 清理Agent资源
            if self.agent and hasattr(self.agent, 'resource_manager'):
                await self.agent.resource_manager.cleanup_all_resources()
            
            # 关闭浏览器
            if self.browser:
                await self.browser.close()
                self.browser = None
            
            # 调用AdsPower关闭API
            await self._close_adspower_browser()
            
            logger.info("✅ 资源清理完成")
            
        except Exception as e:
            logger.error(f"❌ 资源清理失败: {e}")
    
    async def _close_adspower_browser(self):
        """🔧 关闭AdsPower浏览器"""
        try:
            close_url = f"{self.adspower_host}/api/v1/browser/stop"
            close_data = {"user_id": self.profile_id}
            
            response = requests.post(close_url, json=close_data, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    logger.info("✅ AdsPower浏览器已关闭")
                else:
                    logger.warning(f"⚠️ AdsPower关闭响应: {result}")
            else:
                logger.warning(f"⚠️ AdsPower关闭请求失败: {response.status_code}")
                
        except Exception as e:
            logger.error(f"❌ 关闭AdsPower浏览器失败: {e}")

# 兼容性函数，保持与原始API的兼容性
class AdsPowerWebUIIntegration:
    """🔥 修复版AdsPower与WebUI集成类"""
    
    def __init__(self):
        """初始化修复版AdsPower WebUI集成"""
        self.integration_system = None
        logger.info("🔥 修复版AdsPowerWebUIIntegration 初始化完成")
    
    async def run_questionnaire(self, profile_id: str, url: str, digital_human_info: Dict[str, Any], 
                               llm_config: Dict[str, Any]) -> Dict[str, Any]:
        """运行问卷 - 使用修复版五层融合架构"""
        try:
            # 创建修复版五层融合集成系统
            self.integration_system = FixedAdsPowerBrowserUseIntegration(profile_id)
            
            # 启动浏览器会话并获取正确的WebSocket URL
            correct_ws_url = await self.integration_system.start_browser_session()
            if not correct_ws_url:
                return {"success": False, "error": "浏览器启动或WebSocket URL获取失败"}
            
            # 初始化浏览器（使用正确的WebSocket URL）
            browser_init = await self.integration_system.initialize_browser(correct_ws_url)
            if not browser_init:
                return {"success": False, "error": "浏览器初始化失败"}
            
            # 创建智能Agent
            agent_created = await self.integration_system.create_intelligent_agent(
                digital_human_info, 
                f"访问问卷链接 {url} 并完成填写", 
                llm_config
            )
            if not agent_created:
                return {"success": False, "error": "智能Agent创建失败"}
            
            # 运行智能问卷系统
            result = await self.integration_system.run_intelligent_questionnaire(url)
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 修复版问卷执行失败: {e}")
            return {"success": False, "error": str(e)}
        finally:
            if self.integration_system:
                await self.integration_system.cleanup_resources()

# 兼容性封装函数
async def run_complete_questionnaire_workflow_with_existing_browser(browser_env: Dict[str, Any], 
                                                                  questionnaire_url: str,
                                                                  digital_human_info: Dict[str, Any], 
                                                                  llm_config: Dict[str, Any]) -> Dict[str, Any]:
    """🔥 修复版 - 使用现有浏览器的问卷工作流"""
    try:
        profile_id = browser_env.get('profile_id')
        if not profile_id:
            return {"success": False, "error": "缺少profile_id"}
        
        # 创建修复版五层融合集成系统
        integration_system = FixedAdsPowerBrowserUseIntegration(profile_id)
        
        # 🔑 关键修复：不直接使用debug_port构造WebSocket URL，而是通过正确的方式获取
        debug_port = browser_env.get('debug_port')
        if debug_port:
            # 获取正确的WebSocket URL
            correct_ws_url = await integration_system._get_correct_websocket_url(str(debug_port))
            if not correct_ws_url:
                return {"success": False, "error": "无法获取正确的WebSocket URL"}
            
            # 初始化浏览器（使用正确的WebSocket URL）
            browser_init = await integration_system.initialize_browser(correct_ws_url)
            if not browser_init:
                return {"success": False, "error": "浏览器初始化失败"}
            
            # 创建智能Agent
            agent_created = await integration_system.create_intelligent_agent(
                digital_human_info, 
                f"访问问卷链接 {questionnaire_url} 并完成填写", 
                llm_config
            )
            if not agent_created:
                return {"success": False, "error": "智能Agent创建失败"}
            
            # 运行智能问卷系统
            result = await integration_system.run_intelligent_questionnaire(questionnaire_url)
            
            return result
        else:
            return {"success": False, "error": "缺少debug_port"}
            
    except Exception as e:
        logger.error(f"❌ 修复版现有浏览器问卷执行失败: {e}")
        return {"success": False, "error": str(e)}

async def run_five_layer_questionnaire_system():
    """🔥 修复版五层融合问卷系统入口"""
    logger.info("🔥 启动修复版五层融合智能问卷系统")
    
    # 使用修复版集成
    integration = AdsPowerWebUIIntegration()
    
    # 示例数字人信息
    digital_human = {
        'id': 1001,
        'name': '张小雅',
        'age': 28,
        'gender': '女',
        'profession': '产品经理',
        'income': '12000',
        'education': '本科',
        'location': '北京市',
        'interests': ['科技', '阅读', '健身'],
        'personality': '理性消费者'
    }
    
    # LLM配置
    llm_config = get_config('llm')
    
    # 问卷URL
    questionnaire_url = 'http://www.jinshengsurveys.com/?type=qtaskgoto&id=31393&token=EAE8C0EC7F66F828D47F753B487D435CA108172CDC98352371611CC8E8036C152943D234D38237BF4A524D0363900563576EC030D48CB7A13DFC735B9999C94D'
    
    # 执行问卷
    result = await integration.run_questionnaire('test_profile', questionnaire_url, digital_human, llm_config)
    
    logger.info(f"🎉 修复版五层融合系统执行完成: {result}")
    return result

if __name__ == "__main__":
    # 运行修复版系统
    asyncio.run(run_five_layer_questionnaire_system())