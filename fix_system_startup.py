#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
🔧 系统启动修复脚本
在系统启动前应用所有必要的修复，确保系统正常运行
"""

import logging
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

logger = logging.getLogger(__name__)

def apply_all_fixes():
    """应用所有系统修复"""
    
    logger.info("🔧 开始应用系统启动修复...")
    
    fixes_applied = []
    fixes_failed = []
    
    # 修复1: 创建智能下拉框引擎文件（如果不存在）
    try:
        if not os.path.exists('intelligent_dropdown_engine.py'):
            create_intelligent_dropdown_engine()
            fixes_applied.append("智能下拉框引擎文件创建")
        else:
            fixes_applied.append("智能下拉框引擎文件存在")
    except Exception as e:
        fixes_failed.append(f"智能下拉框引擎文件: {e}")
    
    # 修复2: 创建智能引擎集成补丁（如果不存在）
    try:
        if not os.path.exists('intelligent_engine_integration_patch.py'):
            create_intelligent_engine_patch()
            fixes_applied.append("智能引擎集成补丁创建")
        else:
            fixes_applied.append("智能引擎集成补丁存在")
    except Exception as e:
        fixes_failed.append(f"智能引擎集成补丁: {e}")
    
    # 修复3: 验证关键组件
    try:
        verify_critical_components()
        fixes_applied.append("关键组件验证通过")
    except Exception as e:
        fixes_failed.append(f"关键组件验证: {e}")
    
    # 输出修复结果
    logger.info(f"✅ 修复成功: {len(fixes_applied)} 项")
    for fix in fixes_applied:
        logger.info(f"  ✅ {fix}")
    
    if fixes_failed:
        logger.warning(f"⚠️ 修复失败: {len(fixes_failed)} 项")
        for fix in fixes_failed:
            logger.warning(f"  ❌ {fix}")
    
    logger.info("🎯 系统启动修复完成")
    return len(fixes_failed) == 0

def create_intelligent_dropdown_engine():
    """创建智能下拉框引擎文件"""
    
    content = '''"""
🎯 智能下拉框引擎 - 增强WebUI智能性
专门处理各种复杂下拉框选择场景
"""

import logging
from typing import Dict, List

logger = logging.getLogger(__name__)

class IntelligentDropdownEngine:
    """🎯 智能下拉框引擎"""
    
    def __init__(self):
        self.digital_human_info = {}
        
    def set_digital_human_info(self, digital_human_info: Dict):
        """设置数字人信息"""
        self.digital_human_info = digital_human_info
        
    def intelligent_option_matching(self, target_text: str) -> Dict:
        """🧠 智能选项匹配 - 基于数字人信息"""
        try:
            # 职业相关智能匹配
            if '会计' in target_text or '财务' in target_text:
                profession_matches = [
                    '会计/财务', '财务会计', '会计师', '财务经理',
                    'Accounting', 'Finance', 'Accountant',
                    '会计', '财务', '会计/审计', '财务/会计'
                ]
                return {
                    'type': 'profession',
                    'matches': profession_matches,
                    'confidence': 0.95
                }
            
            # 国籍相关智能匹配
            if any(keyword in target_text for keyword in ['中国', 'China', '国籍', 'Country']):
                country_matches = [
                    '中国', '中国大陆', '中华人民共和国', 'China', 'China Mainland',
                    '中国(简体中文)', 'China (Simplified Chinese)', '中国 China'
                ]
                
                return {
                    'type': 'nationality',
                    'matches': country_matches,
                    'confidence': 0.98,
                    'priority': 'HIGH'
                }
            
            return {'type': 'general', 'matches': [target_text], 'confidence': 0.7}
            
        except Exception as e:
            logger.error(f"❌ 智能选项匹配失败: {e}")
            return {'type': 'fallback', 'matches': [target_text], 'confidence': 0.5}
'''
    
    with open('intelligent_dropdown_engine.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    logger.info("✅ 智能下拉框引擎文件已创建")

def create_intelligent_engine_patch():
    """创建智能引擎集成补丁文件"""
    
    content = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
🔧 智能引擎集成补丁 - 解决系统架构问题
修复CustomController缺失的方法和组件
"""

import logging
import types
from typing import Dict

logger = logging.getLogger(__name__)

def apply_intelligent_engine_patch(controller):
    """应用智能引擎补丁"""
    try:
        logger.info("🔧 应用智能引擎补丁...")
        
        # 添加缺失的register_intelligent_dropdown_engine方法
        def register_intelligent_dropdown_engine(self):
            """🎯 注册智能下拉框引擎"""
            try:
                logger.info("🎯 注册智能下拉框引擎...")
                
                # 初始化引擎
                if not hasattr(self, 'dropdown_engine'):
                    try:
                        from intelligent_dropdown_engine import IntelligentDropdownEngine
                        self.dropdown_engine = IntelligentDropdownEngine()
                        if hasattr(self, 'digital_human_info'):
                            self.dropdown_engine.set_digital_human_info(self.digital_human_info)
                        logger.info("✅ 智能下拉框引擎初始化成功")
                    except ImportError:
                        logger.warning("⚠️ 智能下拉框引擎模块不可用")
                        self.dropdown_engine = None
                
                logger.info("✅ 智能下拉框引擎注册完成")
                
            except Exception as e:
                logger.error(f"❌ 智能下拉框引擎注册失败: {e}")
        
        # 绑定方法到controller
        controller.register_intelligent_dropdown_engine = types.MethodType(register_intelligent_dropdown_engine, controller)
        
        logger.info("✅ 智能引擎补丁应用成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ 智能引擎补丁应用失败: {e}")
        return False
'''
    
    with open('intelligent_engine_integration_patch.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    logger.info("✅ 智能引擎集成补丁文件已创建")

def verify_critical_components():
    """验证关键组件"""
    
    # 验证CustomController文件存在
    if not os.path.exists('src/controller/custom_controller.py'):
        raise Exception("CustomController文件不存在")
    
    # 验证AdsPower集成文件存在
    if not os.path.exists('adspower_browser_use_integration.py'):
        raise Exception("AdsPower集成文件不存在")
    
    # 验证main.py文件存在
    if not os.path.exists('main.py'):
        raise Exception("main.py文件不存在")
    
    logger.info("✅ 关键组件验证通过")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    success = apply_all_fixes()
    if success:
        print("🎉 所有系统修复应用成功！可以启动系统了。")
    else:
        print("⚠️ 部分修复失败，请检查日志。")
        sys.exit(1) 