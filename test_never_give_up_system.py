#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 永不放弃智能问卷系统 - 测试验证脚本

测试目标：
1. 验证Done动作拦截机制
2. 验证智能页面恢复功能
3. 验证永不放弃执行逻辑
4. 验证真正完成检测
"""

import asyncio
import logging
import json
import time
from typing import Dict, Any

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('never_give_up_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class NeverGiveUpSystemTester:
    def __init__(self):
        self.test_results = {}
        self.digital_human_info = {
            "name": "测试数字人",
            "age": 28,
            "gender": "女",
            "location": "中国北京",
            "profession": "软件工程师",
            "income": "10000-15000",
            "education": "本科"
        }
    
    async def run_comprehensive_test(self):
        """🚀 运行综合测试"""
        logger.info("🚀 ============== 永不放弃智能问卷系统测试开始 ==============")
        
        try:
            # 测试1：Done动作拦截机制
            await self.test_done_action_interception()
            
            # 测试2：智能页面恢复功能
            await self.test_intelligent_page_recovery()
            
            # 测试3：永不放弃执行逻辑
            await self.test_never_give_up_logic()
            
            # 测试4：真正完成检测
            await self.test_true_completion_detection()
            
            # 测试5：WebUI集成测试
            await self.test_webui_integration()
            
            # 生成测试报告
            self.generate_test_report()
            
        except Exception as e:
            logger.error(f"❌ 测试执行失败: {e}")
            return False
        
        logger.info("✅ ============== 永不放弃智能问卷系统测试完成 ==============")
        return True
    
    async def test_done_action_interception(self):
        """🎯 测试Done动作拦截机制"""
        logger.info("🔍 测试1：Done动作拦截机制")
        
        try:
            from src.agent.browser_use.browser_use_agent import BrowserUseAgent
            
            # 创建测试Agent
            agent = BrowserUseAgent(
                digital_human_info=self.digital_human_info,
                never_give_up_mode=True,
                questionnaire_mode=True
            )
            
            # 测试用例1：页面加载卡顿
            test_content_1 = "The page is stuck on loading."
            result_1 = agent._is_premature_done_action(test_content_1)
            assert result_1 == True, "应该检测到过早的done动作"
            
            # 测试用例2：问卷无法完成
            test_content_2 = "Survey could not be completed."
            result_2 = agent._is_premature_done_action(test_content_2)
            assert result_2 == True, "应该检测到过早的done动作"
            
            # 测试用例3：正常完成
            test_content_3 = "Thank you for your participation in this survey."
            agent.questions_answered = 15  # 设置足够的答题数量
            agent.pages_navigated = 5     # 设置足够的页面跳转
            result_3 = agent._is_premature_done_action(test_content_3)
            assert result_3 == False, "不应该拦截正常的完成"
            
            self.test_results['done_interception'] = {
                'status': 'PASSED',
                'details': '所有测试用例通过',
                'test_cases': {
                    'loading_stuck': result_1,
                    'cannot_complete': result_2,
                    'normal_completion': result_3
                }
            }
            
            logger.info("✅ Done动作拦截机制测试通过")
            
        except Exception as e:
            logger.error(f"❌ Done动作拦截机制测试失败: {e}")
            self.test_results['done_interception'] = {
                'status': 'FAILED',
                'error': str(e)
            }
    
    async def test_intelligent_page_recovery(self):
        """🔧 测试智能页面恢复功能"""
        logger.info("🔍 测试2：智能页面恢复功能")
        
        try:
            # 模拟页面恢复场景
            recovery_scenarios = [
                {
                    'name': '页面加载超时',
                    'condition': 'loading_timeout',
                    'expected': 'should_recover'
                },
                {
                    'name': '连续失败3次',
                    'condition': 'consecutive_failures',
                    'expected': 'should_recover'
                },
                {
                    'name': '30秒无成功动作',
                    'condition': 'no_success_30s',
                    'expected': 'should_recover'
                }
            ]
            
            # 创建测试Agent
            from src.agent.browser_use.browser_use_agent import BrowserUseAgent
            agent = BrowserUseAgent(
                digital_human_info=self.digital_human_info,
                never_give_up_mode=True,
                questionnaire_mode=True
            )
            
            test_results = {}
            
            # 测试场景1：连续失败触发恢复
            agent.consecutive_failures = 3
            should_recover_1 = await agent._should_attempt_page_recovery()
            test_results['consecutive_failures'] = should_recover_1
            
            # 测试场景2：长时间无成功动作
            agent.last_successful_action_time = time.time() - 35  # 35秒前
            should_recover_2 = await agent._should_attempt_page_recovery()
            test_results['long_no_success'] = should_recover_2
            
            # 测试场景3：恢复次数限制
            agent.page_recovery_attempts = 15  # 超过最大次数
            should_recover_3 = await agent._should_attempt_page_recovery()
            test_results['max_attempts_reached'] = not should_recover_3
            
            self.test_results['page_recovery'] = {
                'status': 'PASSED',
                'details': '智能页面恢复逻辑正常',
                'scenarios': test_results
            }
            
            logger.info("✅ 智能页面恢复功能测试通过")
            
        except Exception as e:
            logger.error(f"❌ 智能页面恢复功能测试失败: {e}")
            self.test_results['page_recovery'] = {
                'status': 'FAILED',
                'error': str(e)
            }
    
    async def test_never_give_up_logic(self):
        """🚀 测试永不放弃执行逻辑"""
        logger.info("🔍 测试3：永不放弃执行逻辑")
        
        try:
            # 测试永不放弃模式的核心特性
            test_cases = {
                'max_steps_extension': False,
                'failure_tolerance': False,
                'continuous_execution': False
            }
            
            # 创建测试Agent
            from src.agent.browser_use.browser_use_agent import BrowserUseAgent
            agent = BrowserUseAgent(
                digital_human_info=self.digital_human_info,
                never_give_up_mode=True,
                questionnaire_mode=True
            )
            
            # 测试1：最大步数扩展
            if hasattr(agent, 'never_give_up_mode') and agent.never_give_up_mode:
                test_cases['max_steps_extension'] = True
            
            # 测试2：失败容忍度
            if hasattr(agent, 'max_consecutive_failures'):
                test_cases['failure_tolerance'] = agent.max_consecutive_failures >= 5
            
            # 测试3：持续执行能力
            if hasattr(agent, 'questionnaire_mode') and agent.questionnaire_mode:
                test_cases['continuous_execution'] = True
            
            all_passed = all(test_cases.values())
            
            self.test_results['never_give_up'] = {
                'status': 'PASSED' if all_passed else 'PARTIAL',
                'details': '永不放弃逻辑验证',
                'test_cases': test_cases
            }
            
            logger.info(f"✅ 永不放弃执行逻辑测试{'通过' if all_passed else '部分通过'}")
            
        except Exception as e:
            logger.error(f"❌ 永不放弃执行逻辑测试失败: {e}")
            self.test_results['never_give_up'] = {
                'status': 'FAILED',
                'error': str(e)
            }
    
    async def test_true_completion_detection(self):
        """🎯 测试真正完成检测"""
        logger.info("🔍 测试4：真正完成检测")
        
        try:
            # 创建测试Agent
            from src.agent.browser_use.browser_use_agent import BrowserUseAgent
            agent = BrowserUseAgent(
                digital_human_info=self.digital_human_info,
                never_give_up_mode=True,
                questionnaire_mode=True
            )
            
            # 模拟不同的完成场景
            completion_tests = {}
            
            # 测试1：真正的完成提示
            agent.questions_answered = 15
            agent.pages_navigated = 5
            # 由于无法模拟真实页面，我们测试逻辑
            completion_tests['sufficient_progress'] = (
                agent.questions_answered >= 10 and 
                agent.pages_navigated >= 5
            )
            
            # 测试2：完成指示词检测逻辑
            completion_indicators = [
                "thank you for your participation",
                "survey completed",
                "感谢您的参与",
                "问卷已完成"
            ]
            
            test_text = "Thank you for your participation in this survey."
            found_indicator = any(
                indicator in test_text.lower() 
                for indicator in completion_indicators
            )
            completion_tests['completion_indicators'] = found_indicator
            
            # 测试3：URL完成检测逻辑
            test_url = "https://example.com/survey/complete"
            url_indicators = ["complete", "finish", "thank", "success", "end"]
            url_completion = any(
                indicator in test_url.lower() 
                for indicator in url_indicators
            )
            completion_tests['url_completion'] = url_completion
            
            all_passed = all(completion_tests.values())
            
            self.test_results['completion_detection'] = {
                'status': 'PASSED' if all_passed else 'PARTIAL',
                'details': '真正完成检测逻辑验证',
                'tests': completion_tests
            }
            
            logger.info(f"✅ 真正完成检测测试{'通过' if all_passed else '部分通过'}")
            
        except Exception as e:
            logger.error(f"❌ 真正完成检测测试失败: {e}")
            self.test_results['completion_detection'] = {
                'status': 'FAILED',
                'error': str(e)
            }
    
    async def test_webui_integration(self):
        """🔧 测试WebUI集成"""
        logger.info("🔍 测试5：WebUI集成测试")
        
        try:
            # 测试WebUI组件是否存在
            integration_tests = {}
            
            # 测试1：BrowserUseAgent是否可导入
            try:
                from src.agent.browser_use.browser_use_agent import BrowserUseAgent
                integration_tests['agent_import'] = True
            except ImportError:
                integration_tests['agent_import'] = False
            
            # 测试2：CustomController是否可导入
            try:
                from src.controller.custom_controller import CustomController
                integration_tests['controller_import'] = True
            except ImportError:
                integration_tests['controller_import'] = False
            
            # 测试3：WebUI配置是否正确
            try:
                import os
                webui_files = [
                    'src/webui/interface.py',
                    'src/webui/webui_manager.py',
                    'adspower_browser_use_integration.py'
                ]
                integration_tests['webui_files'] = all(
                    os.path.exists(f) for f in webui_files
                )
            except Exception:
                integration_tests['webui_files'] = False
            
            all_passed = all(integration_tests.values())
            
            self.test_results['webui_integration'] = {
                'status': 'PASSED' if all_passed else 'PARTIAL',
                'details': 'WebUI集成状态检查',
                'tests': integration_tests
            }
            
            logger.info(f"✅ WebUI集成测试{'通过' if all_passed else '部分通过'}")
            
        except Exception as e:
            logger.error(f"❌ WebUI集成测试失败: {e}")
            self.test_results['webui_integration'] = {
                'status': 'FAILED',
                'error': str(e)
            }
    
    def generate_test_report(self):
        """📊 生成测试报告"""
        logger.info("📊 生成测试报告...")
        
        report = {
            'test_summary': {
                'total_tests': len(self.test_results),
                'passed': sum(1 for r in self.test_results.values() if r['status'] == 'PASSED'),
                'partial': sum(1 for r in self.test_results.values() if r['status'] == 'PARTIAL'),
                'failed': sum(1 for r in self.test_results.values() if r['status'] == 'FAILED'),
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
            },
            'detailed_results': self.test_results,
            'recommendations': self._generate_recommendations()
        }
        
        # 保存报告到文件
        with open('never_give_up_test_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 打印摘要
        logger.info("📊 ============== 测试报告摘要 ==============")
        logger.info(f"总测试数: {report['test_summary']['total_tests']}")
        logger.info(f"通过: {report['test_summary']['passed']}")
        logger.info(f"部分通过: {report['test_summary']['partial']}")
        logger.info(f"失败: {report['test_summary']['failed']}")
        
        # 详细结果
        for test_name, result in self.test_results.items():
            status_emoji = {
                'PASSED': '✅',
                'PARTIAL': '⚠️',
                'FAILED': '❌'
            }
            logger.info(f"{status_emoji.get(result['status'], '❓')} {test_name}: {result['status']}")
    
    def _generate_recommendations(self):
        """💡 生成改进建议"""
        recommendations = []
        
        for test_name, result in self.test_results.items():
            if result['status'] == 'FAILED':
                if test_name == 'done_interception':
                    recommendations.append("建议检查Done动作拦截逻辑的实现")
                elif test_name == 'page_recovery':
                    recommendations.append("建议优化智能页面恢复机制")
                elif test_name == 'never_give_up':
                    recommendations.append("建议完善永不放弃执行逻辑")
                elif test_name == 'completion_detection':
                    recommendations.append("建议改进真正完成检测算法")
                elif test_name == 'webui_integration':
                    recommendations.append("建议检查WebUI集成配置")
        
        if not recommendations:
            recommendations.append("所有测试通过，系统运行良好！")
        
        return recommendations

async def main():
    """主测试函数"""
    tester = NeverGiveUpSystemTester()
    success = await tester.run_comprehensive_test()
    
    if success:
        print("\n🎉 永不放弃智能问卷系统测试完成！")
        print("📄 详细报告已保存到: never_give_up_test_report.json")
        print("📝 日志已保存到: never_give_up_test.log")
    else:
        print("\n❌ 测试执行失败，请查看日志了解详情")
    
    return success

if __name__ == "__main__":
    asyncio.run(main()) 