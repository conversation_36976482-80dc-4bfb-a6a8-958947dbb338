#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复adspower_browser_use_integration.py中的logger错误
"""

def fix_logger_error():
    """修复文件中的logger未定义错误"""
    
    filename = "adspower_browser_use_integration.py"
    
    try:
        # 读取文件内容
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找并移除问题行
        lines = content.split('\n')
        fixed_lines = []
        
        skip_next_lines = 0
        for i, line in enumerate(lines):
            if skip_next_lines > 0:
                skip_next_lines -= 1
                continue
                
            # 查找问题行
            if 'if enhanced_cleanup_available:' in line and i < 150:  # 在logger定义之前
                # 跳过这几行
                skip_next_lines = 3  # 跳过if块的3行
                print(f"移除第{i+1}行及其后续行: {line}")
                continue
            
            fixed_lines.append(line)
        
        # 在合适的位置添加增强版资源管理器状态报告
        new_lines = []
        for i, line in enumerate(fixed_lines):
            new_lines.append(line)
            
            # 在双知识库状态报告后添加
            if '⚠️ 双知识库系统导入失败")' in line:
                new_lines.append('')
                new_lines.append('# 增强版AdsPower资源管理器状态报告')
                new_lines.append('if enhanced_cleanup_available:')
                new_lines.append('    logger.info("✅ 增强版AdsPower资源管理器已加载 - 支持两步骤彻底清理")')
                new_lines.append('else:')
                new_lines.append('    logger.warning("⚠️ 增强版AdsPower资源管理器未找到，使用基础版本")')
                print(f"在第{i+1}行后添加增强版资源管理器状态报告")
                break
        
        # 写回文件
        with open(filename, 'w', encoding='utf-8') as f:
            f.write('\n'.join(new_lines))
        
        print("✅ logger错误修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

if __name__ == "__main__":
    fix_logger_error()
