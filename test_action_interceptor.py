#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试Action拦截器功能
验证100%智能化处理是否正常工作
"""

import asyncio
import logging
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_action_interceptor():
    """测试Action拦截器功能"""
    try:
        print("🔥 开始测试Action拦截器功能...")
        
        # 1. 导入必要的模块
        from action_interceptor_patch import apply_action_interceptor_patch
        from src.controller.custom_controller import CustomController
        
        # 2. 创建CustomController实例
        print("📝 创建CustomController实例...")
        controller = CustomController()
        
        # 3. 设置数字人信息
        digital_human_info = {
            "name": "张小娟",
            "age": 28,
            "gender": "female",
            "residence": "北京市丰台区",
            "location": "北京",
            "profession": "会计/财务",
            "education": "本科",
            "income": "5000-8000元"
        }
        controller.set_digital_human_info(digital_human_info)
        print(f"✅ 数字人信息设置完成: {digital_human_info['name']}")
        
        # 4. 应用Action拦截器补丁
        print("🎯 应用Action拦截器补丁...")
        patch_success = apply_action_interceptor_patch(controller)
        
        if patch_success:
            print("✅ Action拦截器补丁应用成功！")
            print("🧠 智能化特性已激活：")
            print("   - 🗺️ 国家选择智能化（最高优先级）")
            print("   - 🗣️ 语言选择智能化") 
            print("   - 👤 个人信息智能化")
            print("   - 📊 态度偏好智能化")
            print("   - 🔄 通用选择智能化")
            print("   - 🛡️ 反作弊策略集成")
        else:
            print("❌ Action拦截器补丁应用失败")
            return False
        
        # 5. 验证补丁是否正确应用
        print("🔍 验证补丁应用状态...")
        
        # 检查act方法是否被替换
        original_act_name = getattr(controller.act, '__name__', 'unknown')
        print(f"📊 当前act方法: {original_act_name}")
        
        # 检查数字人信息是否正确设置
        stored_info = getattr(controller, 'digital_human_info', None)
        if stored_info:
            print(f"✅ 数字人信息已存储: {stored_info.get('name', '未知')}")
        else:
            print("⚠️ 数字人信息未正确存储")
        
        # 6. 测试题型检测功能
        print("🧪 测试题型检测功能...")
        
        # 导入检测函数
        from action_interceptor_patch import (
            is_country_selection_element,
            is_language_selection_element,
            determine_target_nationality,
            validate_country_recommendation
        )
        
        # 测试国家选择检测
        test_texts = [
            "China (中国)",
            "Australia (English)", 
            "简体中文",
            "English",
            "会计/财务",
            "满意"
        ]
        
        for text in test_texts:
            is_country = is_country_selection_element(text)
            is_language = is_language_selection_element(text)
            print(f"   📝 '{text}' -> 国家: {is_country}, 语言: {is_language}")
        
        # 测试目标国籍确定
        target_nationality = determine_target_nationality(digital_human_info)
        print(f"🎯 目标国籍: {target_nationality}")
        
        # 测试国家推荐验证
        test_recommendations = ["China (中国)", "Australia (English)", "中国", "澳大利亚"]
        for rec in test_recommendations:
            is_valid = validate_country_recommendation(rec, target_nationality)
            print(f"   ✅ '{rec}' 验证结果: {is_valid}")
        
        print("🎉 Action拦截器测试完成！")
        print("🚀 系统已准备好进行100%智能化答题")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    try:
        success = asyncio.run(test_action_interceptor())
        return success
    except Exception as e:
        print(f"❌ 主函数执行失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n{'='*50}")
    if success:
        print("🎉 Action拦截器测试成功！系统已准备好进行智能化答题")
        print("🎯 关键特性:")
        print("   ✅ 100%智能化处理所有点击动作")
        print("   ✅ 国家选择优先级最高（解决澳大利亚问题）")
        print("   ✅ 基于数字人信息的智能决策")
        print("   ✅ 反作弊策略集成")
        print("   ✅ 多层次回退机制")
    else:
        print("❌ Action拦截器测试失败！请检查代码")
    print(f"{'='*50}")
    
    sys.exit(0 if success else 1) 