#!/usr/bin/env python3
"""
紧急修复 custom_controller.py 的语法错误
问题：第451行函数定义缺少函数体
"""

import re

def fix_syntax_error():
    """修复语法错误"""
    
    # 读取文件
    with open('src/controller/custom_controller.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找问题行
    lines = content.split('\n')
    
    # 找到问题函数定义
    for i, line in enumerate(lines):
        if 'async def intelligent_wait_for_page_transition' in line:
            print(f"找到问题函数定义在第 {i+1} 行")
            
            # 检查下一行是否直接是另一个函数定义
            if i+1 < len(lines) and lines[i+1].strip().startswith('async def'):
                print("确认：函数体缺失")
                
                # 插入函数体
                function_body = '''            """🎯 智能页面跳转检测和等待 - 确保多次跳转后仍能正常答题"""
            try:
                page = await browser.get_current_page()
                current_url = page.url
                current_title = await page.title()
                
                logger.info(f"🔄 开始智能页面跳转检测 - 当前页面: {current_title}")
                
                # 更新跳转状态
                self.page_transition_state.update({
                    'last_url': current_url,
                    'last_title': current_title,
                    'waiting_for_load': True,
                    'load_start_time': time.time()
                })
                
                # 智能等待页面稳定
                stable_count = 0
                required_stable_count = 3  # 需要连续3次检测都稳定
                
                for _ in range(max_wait_seconds * 2):  # 每0.5秒检测一次
                    await asyncio.sleep(0.5)
                    
                    try:
                        new_url = page.url
                        new_title = await page.title()
                        
                        # 检测页面是否已经稳定
                        if new_url == current_url and new_title == current_title:
                            stable_count += 1
                            if stable_count >= required_stable_count:
                                # 页面已稳定，检查是否有新的问卷内容
                                await self._detect_questionnaire_content(page)
                                break
                        else:
                            # 页面仍在变化
                            current_url = new_url
                            current_title = new_title
                            stable_count = 0
                            self.page_transition_state['transition_count'] += 1
                            logger.info(f"🔄 检测到页面跳转 #{self.page_transition_state['transition_count']}: {new_title}")
                    
                    except Exception as e:
                        logger.warning(f"⚠️ 页面状态检测失败: {e}")
                        stable_count = 0
                
                # 更新最终状态
                self.page_transition_state['waiting_for_load'] = False
                
                msg = f"✅ 页面跳转检测完成 - 总跳转次数: {self.page_transition_state['transition_count']}"
                logger.info(msg)
                return ActionResult(extracted_content=msg, include_in_memory=True)
                
            except Exception as e:
                error_msg = f"❌ 页面跳转检测失败: {e}"
                logger.error(error_msg)
                return ActionResult(error=error_msg)
'''
                
                # 插入函数体
                lines.insert(i+1, function_body)
                
                # 重新组合内容
                fixed_content = '\n'.join(lines)
                
                # 写回文件
                with open('src/controller/custom_controller.py', 'w', encoding='utf-8') as f:
                    f.write(fixed_content)
                
                print("✅ 语法错误修复完成")
                return True
    
    print("❌ 未找到问题函数")
    return False

if __name__ == "__main__":
    fix_syntax_error() 