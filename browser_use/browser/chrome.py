"""Chrome browser configuration and arguments"""

# Chrome基础参数
CHROME_ARGS = [
    "--no-sandbox",
    "--disable-dev-shm-usage",
    "--disable-gpu",
    "--disable-extensions",
    "--disable-plugins",
    "--disable-images",
    "--disable-background-timer-throttling",
    "--disable-backgrounding-occluded-windows",
    "--disable-renderer-backgrounding",
    "--disable-field-trial-config",
    "--disable-back-forward-cache",
    "--disable-ipc-flooding-protection",
    "--disable-hang-monitor",
    "--disable-prompt-on-repost",
    "--disable-sync",
    "--disable-domain-reliability",
    "--disable-component-update",
    "--disable-background-networking",
    "--disable-breakpad",
    "--disable-client-side-phishing-detection",
]

# Chrome无头模式参数
CHROME_HEADLESS_ARGS = [
    "--headless=new",
    "--disable-gpu",
    "--no-sandbox",
]

# Chrome安全禁用参数
CHROME_DISABLE_SECURITY_ARGS = [
    "--disable-web-security",
    "--disable-features=VizDisplayCompositor",
    "--disable-blink-features=AutomationControlled",
    "--allow-running-insecure-content",
    "--disable-site-isolation-trials",
]

# Chrome Docker环境参数
CHROME_DOCKER_ARGS = [
    "--no-sandbox",
    "--disable-dev-shm-usage",
    "--disable-gpu",
    "--remote-debugging-address=0.0.0.0",
]

# Chrome确定性渲染参数
CHROME_DETERMINISTIC_RENDERING_ARGS = [
    "--disable-background-timer-throttling",
    "--disable-backgrounding-occluded-windows",
    "--disable-renderer-backgrounding",
    "--disable-features=TranslateUI",
    "--disable-features=BlinkGenPropertyTrees",
    "--run-all-compositor-stages-before-draw",
    "--disable-threaded-animation",
    "--disable-threaded-scrolling",
    "--disable-checker-imaging",
    "--disable-new-content-rendering-timeout",
    "--disable-image-animation-resync",
]
