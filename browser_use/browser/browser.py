"""Browser implementation module"""

import logging
import asyncio
import os
from typing import Dict, Any, Optional, List
from playwright.async_api import <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>er<PERSON>ontext as PlaywrightBrowser<PERSON>ontext, Page
from .context import Browser<PERSON>ontext, BrowserContextConfig

logger = logging.getLogger(__name__)

# 检测是否在Docker环境中运行
IN_DOCKER = os.path.exists('/.dockerenv') or os.environ.get('DOCKER_CONTAINER', False)

class BrowserConfig:
    """浏览器配置类"""

    def __init__(self, config: Dict[str, Any] = None):
        config = config or {}
        self.headless = config.get('headless', False)
        self.slow_mo = config.get('slow_mo', 50)
        self.timeout = config.get('timeout', 30000)
        self.viewport = config.get('viewport', {'width': 1920, 'height': 1080})
        self.user_data_dir = config.get('user_data_dir', None)
        self.proxy = config.get('proxy', None)
        self.args = config.get('args', [])
        self.debug_port = config.get('debug_port', None)

class Browser:
    """浏览器类 - 与AdsPower集成的实现"""

    def __init__(self, config: Optional[Dict[str, Any]] = None, playwright_browser: Optional[PlaywrightBrowser] = None):
        """初始化浏览器

        Args:
            config: 浏览器配置
            playwright_browser: Playwright浏览器实例
        """
        self.config = BrowserConfig(config or {})
        self.playwright_browser = playwright_browser
        self.context = None
        self.profile_id = None
        self._current_page = None

        # 注意：不在构造函数中创建异步任务，而是在需要时懒加载

    async def _create_default_context(self):
        """创建默认的浏览器上下文"""
        try:
            if self.playwright_browser and not self.context:
                playwright_context = await self.playwright_browser.new_context()
                self.context = BrowserContext(playwright_context=playwright_context)
        except Exception as e:
            logger.warning(f"Failed to create default context: {e}")

    async def new_context(self, config: Optional[BrowserContextConfig] = None) -> BrowserContext:
        """创建新的浏览器上下文"""
        if self.playwright_browser:
            # 如果已经有上下文且没有指定新配置，返回现有上下文
            if self.context and config is None:
                return self.context

            # 创建Playwright上下文
            context_options = {}
            if config:
                if hasattr(config, 'viewport') and config.viewport:
                    context_options['viewport'] = config.viewport
                if hasattr(config, 'user_agent') and config.user_agent:
                    context_options['user_agent'] = config.user_agent
                if hasattr(config, 'locale') and config.locale:
                    context_options['locale'] = config.locale
                if hasattr(config, 'timezone_id') and config.timezone_id:
                    context_options['timezone_id'] = config.timezone_id
                if hasattr(config, 'geolocation') and config.geolocation:
                    context_options['geolocation'] = config.geolocation
                if hasattr(config, 'permissions') and config.permissions:
                    context_options['permissions'] = config.permissions
                if hasattr(config, 'extra_http_headers') and config.extra_http_headers:
                    context_options['extra_http_headers'] = config.extra_http_headers

            playwright_context = await self.playwright_browser.new_context(**context_options)
            new_context = BrowserContext(config=config.__dict__ if config else None, playwright_context=playwright_context)

            # 如果这是第一个上下文，设置为默认上下文
            if not self.context:
                self.context = new_context

            return new_context

        raise RuntimeError("No playwright browser available")

    async def get_current_page(self) -> Page:
        """获取当前页面"""
        if self.context:
            return await self.context.get_current_page()
        raise RuntimeError("No browser context available")

    async def get_selector_map(self) -> Dict:
        """获取选择器映射（兼容性方法）"""
        # 这是一个兼容性方法，返回空字典
        return {}

    async def new_page(self) -> Page:
        """创建新页面"""
        if self.context:
            return await self.context.new_page()
        raise RuntimeError("No browser context available")

    async def close(self):
        """关闭浏览器"""
        if self.context:
            await self.context.close()
        if self.playwright_browser:
            await self.playwright_browser.close()

    async def goto(self, url: str):
        """导航到指定URL"""
        page = await self.get_current_page()
        await page.goto(url)

    async def get_cookies(self):
        """获取cookies"""
        if self.context and self.context.playwright_context:
            return await self.context.playwright_context.cookies()
        return []

    async def set_cookies(self, cookies: list):
        """设置cookies"""
        if self.context:
            await self.context.add_cookies(cookies)