"""Browser views module"""

import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class BrowserState:
    """浏览器状态"""
    
    url: str = ""
    title: str = ""
    screenshot: Optional[str] = None
    elements: List[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.elements is None:
            self.elements = []
    
    def model_dump(self, exclude_none: bool = True) -> Dict[str, Any]:
        """兼容Pydantic的model_dump方法"""
        result = {
            "url": self.url,
            "title": self.title,
            "screenshot": self.screenshot,
            "elements": self.elements
        }
        if exclude_none:
            result = {k: v for k, v in result.items() if v is not None}
        return result
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "url": self.url,
            "title": self.title,
            "screenshot": self.screenshot,
            "elements": self.elements
        }
