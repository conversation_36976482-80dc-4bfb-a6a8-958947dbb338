"""DOM manipulation module"""

import logging
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class DOMElementNode:
    """DOM元素节点"""
    
    tag: str
    attributes: Dict[str, str]
    text: str = ""
    children: List['DOMElementNode'] = None
    xpath: str = ""
    selector: str = ""
    
    def __post_init__(self):
        if self.children is None:
            self.children = []
    
    def get_attribute(self, name: str, default: str = "") -> str:
        """获取属性值"""
        return self.attributes.get(name, default)
    
    def has_attribute(self, name: str) -> bool:
        """检查是否有属性"""
        return name in self.attributes
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'tag': self.tag,
            'attributes': self.attributes,
            'text': self.text,
            'xpath': self.xpath,
            'selector': self.selector,
            'children': [child.to_dict() for child in self.children]
        }

class DOMElement:
    """DOM元素包装器"""
    
    def __init__(self, element_data: Union[Dict[str, Any], DOMElementNode]):
        """初始化DOM元素
        
        Args:
            element_data: 元素数据
        """
        if isinstance(element_data, DOMElementNode):
            self.node = element_data
        else:
            self.node = DOMElementNode(
                tag=element_data.get('tag', ''),
                attributes=element_data.get('attributes', {}),
                text=element_data.get('text', ''),
                xpath=element_data.get('xpath', ''),
                selector=element_data.get('selector', '')
            )
    
    @property
    def tag(self) -> str:
        """标签名"""
        return self.node.tag
    
    @property
    def text(self) -> str:
        """文本内容"""
        return self.node.text
    
    @property
    def attributes(self) -> Dict[str, str]:
        """属性字典"""
        return self.node.attributes
    
    def get_attribute(self, name: str, default: str = "") -> str:
        """获取属性值"""
        return self.node.get_attribute(name, default)
    
    def has_attribute(self, name: str) -> bool:
        """检查是否有属性"""
        return self.node.has_attribute(name)

class DOMOperations:
    """DOM操作类"""
    
    def __init__(self, page=None):
        """初始化DOM操作
        
        Args:
            page: Playwright页面实例
        """
        self.page = page
    
    async def find_element(self, selector: str) -> Optional[DOMElement]:
        """查找元素
        
        Args:
            selector: CSS选择器
            
        Returns:
            Optional[DOMElement]: 找到的元素
        """
        if not self.page:
            return None
            
        try:
            element = await self.page.query_selector(selector)
            if element:
                # 获取元素信息
                tag = await element.evaluate('el => el.tagName.toLowerCase()')
                text = await element.text_content() or ""
                attributes = await element.evaluate('''el => {
                    const attrs = {};
                    for (let attr of el.attributes) {
                        attrs[attr.name] = attr.value;
                    }
                    return attrs;
                }''')
                
                return DOMElement({
                    'tag': tag,
                    'text': text,
                    'attributes': attributes,
                    'selector': selector
                })
        except Exception as e:
            logger.error(f"Failed to find element {selector}: {e}")
            
        return None
    
    async def find_elements(self, selector: str) -> List[DOMElement]:
        """查找多个元素
        
        Args:
            selector: CSS选择器
            
        Returns:
            List[DOMElement]: 找到的元素列表
        """
        if not self.page:
            return []
            
        try:
            elements = await self.page.query_selector_all(selector)
            result = []
            
            for element in elements:
                tag = await element.evaluate('el => el.tagName.toLowerCase()')
                text = await element.text_content() or ""
                attributes = await element.evaluate('''el => {
                    const attrs = {};
                    for (let attr of el.attributes) {
                        attrs[attr.name] = attr.value;
                    }
                    return attrs;
                }''')
                
                result.append(DOMElement({
                    'tag': tag,
                    'text': text,
                    'attributes': attributes,
                    'selector': selector
                }))
                
            return result
        except Exception as e:
            logger.error(f"Failed to find elements {selector}: {e}")
            
        return []
    
    async def click_element(self, selector: str) -> bool:
        """点击元素
        
        Args:
            selector: CSS选择器
            
        Returns:
            bool: 是否成功
        """
        if not self.page:
            return False
            
        try:
            await self.page.click(selector)
            return True
        except Exception as e:
            logger.error(f"Failed to click element {selector}: {e}")
            return False
    
    async def type_text(self, selector: str, text: str) -> bool:
        """输入文本
        
        Args:
            selector: CSS选择器
            text: 要输入的文本
            
        Returns:
            bool: 是否成功
        """
        if not self.page:
            return False
            
        try:
            await self.page.fill(selector, text)
            return True
        except Exception as e:
            logger.error(f"Failed to type text in {selector}: {e}")
            return False
