"""Screen resolution utilities"""

import logging
from typing import Dict, Tuple

logger = logging.getLogger(__name__)

def get_screen_resolution() -> Dict[str, int]:
    """获取屏幕分辨率
    
    Returns:
        Dict[str, int]: 包含width和height的字典
    """
    try:
        # 尝试使用tkinter获取屏幕分辨率
        import tkinter as tk
        root = tk.Tk()
        width = root.winfo_screenwidth()
        height = root.winfo_screenheight()
        root.destroy()
        return {"width": width, "height": height}
    except Exception as e:
        logger.warning(f"无法获取屏幕分辨率，使用默认值: {e}")
        # 返回默认分辨率
        return {"width": 1920, "height": 1080}

def get_window_adjustments() -> Tuple[int, int]:
    """获取窗口位置调整值
    
    Returns:
        Tuple[int, int]: (offset_x, offset_y)
    """
    # 返回默认的窗口偏移量
    return (0, 0)
