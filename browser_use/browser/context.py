"""Browser context module"""

import logging
import asyncio
from typing import Dict, Any, Optional, List
from playwright.async_api import Page, BrowserContext as PlaywrightBrowserContext

logger = logging.getLogger(__name__)

class BrowserContextState:
    """浏览器上下文状态"""

    def __init__(self):
        self.pages = []
        self.cookies = []
        self.local_storage = {}
        self.session_storage = {}

class BrowserContextConfig:
    """浏览器上下文配置"""

    def __init__(self, config: Dict[str, Any] = None):
        config = config or {}
        self.viewport = config.get('viewport', {'width': 1920, 'height': 1080})
        self.user_agent = config.get('user_agent', None)
        self.locale = config.get('locale', 'en-US')
        self.timezone_id = config.get('timezone_id', 'Asia/Shanghai')
        self.geolocation = config.get('geolocation', None)
        self.permissions = config.get('permissions', [])
        self.extra_http_headers = config.get('extra_http_headers', {})

class BrowserContext:
    """浏览器上下文类 - 与AdsPower集成的实现"""

    def __init__(self, config: Optional[Dict[str, Any]] = None, playwright_context: Optional[PlaywrightBrowserContext] = None):
        """初始化浏览器上下文

        Args:
            config: 上下文配置
            playwright_context: Playwright浏览器上下文实例
        """
        self.config = BrowserContextConfig(config or {})
        self.playwright_context = playwright_context
        self.pages = []
        self._current_page = None

    async def get_current_page(self) -> Page:
        """获取当前页面"""
        if self._current_page and not self._current_page.is_closed():
            return self._current_page

        if self.playwright_context:
            # 获取现有页面或创建新页面
            pages = self.playwright_context.pages
            if pages:
                self._current_page = pages[0]
                return self._current_page
            else:
                self._current_page = await self.playwright_context.new_page()
                return self._current_page

        raise RuntimeError("No playwright context available")

    async def navigate_to(self, url: str, wait_until: str = 'networkidle', timeout: int = 30000):
        """导航到指定URL"""
        page = await self.get_current_page()
        await page.goto(url, wait_until=wait_until, timeout=timeout)

    async def new_page(self) -> Page:
        """创建新页面"""
        if self.playwright_context:
            page = await self.playwright_context.new_page()
            self.pages.append(page)
            self._current_page = page
            return page
        raise RuntimeError("No playwright context available")

    async def close(self):
        """关闭上下文"""
        if self.playwright_context:
            await self.playwright_context.close()

    async def add_cookies(self, cookies: list):
        """添加cookies"""
        if self.playwright_context:
            await self.playwright_context.add_cookies(cookies)

    async def clear_cookies(self):
        """清除cookies"""
        if self.playwright_context:
            await self.playwright_context.clear_cookies()