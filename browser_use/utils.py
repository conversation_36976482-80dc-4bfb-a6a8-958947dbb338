"""Utility functions module"""

import logging
import time
import functools
from typing import Any, Callable, Awaitable

logger = logging.getLogger(__name__)

def time_execution_async(func: Callable[..., Awaitable[Any]]) -> Callable[..., Awaitable[Any]]:
    """异步函数执行时间装饰器
    
    Args:
        func: 要装饰的异步函数
        
    Returns:
        Callable[..., Awaitable[Any]]: 装饰后的函数
    """
    @functools.wraps(func)
    async def wrapper(*args: Any, **kwargs: Any) -> Any:
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            end_time = time.time()
            duration = end_time - start_time
            logger.debug(f"Function {func.__name__} took {duration:.2f} seconds")
            return result
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            logger.error(f"Function {func.__name__} failed after {duration:.2f} seconds: {e}")
            raise
    return wrapper

def time_execution_sync(func: Callable[..., Any]) -> Callable[..., Any]:
    """同步函数执行时间装饰器

    Args:
        func: 要装饰的同步函数

    Returns:
        Callable[..., Any]: 装饰后的函数
    """
    @functools.wraps(func)
    def wrapper(*args: Any, **kwargs: Any) -> Any:
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            end_time = time.time()
            duration = end_time - start_time
            logger.debug(f"Function {func.__name__} took {duration:.2f} seconds")
            return result
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            logger.error(f"Function {func.__name__} failed after {duration:.2f} seconds: {e}")
            raise
    return wrapper