"""Utility functions module"""

import logging
import time
import functools
from typing import Any, Callable, Awaitable

logger = logging.getLogger(__name__)

def time_execution_async(name_or_func=None):
    """异步函数执行时间装饰器 - 支持带参数和不带参数的使用

    使用方式:
    @time_execution_async
    async def my_func(): pass

    或者:
    @time_execution_async("custom name")
    async def my_func(): pass
    """
    def decorator(func: Callable[..., Awaitable[Any]]) -> Callable[..., Awaitable[Any]]:
        @functools.wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                end_time = time.time()
                duration = end_time - start_time
                func_name = name_or_func if isinstance(name_or_func, str) else func.__name__
                logger.debug(f"Function {func_name} took {duration:.2f} seconds")
                return result
            except Exception as e:
                end_time = time.time()
                duration = end_time - start_time
                func_name = name_or_func if isinstance(name_or_func, str) else func.__name__
                logger.error(f"Function {func_name} failed after {duration:.2f} seconds: {e}")
                raise
        return wrapper

    # 如果直接传入函数（不带参数的装饰器）
    if callable(name_or_func):
        return decorator(name_or_func)
    # 如果传入字符串或None（带参数的装饰器）
    else:
        return decorator

def time_execution_sync(func: Callable[..., Any]) -> Callable[..., Any]:
    """同步函数执行时间装饰器

    Args:
        func: 要装饰的同步函数

    Returns:
        Callable[..., Any]: 装饰后的函数
    """
    @functools.wraps(func)
    def wrapper(*args: Any, **kwargs: Any) -> Any:
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            end_time = time.time()
            duration = end_time - start_time
            logger.debug(f"Function {func.__name__} took {duration:.2f} seconds")
            return result
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            logger.error(f"Function {func.__name__} failed after {duration:.2f} seconds: {e}")
            raise
    return wrapper