"""Agent service module"""

import logging
import asyncio
from typing import Dict, Any, Optional, List, Callable, Awaitable
from ..browser.browser import Browser
from ..browser.context import BrowserContext

logger = logging.getLogger(__name__)

class Agent:
    """基础代理类 - 与browser-use兼容的实现"""

    def __init__(self,
                 task: str = "",
                 llm: Optional[Any] = None,
                 browser: Optional[Browser] = None,
                 browser_context: Optional[BrowserContext] = None,
                 config: Optional[Dict[str, Any]] = None):
        """初始化代理

        Args:
            task: 任务描述
            llm: 语言模型实例
            browser: 浏览器实例
            browser_context: 浏览器上下文
            config: 配置信息
        """
        self.task = task
        self.llm = llm
        self.browser = browser
        self.browser_context = browser_context
        self.config = config or {}
        self.history = []
        self._step_count = 0
        self._max_steps = self.config.get('max_steps', 50)

    async def run(self, max_steps: Optional[int] = None) -> Dict[str, Any]:
        """运行代理任务

        Args:
            max_steps: 最大步数

        Returns:
            Dict[str, Any]: 执行结果
        """
        max_steps = max_steps or self._max_steps

        try:
            for step in range(max_steps):
                self._step_count = step + 1

                # 观察当前状态
                observation = await self.observe()

                # 执行动作
                action_result = await self.act(observation)

                # 添加到历史记录
                self.add_to_history(observation, action_result)

                # 检查是否完成
                if action_result.get('completed', False):
                    return {
                        'success': True,
                        'steps': self._step_count,
                        'result': action_result,
                        'message': 'Task completed successfully'
                    }

                # 短暂等待
                await asyncio.sleep(0.5)

            return {
                'success': False,
                'steps': self._step_count,
                'message': f'Task not completed within {max_steps} steps'
            }

        except Exception as e:
            logger.error(f"Agent execution failed: {e}")
            return {
                'success': False,
                'steps': self._step_count,
                'error': str(e),
                'message': 'Task execution failed'
            }

    async def act(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """执行动作

        Args:
            state: 当前状态

        Returns:
            Dict[str, Any]: 执行结果
        """
        # 默认实现 - 子类应该重写此方法
        return {
            'action': 'no_action',
            'completed': True,
            'message': 'No action implemented'
        }

    async def observe(self) -> Dict[str, Any]:
        """观察当前状态

        Returns:
            Dict[str, Any]: 观察结果
        """
        try:
            if self.browser_context:
                page = await self.browser_context.get_current_page()
                return {
                    'url': page.url,
                    'title': await page.title(),
                    'step': self._step_count
                }
        except Exception as e:
            logger.warning(f"Failed to observe state: {e}")

        return {
            'step': self._step_count,
            'error': 'Failed to observe state'
        }

    def add_to_history(self, observation: Dict[str, Any], action_result: Dict[str, Any]):
        """添加到历史记录

        Args:
            observation: 观察结果
            action_result: 动作执行结果
        """
        self.history.append({
            'step': self._step_count,
            'observation': observation,
            'action_result': action_result,
            'timestamp': asyncio.get_event_loop().time()
        })