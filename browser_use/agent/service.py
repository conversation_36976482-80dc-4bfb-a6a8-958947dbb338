"""Agent service module"""

import logging
from typing import Dict, Any, Optional, List

logger = logging.getLogger(__name__)

class Agent:
    """基础代理类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化代理
        
        Args:
            config: 配置信息
        """
        self.config = config or {}
        self.history = []
        
    async def act(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """执行动作
        
        Args:
            state: 当前状态
            
        Returns:
            Dict[str, Any]: 执行结果
        """
        raise NotImplementedError("This method should be implemented by subclasses")
        
    async def observe(self) -> Dict[str, Any]:
        """观察当前状态
        
        Returns:
            Dict[str, Any]: 观察结果
        """
        raise NotImplementedError("This method should be implemented by subclasses")
        
    def add_to_history(self, action: Dict[str, Any], result: Dict[str, Any]):
        """添加到历史记录
        
        Args:
            action: 执行的动作
            result: 执行结果
        """
        self.history.append({
            'action': action,
            'result': result
        }) 