"""Agent views module"""

import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class AgentStepInfo:
    """代理步骤信息"""
    
    action: Dict[str, Any]
    result: Dict[str, Any]
    timestamp: datetime
    duration: float
    success: bool
    error: Optional[str] = None
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AgentStepInfo':
        """从字典创建实例
        
        Args:
            data: 字典数据
            
        Returns:
            AgentStepInfo: 步骤信息实例
        """
        return cls(
            action=data['action'],
            result=data['result'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            duration=data['duration'],
            success=data['success'],
            error=data.get('error')
        )
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典
        
        Returns:
            Dict[str, Any]: 字典数据
        """
        return {
            'action': self.action,
            'result': self.result,
            'timestamp': self.timestamp.isoformat(),
            'duration': self.duration,
            'success': self.success,
            'error': self.error
        }

class AgentHistoryList:
    """代理历史记录列表"""
    
    def __init__(self):
        """初始化历史记录列表"""
        self.steps: List[AgentStepInfo] = []
        
    def add_step(self, step: AgentStepInfo):
        """添加步骤
        
        Args:
            step: 步骤信息
        """
        self.steps.append(step)
        
    def get_last_step(self) -> Optional[AgentStepInfo]:
        """获取最后一个步骤
        
        Returns:
            Optional[AgentStepInfo]: 最后一个步骤信息
        """
        return self.steps[-1] if self.steps else None
        
    def get_success_rate(self) -> float:
        """获取成功率
        
        Returns:
            float: 成功率
        """
        if not self.steps:
            return 0.0
        success_count = sum(1 for step in self.steps if step.success)
        return success_count / len(self.steps)
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典
        
        Returns:
            Dict[str, Any]: 字典数据
        """
        return {
            'steps': [step.to_dict() for step in self.steps],
            'success_rate': self.get_success_rate(),
            'total_steps': len(self.steps)
        } 