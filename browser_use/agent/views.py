"""Agent views module"""

import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class ActionResult:
    """动作执行结果"""

    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

    @classmethod
    def success_result(cls, message: str, data: Optional[Dict[str, Any]] = None) -> 'ActionResult':
        """创建成功结果"""
        return cls(success=True, message=message, data=data)

    @classmethod
    def error_result(cls, message: str, error: Optional[str] = None) -> 'ActionResult':
        """创建错误结果"""
        return cls(success=False, message=message, error=error)

@dataclass
class ActionModel:
    """动作模型"""

    action_type: str
    parameters: Dict[str, Any]
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

@dataclass
class AgentStepInfo:
    """代理步骤信息"""
    
    action: Dict[str, Any]
    result: Dict[str, Any]
    timestamp: datetime
    duration: float
    success: bool
    error: Optional[str] = None
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AgentStepInfo':
        """从字典创建实例
        
        Args:
            data: 字典数据
            
        Returns:
            AgentStepInfo: 步骤信息实例
        """
        return cls(
            action=data['action'],
            result=data['result'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            duration=data['duration'],
            success=data['success'],
            error=data.get('error')
        )
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典
        
        Returns:
            Dict[str, Any]: 字典数据
        """
        return {
            'action': self.action,
            'result': self.result,
            'timestamp': self.timestamp.isoformat(),
            'duration': self.duration,
            'success': self.success,
            'error': self.error
        }

@dataclass
class AgentOutput:
    """Agent输出结果 - 兼容webui"""

    action: List[Dict[str, Any]]
    current_state: Any

    def model_dump(self, exclude_none: bool = True) -> Dict[str, Any]:
        """兼容Pydantic的model_dump方法"""
        result = {
            'action': self.action,
            'current_state': self.current_state
        }
        if exclude_none:
            result = {k: v for k, v in result.items() if v is not None}
        return result

class AgentHistoryList:
    """代理历史记录列表"""

    def __init__(self):
        """初始化历史记录列表"""
        self.steps: List[AgentStepInfo] = []

    def add_step(self, step: AgentStepInfo):
        """添加步骤

        Args:
            step: 步骤信息
        """
        self.steps.append(step)

    def get_last_step(self) -> Optional[AgentStepInfo]:
        """获取最后一个步骤

        Returns:
            Optional[AgentStepInfo]: 最后一个步骤信息
        """
        return self.steps[-1] if self.steps else None

    def get_success_rate(self) -> float:
        """获取成功率

        Returns:
            float: 成功率
        """
        if not self.steps:
            return 0.0
        success_count = sum(1 for step in self.steps if step.success)
        return success_count / len(self.steps)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典

        Returns:
            Dict[str, Any]: 字典数据
        """
        return {
            'steps': [step.to_dict() for step in self.steps],
            'success_rate': self.get_success_rate(),
            'total_steps': len(self.steps)
        }

    # 兼容webui的方法
    def total_duration_seconds(self) -> float:
        """总执行时间（秒）"""
        return sum(step.duration for step in self.steps)

    def total_input_tokens(self) -> int:
        """总输入token数"""
        return sum(step.result.get('input_tokens', 0) for step in self.steps)

    def final_result(self) -> Optional[Dict[str, Any]]:
        """最终结果"""
        last_step = self.get_last_step()
        return last_step.result if last_step else None

    def errors(self) -> List[str]:
        """错误列表"""
        return [step.error for step in self.steps if step.error]