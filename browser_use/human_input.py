"""Human-like input simulation module"""

import asyncio
import logging
import random
from typing import Dict, Any, Optional, List, Tuple

logger = logging.getLogger(__name__)

class HumanLikeInput:
    """人类行为模拟类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化人类行为模拟
        
        Args:
            config: 配置信息
        """
        self.config = config or {}
        self.typing_speed = self.config.get('typing_speed', {
            'min': 50,    # 最小按键间隔（毫秒）
            'max': 150,   # 最大按键间隔（毫秒）
            'pause_min': 200,  # 最小暂停时间（毫秒）
            'pause_max': 500   # 最大暂停时间（毫秒）
        })
        self.mouse_speed = self.config.get('mouse_speed', {
            'min': 800,   # 最小移动速度（像素/秒）
            'max': 1500   # 最大移动速度（像素/秒）
        })
        self.click_timing = self.config.get('click_timing', {
            'down_min': 50,   # 最小按下时间（毫秒）
            'down_max': 150,  # 最大按下时间（毫秒）
            'up_min': 50,     # 最小释放时间（毫秒）
            'up_max': 150     # 最大释放时间（毫秒）
        })
        
    async def type_text(self, element: Any, text: str):
        """模拟人类输入文本
        
        Args:
            element: 目标元素
            text: 要输入的文本
        """
        for char in text:
            # 随机延迟
            delay = random.randint(
                self.typing_speed['min'],
                self.typing_speed['max']
            ) / 1000.0
            await asyncio.sleep(delay)
            
            # 输入字符
            await element.type(char)
            
            # 随机暂停
            if random.random() < 0.1:  # 10%概率暂停
                pause = random.randint(
                    self.typing_speed['pause_min'],
                    self.typing_speed['pause_max']
                ) / 1000.0
                await asyncio.sleep(pause)
                
    async def click_element(self, element: Any):
        """模拟人类点击元素
        
        Args:
            element: 目标元素
        """
        # 获取元素位置
        box = await element.bounding_box()
        if not box:
            raise ValueError("Element not visible")
            
        # 计算点击位置（随机偏移）
        x = box['x'] + box['width'] * random.uniform(0.2, 0.8)
        y = box['y'] + box['height'] * random.uniform(0.2, 0.8)
        
        # 模拟鼠标移动
        await self._move_mouse_to(x, y)
        
        # 模拟点击
        down_time = random.randint(
            self.click_timing['down_min'],
            self.click_timing['down_max']
        ) / 1000.0
        up_time = random.randint(
            self.click_timing['up_min'],
            self.click_timing['up_max']
        ) / 1000.0
        
        await element.mouse_down()
        await asyncio.sleep(down_time)
        await element.mouse_up()
        await asyncio.sleep(up_time)
        
    async def _move_mouse_to(self, x: float, y: float):
        """模拟鼠标移动
        
        Args:
            x: 目标X坐标
            y: 目标Y坐标
        """
        # 计算移动时间
        speed = random.randint(
            self.mouse_speed['min'],
            self.mouse_speed['max']
        )
        distance = ((x ** 2) + (y ** 2)) ** 0.5
        duration = distance / speed
        
        # 模拟移动
        await asyncio.sleep(duration) 