"""Controller service module"""

import logging
import asyncio
from typing import Dict, Any, Optional, List, Callable, Awaitable
from dataclasses import dataclass
from ..agent.service import Agent
from ..agent.views import ActionResult, ActionModel

logger = logging.getLogger(__name__)

@dataclass
class DoneAction:
    """完成动作"""

    message: str
    success: bool = True
    result: Optional[Dict[str, Any]] = None
    
    @classmethod
    def success(cls, message: str, result: Optional[Dict[str, Any]] = None) -> 'DoneAction':
        """创建成功的完成动作"""
        return cls(success=True, message=message, result=result)
    
    @classmethod
    def failure(cls, message: str) -> 'DoneAction':
        """创建失败的完成动作"""
        return cls(success=False, message=message)

class Controller:
    """控制器类 - 管理Agent的执行"""
    
    def __init__(self, 
                 agent: Optional[Agent] = None,
                 config: Optional[Dict[str, Any]] = None):
        """初始化控制器
        
        Args:
            agent: Agent实例
            config: 配置信息
        """
        self.agent = agent
        self.config = config or {}
        self.history = []
        self._running = False
        self._callbacks = {}
        
    def set_agent(self, agent: Agent):
        """设置Agent"""
        self.agent = agent
        
    def add_callback(self, event: str, callback: Callable):
        """添加回调函数"""
        if event not in self._callbacks:
            self._callbacks[event] = []
        self._callbacks[event].append(callback)
        
    async def _trigger_callback(self, event: str, data: Any):
        """触发回调函数"""
        if event in self._callbacks:
            for callback in self._callbacks[event]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(data)
                    else:
                        callback(data)
                except Exception as e:
                    logger.error(f"Callback error for event {event}: {e}")
    
    async def run(self, task: str, max_steps: Optional[int] = None) -> ActionResult:
        """运行任务
        
        Args:
            task: 任务描述
            max_steps: 最大步数
            
        Returns:
            ActionResult: 执行结果
        """
        if not self.agent:
            return ActionResult.error_result("No agent configured")
            
        self._running = True
        
        try:
            # 设置Agent任务
            self.agent.task = task
            
            # 触发开始回调
            await self._trigger_callback('start', {'task': task})
            
            # 运行Agent
            result = await self.agent.run(max_steps)
            
            # 触发完成回调
            await self._trigger_callback('complete', result)
            
            if result.get('success', False):
                return ActionResult.success_result(
                    message=result.get('message', 'Task completed'),
                    data=result
                )
            else:
                return ActionResult.error_result(
                    message=result.get('message', 'Task failed'),
                    error=result.get('error')
                )
                
        except Exception as e:
            logger.error(f"Controller execution failed: {e}")
            await self._trigger_callback('error', {'error': str(e)})
            return ActionResult.error_result(
                message="Controller execution failed",
                error=str(e)
            )
        finally:
            self._running = False
    
    async def stop(self):
        """停止执行"""
        self._running = False
        await self._trigger_callback('stop', {})
        
    def is_running(self) -> bool:
        """检查是否正在运行"""
        return self._running
        
    def get_history(self) -> List[Dict[str, Any]]:
        """获取历史记录"""
        if self.agent:
            return self.agent.history
        return self.history
