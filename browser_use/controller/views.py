"""Controller views module"""

import logging
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass
from datetime import datetime
from enum import Enum

logger = logging.getLogger(__name__)

class ActionType(Enum):
    """动作类型枚举"""
    CLICK = "click"
    TYPE = "type"
    SCROLL = "scroll"
    WAIT = "wait"
    NAVIGATE = "navigate"
    EXTRACT = "extract"
    DONE = "done"

@dataclass
class ActionModel:
    """动作模型"""
    
    action: str
    coordinate: Optional[List[int]] = None
    text: Optional[str] = None
    url: Optional[str] = None
    reasoning: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {"action": self.action}
        if self.coordinate is not None:
            result["coordinate"] = self.coordinate
        if self.text is not None:
            result["text"] = self.text
        if self.url is not None:
            result["url"] = self.url
        if self.reasoning is not None:
            result["reasoning"] = self.reasoning
        return result

@dataclass
class ActionResult:
    """动作执行结果"""
    
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    screenshot: Optional[str] = None
    
    @classmethod
    def success_result(cls, message: str, data: Optional[Dict[str, Any]] = None, screenshot: Optional[str] = None) -> 'ActionResult':
        """创建成功结果"""
        return cls(success=True, message=message, data=data, screenshot=screenshot)
    
    @classmethod
    def error_result(cls, message: str, error: Optional[str] = None) -> 'ActionResult':
        """创建错误结果"""
        return cls(success=False, message=message, error=error)

@dataclass
class ControllerState:
    """控制器状态"""
    
    current_url: str = ""
    page_title: str = ""
    is_running: bool = False
    current_step: int = 0
    max_steps: int = 50
    last_action: Optional[ActionModel] = None
    last_result: Optional[ActionResult] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "current_url": self.current_url,
            "page_title": self.page_title,
            "is_running": self.is_running,
            "current_step": self.current_step,
            "max_steps": self.max_steps,
            "last_action": self.last_action.to_dict() if self.last_action else None,
            "last_result": {
                "success": self.last_result.success,
                "message": self.last_result.message,
                "data": self.last_result.data,
                "error": self.last_result.error
            } if self.last_result else None
        }

@dataclass
class BrowserState:
    """浏览器状态"""
    
    url: str = ""
    title: str = ""
    screenshot: Optional[str] = None
    elements: List[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.elements is None:
            self.elements = []
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "url": self.url,
            "title": self.title,
            "screenshot": self.screenshot,
            "elements": self.elements
        }

@dataclass
class HistoryEntry:
    """历史记录条目"""
    
    step: int
    action: ActionModel
    result: ActionResult
    state: BrowserState
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "step": self.step,
            "action": self.action.to_dict(),
            "result": {
                "success": self.result.success,
                "message": self.result.message,
                "data": self.result.data,
                "error": self.result.error
            },
            "state": self.state.to_dict(),
            "timestamp": self.timestamp.isoformat()
        }

class ControllerHistory:
    """控制器历史记录"""
    
    def __init__(self):
        """初始化历史记录"""
        self.entries: List[HistoryEntry] = []
    
    def add_entry(self, entry: HistoryEntry):
        """添加历史记录条目"""
        self.entries.append(entry)
    
    def get_last_entry(self) -> Optional[HistoryEntry]:
        """获取最后一个条目"""
        return self.entries[-1] if self.entries else None
    
    def get_entries(self, limit: Optional[int] = None) -> List[HistoryEntry]:
        """获取历史记录条目"""
        if limit:
            return self.entries[-limit:]
        return self.entries
    
    def clear(self):
        """清空历史记录"""
        self.entries.clear()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "entries": [entry.to_dict() for entry in self.entries],
            "total_entries": len(self.entries)
        }

@dataclass
class SystemInfo:
    """系统信息"""

    browser_version: str = ""
    platform: str = ""
    screen_resolution: str = ""
    user_agent: str = ""

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "browser_version": self.browser_version,
            "platform": self.platform,
            "screen_resolution": self.screen_resolution,
            "user_agent": self.user_agent
        }

# 具体的Action类
@dataclass
class ClickElementAction(ActionModel):
    """点击元素动作"""

    def __init__(self, coordinate: List[int], reasoning: str = ""):
        super().__init__(action="click", coordinate=coordinate, reasoning=reasoning)

@dataclass
class TypeAction(ActionModel):
    """输入文本动作"""

    def __init__(self, text: str, coordinate: Optional[List[int]] = None, reasoning: str = ""):
        super().__init__(action="type", text=text, coordinate=coordinate, reasoning=reasoning)

@dataclass
class ScrollAction(ActionModel):
    """滚动动作"""

    def __init__(self, coordinate: List[int], reasoning: str = ""):
        super().__init__(action="scroll", coordinate=coordinate, reasoning=reasoning)

@dataclass
class WaitAction(ActionModel):
    """等待动作"""

    def __init__(self, reasoning: str = ""):
        super().__init__(action="wait", reasoning=reasoning)

@dataclass
class GoToUrlAction(ActionModel):
    """导航到URL动作"""

    def __init__(self, url: str, reasoning: str = ""):
        super().__init__(action="navigate", url=url, reasoning=reasoning)

@dataclass
class ExtractAction(ActionModel):
    """提取信息动作"""

    def __init__(self, reasoning: str = ""):
        super().__init__(action="extract", reasoning=reasoning)

@dataclass
class DoneAction(ActionModel):
    """完成动作"""

    def __init__(self, text: str = "", reasoning: str = ""):
        super().__init__(action="done", text=text, reasoning=reasoning)

@dataclass
class ExtractPageContentAction(ActionModel):
    """提取页面内容动作"""

    def __init__(self, reasoning: str = ""):
        super().__init__(action="extract_content", reasoning=reasoning)

@dataclass
class InputTextAction(ActionModel):
    """输入文本动作"""

    def __init__(self, text: str, coordinate: Optional[List[int]] = None, reasoning: str = ""):
        super().__init__(action="input_text", text=text, coordinate=coordinate, reasoning=reasoning)

@dataclass
class OpenTabAction(ActionModel):
    """打开新标签页动作"""

    def __init__(self, url: str, reasoning: str = ""):
        super().__init__(action="open_tab", url=url, reasoning=reasoning)

@dataclass
class SearchGoogleAction(ActionModel):
    """Google搜索动作"""

    def __init__(self, text: str, reasoning: str = ""):
        super().__init__(action="search_google", text=text, reasoning=reasoning)

@dataclass
class SendKeysAction(ActionModel):
    """发送按键动作"""

    def __init__(self, text: str, coordinate: Optional[List[int]] = None, reasoning: str = ""):
        super().__init__(action="send_keys", text=text, coordinate=coordinate, reasoning=reasoning)

@dataclass
class SwitchTabAction(ActionModel):
    """切换标签页动作"""

    def __init__(self, reasoning: str = ""):
        super().__init__(action="switch_tab", reasoning=reasoning)
