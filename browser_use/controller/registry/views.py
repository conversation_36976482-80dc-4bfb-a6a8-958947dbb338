"""Registry views module"""

import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime

logger = logging.getLogger(__name__)

# 重新导出ActionModel，保持兼容性
from ..views import ActionModel, ActionResult

@dataclass
class RegistryActionModel(ActionModel):
    """注册表动作模型"""
    
    registry_id: Optional[str] = None
    category: Optional[str] = None
    
    def __post_init__(self):
        super().__post_init__()
        if self.registry_id is None:
            self.registry_id = f"action_{self.timestamp.strftime('%Y%m%d_%H%M%S')}"

@dataclass
class RegistryActionResult(ActionResult):
    """注册表动作结果"""
    
    registry_id: Optional[str] = None
    execution_time: Optional[float] = None
    
    @classmethod
    def from_action_result(cls, result: ActionResult, registry_id: str = None, execution_time: float = None):
        """从ActionResult创建RegistryActionResult"""
        return cls(
            success=result.success,
            message=result.message,
            data=result.data,
            error=result.error,
            screenshot=result.screenshot,
            registry_id=registry_id,
            execution_time=execution_time
        )

# 为了兼容性，导出所有必要的类
__all__ = [
    'ActionModel',
    'ActionResult', 
    'RegistryActionModel',
    'RegistryActionResult'
]
