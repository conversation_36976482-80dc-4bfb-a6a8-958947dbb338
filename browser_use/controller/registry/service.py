"""Registry service module"""

import logging
import asyncio
from typing import Dict, Any, Optional, List, Callable, Awaitable
from dataclasses import dataclass
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class RegisteredAction:
    """注册的动作"""
    
    name: str
    description: str
    handler: Callable
    parameters: Dict[str, Any] = None
    category: str = "general"
    
    def __post_init__(self):
        if self.parameters is None:
            self.parameters = {}

class Registry:
    """动作注册表"""
    
    def __init__(self):
        """初始化注册表"""
        self._actions: Dict[str, RegisteredAction] = {}
        self._categories: Dict[str, List[str]] = {}
        
    def register(self, 
                 name: str, 
                 handler: Callable,
                 description: str = "",
                 parameters: Optional[Dict[str, Any]] = None,
                 category: str = "general") -> RegisteredAction:
        """注册动作
        
        Args:
            name: 动作名称
            handler: 处理函数
            description: 描述
            parameters: 参数定义
            category: 分类
            
        Returns:
            RegisteredAction: 注册的动作
        """
        action = RegisteredAction(
            name=name,
            description=description,
            handler=handler,
            parameters=parameters or {},
            category=category
        )
        
        self._actions[name] = action
        
        # 更新分类
        if category not in self._categories:
            self._categories[category] = []
        if name not in self._categories[category]:
            self._categories[category].append(name)
            
        logger.info(f"Registered action: {name} in category: {category}")
        return action
    
    def unregister(self, name: str) -> bool:
        """注销动作
        
        Args:
            name: 动作名称
            
        Returns:
            bool: 是否成功注销
        """
        if name in self._actions:
            action = self._actions[name]
            del self._actions[name]
            
            # 从分类中移除
            if action.category in self._categories:
                if name in self._categories[action.category]:
                    self._categories[action.category].remove(name)
                    
            logger.info(f"Unregistered action: {name}")
            return True
        return False
    
    def get_action(self, name: str) -> Optional[RegisteredAction]:
        """获取动作
        
        Args:
            name: 动作名称
            
        Returns:
            Optional[RegisteredAction]: 动作实例
        """
        return self._actions.get(name)
    
    def list_actions(self, category: Optional[str] = None) -> List[RegisteredAction]:
        """列出动作
        
        Args:
            category: 分类过滤
            
        Returns:
            List[RegisteredAction]: 动作列表
        """
        if category:
            action_names = self._categories.get(category, [])
            return [self._actions[name] for name in action_names if name in self._actions]
        else:
            return list(self._actions.values())
    
    def list_categories(self) -> List[str]:
        """列出所有分类

        Returns:
            List[str]: 分类列表
        """
        return list(self._categories.keys())

    def action(self, name: str = None, description: str = "", category: str = "general"):
        """动作装饰器

        Args:
            name: 动作名称
            description: 描述
            category: 分类

        Returns:
            装饰器函数
        """
        def decorator(func: Callable):
            action_name = name or func.__name__
            self.register(
                name=action_name,
                handler=func,
                description=description,
                category=category
            )
            return func
        return decorator
    
    async def execute_action(self, name: str, **kwargs) -> Any:
        """执行动作
        
        Args:
            name: 动作名称
            **kwargs: 参数
            
        Returns:
            Any: 执行结果
        """
        action = self.get_action(name)
        if not action:
            raise ValueError(f"Action not found: {name}")
            
        try:
            if asyncio.iscoroutinefunction(action.handler):
                return await action.handler(**kwargs)
            else:
                return action.handler(**kwargs)
        except Exception as e:
            logger.error(f"Action execution failed: {name}, error: {e}")
            raise

# 全局注册表实例
global_registry = Registry()
