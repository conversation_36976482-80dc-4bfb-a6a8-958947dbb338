"""
🎯 自定义Radio按钮处理引擎
专门处理SVG自定义radio按钮和复杂结构的点击问题
解决类似 Nikkei Research 调查网站的特殊radio按钮无法点击的问题
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from browser_use.browser.context import BrowserContext

logger = logging.getLogger(__name__)


class CustomRadioButtonHandler:
    """🎯 自定义Radio按钮处理器"""
    
    def __init__(self):
        self.detection_cache = {}
        self.click_strategies = [
            ("label_click", "点击label标签"),
            ("container_click", "点击容器元素"),
            ("svg_icon_click", "点击SVG图标"),
            ("javascript_trigger", "JavaScript触发"),
            ("input_element_click", "直接点击input元素"),
            ("force_selection", "强制选择")
        ]
    
    async def handle_radio_click(self, page, dom_element, index: int, element_text: str) -> Dict:
        """🎯 处理Radio按钮点击的主入口"""
        try:
            logger.info(f"🎯 开始处理Radio按钮点击: '{element_text}' (索引: {index})")
            
            # 检测是否是自定义Radio按钮页面
            is_custom_radio_page = await self._detect_custom_radio_page(page)
            
            if is_custom_radio_page:
                logger.info("🔍 检测到自定义Radio按钮页面，使用增强点击策略")
                
                # 使用多策略点击方案
                click_result = await self._execute_multi_strategy_radio_click(
                    page, dom_element, index, element_text
                )
                
                if click_result["success"]:
                    # 验证点击效果
                    verification_result = await self._verify_radio_selection(
                        page, element_text, dom_element
                    )
                    
                    if verification_result["success"]:
                        return {
                            "success": True,
                            "message": f"✅ 自定义Radio按钮点击成功: {element_text}",
                            "strategy": click_result.get("strategy"),
                            "verification": verification_result.get("method")
                        }
                    else:
                        logger.warning(f"⚠️ 点击成功但验证失败，尝试备用方案")
                        # 尝试备用点击方案
                        backup_result = await self._execute_backup_radio_click(
                            page, dom_element, element_text
                        )
                        if backup_result["success"]:
                            return {
                                "success": True,
                                "message": f"✅ 备用方案点击成功: {element_text}",
                                "strategy": "backup_force_click"
                            }
                
                return {
                    "success": False,
                    "error": f"❌ 所有点击策略都失败: {element_text}"
                }
            else:
                # 标准点击逻辑
                logger.info("🔄 使用标准点击逻辑")
                try:
                    xpath = '//' + dom_element.xpath
                    element_locator = page.locator(xpath)
                    await element_locator.click()
                    
                    return {
                        "success": True,
                        "message": f"✅ 标准点击成功: {element_text}",
                        "strategy": "standard_click"
                    }
                except Exception as e:
                    return {
                        "success": False,
                        "error": f"标准点击失败: {e}"
                    }
                
        except Exception as e:
            logger.error(f"❌ Radio按钮点击处理失败: {e}")
            return {
                "success": False,
                "error": f"Radio按钮点击处理失败: {e}"
            }

    async def _detect_custom_radio_page(self, page) -> bool:
        """🔍 检测是否是自定义Radio按钮页面"""
        try:
            # 获取页面URL作为缓存键
            page_url = page.url
            cache_key = f"custom_radio_detection_{hash(page_url)}"
            
            # 检查缓存
            if cache_key in self.detection_cache:
                return self.detection_cache[cache_key]
            
            # 检测特征标识符 - 针对Nikkei Research等网站的特殊结构
            custom_radio_selectors = [
                'span.fir-icon',  # 从HTML看到的SVG图标
                'svg[viewBox="-1 -1 22 22"]',  # 特定的SVG viewBox
                'input.fir-hidden',  # 隐藏的input元素
                '.element.clickableCell',  # 可点击的单元格
                'path.fir-selected',  # 选中状态的path
                '.survey-error',  # 调查错误提示
                'input[type="radio"].fir-hidden'  # 隐藏的radio按钮
            ]
            
            detection_results = []
            for selector in custom_radio_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    detection_results.append(len(elements) > 0)
                    if len(elements) > 0:
                        logger.info(f"🔍 检测到自定义Radio特征: {selector} (数量: {len(elements)})")
                except:
                    detection_results.append(False)
            
            # 如果有2个以上特征匹配，认为是自定义Radio页面
            is_custom_radio = sum(detection_results) >= 2
            
            # 缓存结果
            self.detection_cache[cache_key] = is_custom_radio
            
            logger.info(f"🔍 自定义Radio页面检测结果: {is_custom_radio} (匹配特征: {sum(detection_results)}/7)")
            return is_custom_radio
            
        except Exception as e:
            logger.error(f"❌ 自定义Radio页面检测失败: {e}")
            return False

    async def _execute_multi_strategy_radio_click(self, page, dom_element, index: int, element_text: str) -> Dict:
        """🔥 执行多策略Radio按钮点击"""
        try:
            for strategy_name, strategy_desc in self.click_strategies:
                logger.info(f"🎯 尝试策略: {strategy_desc}")
                
                try:
                    success = False
                    
                    if strategy_name == "label_click":
                        success = await self._click_radio_label(page, element_text)
                    elif strategy_name == "container_click":
                        success = await self._click_radio_container(page, dom_element)
                    elif strategy_name == "svg_icon_click":
                        success = await self._click_svg_icon(page, dom_element)
                    elif strategy_name == "javascript_trigger":
                        success = await self._javascript_radio_trigger(page, element_text)
                    elif strategy_name == "input_element_click":
                        success = await self._click_hidden_input(page, element_text)
                    elif strategy_name == "force_selection":
                        success = await self._force_radio_selection(page, element_text)
                    
                    if success:
                        logger.info(f"✅ 策略成功: {strategy_desc}")
                        return {"success": True, "strategy": strategy_name}
                    else:
                        logger.warning(f"⚠️ 策略失败: {strategy_desc}")
                        
                except Exception as e:
                    logger.warning(f"⚠️ 策略异常 {strategy_desc}: {e}")
                    continue
            
            return {"success": False, "error": "所有策略都失败"}
            
        except Exception as e:
            logger.error(f"❌ 多策略点击执行失败: {e}")
            return {"success": False, "error": str(e)}

    async def _click_radio_label(self, page, element_text: str) -> bool:
        """🎯 策略1: 点击label标签"""
        try:
            # 查找包含文本的label - 针对Nikkei Research的结构
            label_selectors = [
                f'label:has-text("{element_text}")',
                f'span.cell-text:has-text("{element_text}")',
                f'.cell-sub-column:has-text("{element_text}")',
                f'label[for]:has-text("{element_text}")',
                f'.element:has-text("{element_text}") label'
            ]
            
            for selector in label_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        await elements[0].click()
                        await asyncio.sleep(0.5)  # 等待响应
                        return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Label点击策略失败: {e}")
            return False

    async def _click_radio_container(self, page, dom_element) -> bool:
        """🎯 策略2: 点击容器元素"""
        try:
            xpath = '//' + dom_element.xpath
            
            # 查找父容器 - 针对Nikkei Research的结构
            container_xpaths = [
                xpath + "/ancestor::div[contains(@class, 'element')]",
                xpath + "/ancestor::div[contains(@class, 'clickableCell')]",
                xpath + "/ancestor::span[contains(@class, 'cell-sub-wrapper')]",
                xpath + "/parent::*",
                xpath
            ]
            
            for container_xpath in container_xpaths:
                try:
                    element = page.locator(container_xpath)
                    if await element.count() > 0:
                        await element.first.click()
                        await asyncio.sleep(0.5)
                        return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"❌ 容器点击策略失败: {e}")
            return False

    async def _click_svg_icon(self, page, dom_element) -> bool:
        """🎯 策略3: 点击SVG图标"""
        try:
            xpath = '//' + dom_element.xpath
            
            # 查找SVG图标 - 针对Nikkei Research的fir-icon结构
            svg_selectors = [
                xpath + "//span[contains(@class, 'fir-icon')]",
                xpath + "//svg",
                xpath + "//*[name()='svg']",
                xpath + "/preceding-sibling::span[contains(@class, 'fir-icon')]",
                xpath + "/following-sibling::span[contains(@class, 'fir-icon')]"
            ]
            
            for svg_selector in svg_selectors:
                try:
                    element = page.locator(svg_selector)
                    if await element.count() > 0:
                        await element.first.click()
                        await asyncio.sleep(0.5)
                        return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"❌ SVG图标点击策略失败: {e}")
            return False

    async def _javascript_radio_trigger(self, page, element_text: str) -> bool:
        """🎯 策略4: JavaScript触发选择"""
        try:
            # 使用JavaScript直接触发radio按钮选择
            js_script = f"""
            () => {{
                // 清理文本进行匹配
                const cleanText = '{element_text}'.trim();
                
                // 查找包含文本的元素
                const elements = Array.from(document.querySelectorAll('label, span, div'));
                const targetElement = elements.find(el => el.textContent && el.textContent.trim() === cleanText);
                
                if (targetElement) {{
                    console.log('找到目标元素:', targetElement);
                    
                    // 查找关联的input
                    let input = null;
                    
                    // 方法1: 通过for属性查找
                    if (targetElement.tagName === 'LABEL' && targetElement.getAttribute('for')) {{
                        const inputId = targetElement.getAttribute('for');
                        input = document.getElementById(inputId);
                    }}
                    
                    // 方法2: 在元素内部查找
                    if (!input) {{
                        input = targetElement.querySelector('input[type="radio"]');
                    }}
                    
                    // 方法3: 在父容器中查找
                    if (!input) {{
                        const container = targetElement.closest('.element, .clickableCell');
                        if (container) {{
                            input = container.querySelector('input[type="radio"]');
                        }}
                    }}
                    
                    if (input) {{
                        console.log('找到input元素:', input);
                        
                        // 设置选中状态
                        input.checked = true;
                        
                        // 触发各种事件
                        const events = ['change', 'click', 'input'];
                        events.forEach(eventType => {{
                            input.dispatchEvent(new Event(eventType, {{ bubbles: true }}));
                        }});
                        
                        return true;
                    }}
                    
                    // 如果没找到input，直接点击元素
                    try {{
                        targetElement.click();
                        return true;
                    }} catch (e) {{
                        console.log('点击元素失败:', e);
                    }}
                }}
                
                return false;
            }}
            """
            
            result = await page.evaluate(js_script)
            return bool(result)
            
        except Exception as e:
            logger.error(f"❌ JavaScript触发策略失败: {e}")
            return False

    async def _click_hidden_input(self, page, element_text: str) -> bool:
        """🎯 策略5: 直接点击隐藏的input元素"""
        try:
            js_script = f"""
            () => {{
                const cleanText = '{element_text}'.trim();
                
                // 查找所有label元素
                const labels = Array.from(document.querySelectorAll('label'));
                const targetLabel = labels.find(label => label.textContent && label.textContent.trim() === cleanText);
                
                if (targetLabel && targetLabel.getAttribute('for')) {{
                    const inputId = targetLabel.getAttribute('for');
                    const input = document.getElementById(inputId);
                    
                    if (input) {{
                        console.log('处理隐藏的input:', input);
                        
                        // 临时显示元素
                        const originalDisplay = input.style.display;
                        const originalVisibility = input.style.visibility;
                        const originalOpacity = input.style.opacity;
                        
                        input.style.display = 'block';
                        input.style.visibility = 'visible';
                        input.style.opacity = '1';
                        input.style.position = 'static';
                        
                        // 设置选中状态
                        input.checked = true;
                        
                        // 触发事件
                        input.dispatchEvent(new Event('change', {{ bubbles: true }}));
                        input.dispatchEvent(new Event('click', {{ bubbles: true }}));
                        input.dispatchEvent(new Event('input', {{ bubbles: true }}));
                        
                        // 恢复原始样式
                        setTimeout(() => {{
                            input.style.display = originalDisplay;
                            input.style.visibility = originalVisibility;
                            input.style.opacity = originalOpacity;
                        }}, 100);
                        
                        return true;
                    }}
                }}
                
                return false;
            }}
            """
            
            result = await page.evaluate(js_script)
            return bool(result)
            
        except Exception as e:
            logger.error(f"❌ 隐藏input点击策略失败: {e}")
            return False

    async def _force_radio_selection(self, page, element_text: str) -> bool:
        """🎯 策略6: 强制Radio选择 - 最后的备用方案"""
        try:
            js_script = f"""
            () => {{
                const cleanText = '{element_text}'.trim();
                console.log('强制选择策略，目标文本:', cleanText);
                
                // 更广泛的元素搜索
                const allElements = Array.from(document.querySelectorAll('*'));
                let targetElements = [];
                
                // 收集所有包含目标文本的元素
                for (let element of allElements) {{
                    if (element.textContent && element.textContent.trim() === cleanText) {{
                        targetElements.push(element);
                    }}
                }}
                
                console.log('找到包含目标文本的元素数量:', targetElements.length);
                
                for (let targetElement of targetElements) {{
                    console.log('处理元素:', targetElement);
                    
                    // 尝试多层级点击
                    const elementsToTry = [
                        targetElement,
                        targetElement.parentElement,
                        targetElement.parentElement?.parentElement,
                        targetElement.closest('.element'),
                        targetElement.closest('.clickableCell')
                    ].filter(Boolean);
                    
                    for (let el of elementsToTry) {{
                        try {{
                            // 模拟鼠标事件
                            const events = ['mousedown', 'mouseup', 'click'];
                            events.forEach(eventType => {{
                                el.dispatchEvent(new MouseEvent(eventType, {{
                                    bubbles: true,
                                    cancelable: true,
                                    view: window
                                }}));
                            }});
                            
                            // 如果是有对应radio的元素，确保radio被选中
                            const radio = el.querySelector('input[type="radio"]') || 
                                         el.closest('.element')?.querySelector('input[type="radio"]');
                            if (radio) {{
                                radio.checked = true;
                                radio.dispatchEvent(new Event('change', {{ bubbles: true }}));
                            }}
                            
                            console.log('强制点击成功:', el);
                            return true;
                            
                        }} catch (e) {{
                            console.log('强制点击失败:', e);
                            continue;
                        }}
                    }}
                }}
                
                return false;
            }}
            """
            
            result = await page.evaluate(js_script)
            return bool(result)
            
        except Exception as e:
            logger.error(f"❌ 强制选择策略失败: {e}")
            return False

    async def _verify_radio_selection(self, page, element_text: str, dom_element) -> Dict:
        """🔍 验证Radio按钮选择结果"""
        try:
            # 等待页面响应
            await asyncio.sleep(1)
            
            # 检查选中状态的多种方法
            verification_methods = [
                ("checked_attribute", self._verify_by_checked_attribute(page, element_text)),
                ("visual_indicator", self._verify_by_visual_indicator(page, element_text)),
                ("class_change", self._verify_by_class_change(page, dom_element)),
                ("form_data", self._verify_by_form_data(page, element_text))
            ]
            
            for method_name, verify_method in verification_methods:
                try:
                    result = await verify_method
                    if result:
                        logger.info(f"✅ 验证成功 - 方法: {method_name}")
                        return {"success": True, "method": method_name}
                except Exception as e:
                    logger.warning(f"⚠️ 验证方法 {method_name} 失败: {e}")
                    continue
            
            return {"success": False, "error": "所有验证方法都失败"}
            
        except Exception as e:
            logger.error(f"❌ Radio选择验证失败: {e}")
            return {"success": False, "error": str(e)}

    async def _verify_by_checked_attribute(self, page, element_text: str) -> bool:
        """通过checked属性验证"""
        try:
            js_script = f"""
            () => {{
                const cleanText = '{element_text}'.trim();
                const labels = Array.from(document.querySelectorAll('label'));
                const targetLabel = labels.find(label => label.textContent && label.textContent.trim() === cleanText);
                
                if (targetLabel && targetLabel.getAttribute('for')) {{
                    const inputId = targetLabel.getAttribute('for');
                    const input = document.getElementById(inputId);
                    return input && input.checked;
                }}
                
                return false;
            }}
            """
            
            result = await page.evaluate(js_script)
            return bool(result)
            
        except:
            return False

    async def _verify_by_visual_indicator(self, page, element_text: str) -> bool:
        """通过视觉指示器验证"""
        try:
            # 查找选中状态的视觉指示器
            selectors = [
                '.fir-selected',
                '.selected',
                '[aria-checked="true"]',
                '.checked',
                '.active'
            ]
            
            for selector in selectors:
                elements = await page.query_selector_all(selector)
                for element in elements:
                    try:
                        text = await element.text_content()
                        if element_text in text:
                            return True
                    except:
                        continue
            
            return False
            
        except:
            return False

    async def _verify_by_class_change(self, page, dom_element) -> bool:
        """通过类名变化验证"""
        try:
            xpath = '//' + dom_element.xpath
            element = page.locator(xpath)
            
            if await element.count() > 0:
                class_list = await element.first.get_attribute('class') or ''
                # 检查是否有选中相关的类名
                selected_indicators = ['selected', 'checked', 'active', 'chosen']
                return any(indicator in class_list.lower() for indicator in selected_indicators)
            
            return False
            
        except:
            return False

    async def _verify_by_form_data(self, page, element_text: str) -> bool:
        """通过表单数据验证"""
        try:
            js_script = """
            () => {
                const form = document.querySelector('form');
                if (form) {
                    const formData = new FormData(form);
                    const radioInputs = form.querySelectorAll('input[type="radio"]:checked');
                    return radioInputs.length > 0;
                }
                return false;
            }
            """
            
            result = await page.evaluate(js_script)
            return bool(result)
            
        except:
            return False

    async def _execute_backup_radio_click(self, page, dom_element, element_text: str) -> Dict:
        """🔄 执行备用Radio按钮点击方案"""
        try:
            logger.info(f"🔄 执行备用点击方案: {element_text}")
            
            # 备用方案：最暴力的JavaScript选择
            js_script = f"""
            () => {{
                const cleanText = '{element_text}'.trim();
                console.log('备用方案 - 处理文本:', cleanText);
                
                // 查找所有可能的元素
                const selectors = [
                    'label', 'span', 'div', 'button', 'input', 
                    '.element', '.clickableCell', '.cell-text', '.cell-sub-column'
                ];
                
                let found = false;
                
                for (let selector of selectors) {{
                    const elements = document.querySelectorAll(selector);
                    
                    for (let element of elements) {{
                        if (element.textContent && element.textContent.trim() === cleanText) {{
                            console.log('备用方案找到元素:', element);
                            
                            // 尝试多种激活方式
                            try {{
                                // 1. 直接点击
                                element.click();
                                
                                // 2. 模拟鼠标事件序列
                                ['mouseenter', 'mouseover', 'mousedown', 'mouseup', 'click'].forEach(eventType => {{
                                    element.dispatchEvent(new MouseEvent(eventType, {{
                                        bubbles: true,
                                        cancelable: true,
                                        view: window
                                    }}));
                                }});
                                
                                // 3. 如果有关联的radio，强制设置
                                const container = element.closest('.element, .clickableCell, form');
                                if (container) {{
                                    const radio = container.querySelector('input[type="radio"]');
                                    if (radio) {{
                                        radio.checked = true;
                                        radio.dispatchEvent(new Event('change', {{ bubbles: true }}));
                                        radio.dispatchEvent(new Event('click', {{ bubbles: true }}));
                                    }}
                                }}
                                
                                found = true;
                                break;
                                
                            }} catch (e) {{
                                console.log('备用方案点击失败:', e);
                                continue;
                            }}
                        }}
                    }}
                    
                    if (found) break;
                }}
                
                return found;
            }}
            """
            
            result = await page.evaluate(js_script)
            
            return {"success": bool(result)}
            
        except Exception as e:
            logger.error(f"❌ 备用点击方案失败: {e}")
            return {"success": False, "error": str(e)}


# 全局处理器实例
custom_radio_handler = CustomRadioButtonHandler() 